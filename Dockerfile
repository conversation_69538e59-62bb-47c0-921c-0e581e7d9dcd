FROM registry.duopu.cn/zyws/docker/node:18-alpine

LABEL author="<EMAIL>"

ENV PORT=7009 NODE_ENV=production

WORKDIR /app
COPY app.js package.json server.js version /app/
COPY package-lock.json /app/
COPY ./app/ app/
COPY ./config/ config/
COPY ./lib/ lib/
COPY ./backstage/ backstage/

RUN rm -rf /app/app/public/plugins/ueditor/jsp/lib

#RUN npm config set proxy http://192.168.0.3:7890
#RUN npm config set https-proxy http://192.168.0.3:7890
#RUN npm  install
RUN npm install --registry=https://registry.npmmirror.com
RUN npm config delete proxy
RUN npm config delete https-proxy

EXPOSE ${PORT}


ENTRYPOINT [ "npm", "run" ]
CMD [ "dev" ]
