// const CryptoJS = require('crypto-js');
// const fs = require('fs');
// const path = require('path');
// const JSZip = require('jszip');
// const mongoose = require('mongoose');
// const shortid = require('shortid');
// const encryptionKey = 'chiscdc@bhkdownload#$%^';
// // 连接数据库  mongodb://dbadmin:<EMAIL>:25000/script?authSource=admin
// // const url = 'mongodb://127.0.0.1:27017/fujian?authSource=admin';
// mongoose.connect('mongodb://127.0.0.1:27017/fujian?authSource=admin', {
//   useNewUrlParser: true,
//   useUnifiedTopology: true,
// });
// const db = mongoose.connection;
// db.on('error', console.error.bind(console, 'connection error:'));
// db.once('open', function() {
//   console.log('连接成功');
// });

// // 定义数据模型
// const bhkSchema = new mongoose.Schema(
//   {
//     name: String,
//   },
//   {
//     strict: false, // 允许存储未在 schema 中定义的字段
//     timestamps: true, // 自动管理 createdAt 和 updatedAt
//     versionKey: false, // 不使用 __v 字段
//   }
// );
// const bhkModel = mongoose.model('fzzhenduan', bhkSchema);

// // 定义 fzbhkskip 数据模型
// const fzbhkskipSchema = new mongoose.Schema({
//   _id: {
//     type: String,
//     default: shortid.generate,
//   },
//   // 文件信息
//   filePath: {
//     type: String,
//     required: true,
//     index: true, // 添加索引便于按文件路径查询
//   },
//   fileName: {
//     type: String,
//     required: true,
//   },
//   fileSize: {
//     type: Number,
//     default: 0,
//   },
//   // 解密后的原始数据
//   sourceData: {
//     type: mongoose.Schema.Types.Mixed,
//     required: true,
//   },
//   // 数据类型信息
//   dataType: {
//     type: String,
//     default: '00', // type === '00'
//     index: true,
//   },
//   // 体检数据统计
//   bhkListCount: {
//     type: Number,
//     default: 0,
//   },
//   // 处理状态
//   processStatus: {
//     type: String,
//     enum: [ 'pending', 'processed', 'error' ],
//     default: 'pending',
//     index: true,
//   },
//   // 备注信息
//   remark: {
//     type: String,
//     default: '',
//   },
//   // 处理时间
//   processTime: {
//     type: Date,
//     default: Date.now,
//   },
// }, {
//   timestamps: true, // 自动添加 createdAt 和 updatedAt
//   versionKey: false, // 不需要 __v 字段
//   collection: 'fzzhenduanskips', // 指定集合名称
// });

// // 添加索引
// fzbhkskipSchema.index({ filePath: 1, createdAt: -1 });
// fzbhkskipSchema.index({ processStatus: 1, createdAt: -1 });
// fzbhkskipSchema.index({ dataType: 1, bhkListCount: 1 });

// const fzbhkskipModel = mongoose.model('fzzhenduanskips', fzbhkskipSchema);
// console.log('开始执行');
// // // 查询数据库
// // bhkModel.find({}).then((res) => {
// //   console.log('查询的文档数量为: ' + res.length);
// //   console.log(res, 'bhkList');
// // }).catch((err) => {
// //   throw err;
// // });

// class EncryptUtil {
//   /**  处理危害因素 3.8
//    * @param {String} value - 危害因素值 190902
//    * @return {String} - 返回危害因素  有机锡
//    */
//   static getHarmFactors(value) {
//     // value转换为数字
//     value = +value;
//     const codeObj = {
//       11: '粉尘',
//       110001: '矽尘',
//       110002: '煤尘（游离 SiO2 含量＜10%）',
//       110003: '石棉（石棉含量＞10%）粉尘纤维',
//       110005: '棉尘',
//       110007: '电焊烟尘',
//       110008: '硬质合金粉尘',
//       110010: '沸石粉尘',
//       110011: '滑石粉尘（游离 SiO2 含量＜10%）',
//       110012: '石墨粉尘',
//       110013: '炭黑粉尘',
//       110014: '水泥粉尘（游离 SiO2 含量<10 %）',
//       110015: '云母粉尘',
//       110016: '陶土粉尘',
//       110017: '铝尘（铝金属、铝合金粉尘氧化铝粉尘）',
//       110018: '铸造粉尘',
//       110100: '白云石粉尘',
//       110101: '玻璃钢粉尘',
//       110102: '茶尘',
//       110103: '沉淀 SiO2（白炭黑）',
//       110104: '大理石粉尘 （碳酸钙）',
//       110105: '二氧化钛粉尘',
//       110106: '酚醛树酯粉尘',
//       110107: '谷物粉尘（游离 SiO2 含量＜10%）',
//       110108: '硅灰石粉尘',
//       110109: '硅藻土粉尘（游离 SiO2 含量＜10%）',
//       110110: '活性炭粉尘',
//       110111: '聚丙烯粉尘',
//       110112: '聚丙烯腈纤维粉尘',
//       110113: '聚氯乙烯粉尘',
//       110114: '聚乙烯粉尘',
//       110115: '麻尘（亚麻、黄麻、芒麻）',
//       110116: '木粉尘（硬）',
//       110117: '凝聚 SiO2 粉尘',
//       110118: '膨润土粉尘',
//       110119: '皮毛粉尘',
//       110120: '人造矿物纤维绝热棉粉尘（玻璃棉、矿渣棉、岩棉）',
//       110121: '桑蚕丝尘',
//       110122: '砂轮磨尘',
//       110123: '石膏粉尘（硫酸钙）',
//       110124: '石灰石粉尘',
//       110125: '碳化硅粉尘',
//       110126: '碳纤维粉尘',
//       110127: '稀土粉尘（游离 SiO2 含量<10 %）',
//       110128: '洗衣粉混合尘',
//       110129: '烟草尘',
//       110130: '萤石混合性粉尘',
//       110131: '珍珠岩粉尘',
//       110132: '蛭石粉尘',
//       110133: '重晶石粉尘（硫酸钡）',
//       110134: '工业酶混合尘',
//       110135: '过氯酸铵粉尘',
//       110136: '锑及其化合物粉尘',
//       110137: '铁及其化合物粉尘',
//       110140: '锡及其化合物粉尘',
//       110999: '其他粉尘',
//       12: '化学有害因素',
//       120001: '四乙基铅（按 Pb 计）',
//       120002: '汞-有机汞化合物（按 Hg 计）',
//       120003: '锰及其无机化合物（按 MnO2 计）',
//       120004: '铍及其化合物（按 Be 计）',
//       120005: '镉及其化合物（按 Cd 计）',
//       120006: '铬及其化合物',
//       120007: '氧化锌',
//       120008: '砷',
//       120009: '砷化氢（胂）',
//       120010: '黄磷',
//       120011: '磷化氢',
//       120012: '钡及其可溶性化合物（按 Ba 计）',
//       120013: '钒及其化合物（按 V 计）',
//       120014: '有机锡',
//       120015: '铊及其可溶性化合物（按 Tl 计）',
//       120016: '羰基镍（按 Ni 计）',
//       120017: '氟及其化合物（不含氟化氢）（按 F 计）',
//       120018: '苯',
//       120019: '二硫化碳',
//       120020: '四氯化碳',
//       120021: '甲醇',
//       120022: '汽油、溶剂汽油',
//       120023: '溴甲烷',
//       120024: '1,2-二氯乙烷',
//       120025: '正己烷',
//       120026: '苯的氨基与硝基化合物（不含三硝基甲苯）',
//       120027: '三硝基甲苯',
//       120028: '联苯胺（4,4’-二氨基联苯）',
//       120029: '氯，氯气',
//       120030: '二氧化硫',
//       120031: '氮氧化物（一氧化氮和二氧化氮）',
//       120032: '氨',
//       120033: '光气（碳酰氯）',
//       120034: '甲醛',
//       120035: '一甲胺',
//       120036: '一氧化碳',
//       120037: '硫化氢',
//       120038: '氯乙烯',
//       120039: '三氯乙烯',
//       120040: '氯丙烯',
//       120041: 'β-氯丁二烯（氯丁二烯）',
//       120042: '有机氟聚合物单体及其热裂解物',
//       120043: '二异氰酸甲苯酯',
//       120044: '二甲基甲酰胺',
//       120045: '氰及其腈类化合物',
//       120046: '酚',
//       120047: '五氯酚及其钠盐',
//       120048: '氯甲醚',
//       120049: '丙烯酰胺',
//       120050: '偏二甲基肼',
//       120051: '硫酸二甲酯',
//       120052: '有机磷',
//       120053: '氨基甲酸酯类',
//       120054: '拟除虫菊酯',
//       120057: '焦炉逸散物（按苯溶物计）',
//       120058: '铅及其无机化合物（按 Pb 计，不包括四乙基铅）',
//       120059: '砷及其无机化合物（按 As 计）',
//       120060: '三氧化铬、铬酸盐、重铬酸盐（按 Cr 计）',
//       120061: '煤焦油',
//       120062: 'β萘胺',
//       120200: '安妥（α-萘硫脲）',
//       120201: '2-氨基吡啶',
//       120202: '氨基磺酸铵',
//       120203: '奥克托今（环四次甲基四硝胺）',
//       120204: '巴豆醛（丁烯醛）',
//       120205: '百草枯（1,1-二甲基-4,4-联吡啶鎓盐二氯化物）',
//       120206: '百菌清',
//       120207: '倍硫磷',
//       120208: '苯基醚（二苯醚）',
//       120209: '苯醌',
//       120210: '苯硫磷',
//       120211: '苯乙烯',
//       120212: '吡啶',
//       120213: '苄基氯',
//       120214: '丙酸',
//       120215: '丙烯醇',
//       120217: '丙烯菊酯',
//       120218: '丙烯醛',
//       120219: '丙烯酸',
//       120220: '丙烯酸甲酯',
//       120221: '丙烯酸正丁酯',
//       120222: '草甘膦',
//       120223: '草酸',
//       120224: '重氮甲烷',
//       120225: '抽余油（60 ℃~220 ℃）',
//       120226: '臭氧',
//       120227: '二氯二苯基三氯乙烷（滴滴涕，DDT）',
//       120228: 'O,O-二甲基-（2,2,2-三氯-1 羟基乙基）磷酸酯（敌百虫）',
//       120229: 'N-3,4-二氯苯基-N`,N`-二甲基脲（敌草隆）',
//       120230: 'o,o-二甲基-S-（甲基氨基甲酰甲基）二硫代磷酸酯（乐果）',
//       120231: '2,4-二氯苯氧基乙酸（2,4-滴）',
//       120232: '碲及其化合物（不含碲化氢）（按 Te 计）',
//       120233: '碲化铋（按 Bi2Te3 计）',
//       120234: '碘',
//       120235: '碘仿',
//       120236: '叠氮酸蒸气',
//       120237: '叠氮化钠',
//       120238: '丁醇',
//       120239: '1,3-丁二烯',
//       120240: '2-丁氧基乙醇',
//       120241: '丁醛',
//       120242: '丁酮',
//       120243: '丁烯',
//       120244: '毒死蜱',
//       120245: '对苯二胺',
//       120246: '对苯二甲酸',
//       120247: '对二氯苯（二氯苯）',
//       120248: '对硫磷',
//       120249: '对特丁基甲苯',
//       120250: '对硝基苯胺',
//       120251: '对硝基氯苯',
//       120252: '多次甲基多苯基多异氰酸酯',
//       120253: '二苯胺',
//       120254: '二苯基甲烷二异氰酸酯',
//       120255: '二丙二醇甲醚（2-甲氧基甲乙氧基丙醇）',
//       120256: '二丙酮醇',
//       120257: '2-N-二丁氨基乙醇',
//       120258: '二噁烷',
//       120259: '二噁英类化合物',
//       120260: '二氟氯甲烷',
//       120261: '二甲胺',
//       120262: '二甲苯（全部异构体）',
//       120263: 'N,N-二甲基苯胺',
//       120264: '1,3-二甲基丁基乙酸酯（仲乙酸己酯、乙酸仲己酯）',
//       120265: '二甲基二氯硅烷',
//       120266: '3,3-二甲基联苯胺',
//       120267: '二甲基亚砜',
//       120268: '二甲基乙酰胺',
//       120269: '二甲氧基甲烷',
//       120270: '二聚环戊二烯',
//       120271: '1,1-二氯-1-硝基乙烷',
//       120272: '1,3-二氯丙醇',
//       120273: '1,2-二氯丙烷',
//       120274: '1,3-二氯丙烯',
//       120275: '二氯二氟甲烷',
//       120276: '二氯甲烷',
//       120277: '二氯乙炔',
//       120278: '1,2-二氯乙烯（全部异构体）',
//       120279: '二硼烷（乙硼烷）',
//       120280: '二缩水甘油醚',
//       120281: '二硝基苯（全部异构体）',
//       120282: '二硝基甲苯',
//       120283: '4,6-二硝基邻甲酚',
//       120284: '2,4-二硝基氯苯',
//       120285: '二氧化氯',
//       120286: '二氧化碳',
//       120287: '二氧化锡（按 Sn 计）',
//       120288: '2-二乙氨基乙醇',
//       120289: '二乙烯三胺（二乙撑三胺）',
//       120290: '二乙基甲酮',
//       120291: '二乙烯基苯',
//       120292: '二异丁基甲酮',
//       120293: '甲苯 -2,4- 二异氰酸酯（TDI）',
//       120294: '二月桂酸二丁基锡',
//       120295: '呋喃',
//       120296: '氟化氢（按 F 计）',
//       120297: '锆及其化合物（按 Zr 计）',
//       120298: '汞-金属汞（蒸气）',
//       120299: '钴及其化合物（按 Co 计）',
//       120300: '癸硼烷',
//       120301: '过氧化苯甲酰',
//       120302: '过氧化甲乙酮',
//       120303: '过氧化氢',
//       120304: '环己胺',
//       120305: '环己醇',
//       120306: '环己酮',
//       120307: '环己烷',
//       120308: '环三次甲基三硝胺（黑索今）',
//       120309: '环氧丙烷',
//       120310: '环氧氯丙烷',
//       120311: '邻-茴香胺，对-茴香胺',
//       120312: '己二醇',
//       120313: '1,6-己二异氰酸酯（六亚甲基二异氰酸酯（HDI））',
//       120314: '己内酰胺',
//       120315: '2-己酮（甲基正丁基甲酮）',
//       120316: '甲拌磷',
//       120317: '甲苯',
//       120318: 'N-甲苯胺，O-甲苯胺',
//       120319: '甲酚（全部异构体）',
//       120320: '甲基丙烯腈',
//       120321: '甲基丙烯酸',
//       120322: '甲基丙烯酸甲酯（异丁烯酸甲酯）',
//       120323: '甲基丙烯酸缩水甘油酯',
//       120324: '甲基肼',
//       120325: '甲基内吸磷',
//       120326: '18-甲基炔诺酮（炔诺孕酮）',
//       120327: '甲基叔丁基醚',
//       120328: '甲硫醇',
//       120329: '甲酸',
//       120330: '甲乙酮（2-丁酮）',
//       120331: '2-甲氧基乙醇（甲氧基乙醇）',
//       120332: '2-甲氧基乙基乙酸酯',
//       120333: '甲氧氯',
//       120334: '间苯二酚',
//       120335: '肼',
//       120336: '久效磷',
//       120337: '糠醇',
//       120338: '糠醛',
//       120339: '考的松',
//       120340: '苦味酸（2,4,6-三硝基苯酚）',
//       120341: '联苯',
//       120342: '邻苯二甲酸二丁酯',
//       120343: '邻苯二甲酸酐（PA）',
//       120344: '邻二氯苯',
//       120345: '邻氯苯乙烯',
//       120346: '邻氯苄叉丙二腈',
//       120347: '邻仲丁基苯酚',
//       120348: '磷胺',
//       120349: '磷酸',
//       120350: '磷酸二丁基苯酯',
//       120351: '硫酸钡（按 Ba 计）',
//       120352: '硫酸及三氧化硫',
//       120353: '硫酰氟',
//       120354: '六氟丙酮',
//       120355: '六氟丙烯',
//       120356: '六氟化硫',
//       120357: '六六六（六氯环已烷）',
//       120358: 'γ-六六六（γ-六氯环己烷）',
//       120359: '六氯丁二烯',
//       120360: '六氯环戊二烯',
//       120361: '六氯萘',
//       120362: '六氯乙烷',
//       120363: '氯苯',
//       120364: '氯丙酮',
//       120365: '氯化铵烟',
//       120366: '氯化汞（升汞）',
//       120367: '氯化苦（三氯硝基甲烷）',
//       120368: '氯化氢及盐酸',
//       120369: '氯化锌烟',
//       120370: '氯甲烷',
//       120371: '氯联苯（54 %氯）',
//       120372: '氯萘',
//       120373: '氯乙醇',
//       120374: '氯乙醛',
//       120375: 'α-氯乙酰苯',
//       120376: '氯乙酰氯',
//       120377: '马拉硫磷',
//       120378: '马来酸酐',
//       120379: '吗啉',
//       120380: '煤焦油沥青挥发物（按苯溶物计）',
//       120381: '钼及其化合物（按 Mo 计）',
//       120382: '内吸磷',
//       120383: '萘',
//       120384: '萘酚',
//       120385: '萘烷',
//       120386: '尿素',
//       120387: '镍及其无机化合物(按 Ni 计) ',
//       120388: '氢化锂',
//       120389: '氢醌（对苯二酚）',
//       120390: '氢氧化钾',
//       120391: '氢氧化钠',
//       120392: '氢氧化铯',
//       120393: '氰氨化钙',
//       120394: '氰戊菊酯',
//       120395: '全氟异丁烯',
//       120396: '壬烷',
//       120397: '乳酸正丁酯',
//       120398: '三氟化氯',
//       120399: '三氟化硼',
//       120400: '三氟甲基次氟化物',
//       120401: '三甲苯磷酸酯（全部异构体）',
//       120402: '三甲基氯化锡',
//       120403: '1,2,3-三氯丙烷',
//       120404: '三氯化磷',
//       120405: '三氯甲烷（氯仿）',
//       120406: '三氯硫磷',
//       120407: '三氯氢硅',
//       120408: '三氯氧磷',
//       120409: '三氯乙醛',
//       120410: '1,1,1-三氯乙烷',
//       120411: '三溴甲烷',
//       120412: '三乙基氯化锡',
//       120413: '杀螟松',
//       120414: '杀鼠灵（3-（1-丙酮基苄基）-4-羟基香豆素；华法林）',
//       120415: '石蜡烟',
//       120416: '十溴联苯醚',
//       120417: '石油沥青烟(按苯溶物计)',
//       120418: '双（巯基乙酸）二辛基锡',
//       120419: '双酚 A ',
//       120420: '双硫醒',
//       120421: '双氯甲醚',
//       120422: '四氯乙烯',
//       120423: '四氢呋喃',
//       120424: '四氢化硅',
//       120425: '四氢化锗',
//       120426: '四溴化碳',
//       120427: '松节油',
//       120428: '钽及其氧化物（按 Ta 计）',
//       120429: '碳酸钠（纯碱）',
//       120430: '羰基氟',
//       120431: '锑及其化合物（按 Sb 计）',
//       120432: '铜及其化合物（按 Cu 计）',
//       120433: '钨及其不溶性化合物（按 W 计）',
//       120434: '五氟（一）氯乙烷',
//       120435: '五硫化二磷',
//       120436: '五羰基铁（按 Fe 计）',
//       120437: '五氧化二磷',
//       120438: '戊醇',
//       120439: '戊烷（全部异构体）',
//       120440: '硒化氢（按 Se 计）',
//       120441: '硒及其化合物（按 Se 计）（不包括六氟化硒、硒化氢）',
//       120442: '纤维素',
//       120443: '硝化甘油',
//       120444: '硝基苯',
//       120445: '1-硝基丙烷（硝基丙烷）',
//       120446: '2-硝基丙烷',
//       120447: '硝基甲苯（全部异构体）',
//       120448: '硝基甲烷',
//       120449: '硝基乙烷',
//       120450: '辛烷',
//       120451: '溴',
//       120452: '溴化氢（氢溴酸）',
//       120453: '溴丙烷（1-溴丙烷；2-溴丙烷）',
//       120454: '溴氰菊酯',
//       120455: '溴鼠灵',
//       120456: '氧化钙',
//       120457: '氧化镁烟',
//       120458: '氧乐果',
//       120459: '液化石油气',
//       120460: '乙胺',
//       120461: '乙苯',
//       120462: '乙醇胺（氨基乙醇）',
//       120463: '乙二胺（乙烯二胺，EDA）',
//       120464: '乙二醇',
//       120465: '乙二醇二硝酸酯',
//       120466: '乙酐（乙酸酐）',
//       120467: 'N-乙基吗啉',
//       120468: '乙基戊基甲酮',
//       120469: '乙腈',
//       120470: '乙硫醇',
//       120471: '乙醚',
//       120472: '乙醛',
//       120473: '乙酸',
//       120474: '乙酸丙酯',
//       120475: '乙酸丁酯',
//       120476: '乙酸甲酯',
//       120477: '乙酸戊酯（全部异构体）',
//       120478: '乙酸乙烯酯',
//       120479: '乙酸乙酯',
//       120480: '乙烯酮',
//       120481: '乙酰甲胺磷',
//       120482: '乙酰水杨酸（阿司匹林）',
//       120483: '2-乙氧基乙醇',
//       120484: '2-乙氧基乙基乙酸酯',
//       120485: '钇及其化合物（按 Y 计）',
//       120486: '异丙胺',
//       120487: '异丙醇',
//       120488: 'N-异丙基苯胺',
//       120489: '异稻瘟净',
//       120490: '3，5，5-三甲基-2-环己烯-1-酮（异佛尔酮）',
//       120491: '异佛尔酮二异氰酸酯',
//       120492: '异氰酸甲酯',
//       120493: '异亚丙基丙酮',
//       120494: '铟及其化合物（按 In 计）',
//       120495: '茚',
//       120496: '莠去津',
//       120497: '正丙醇',
//       120498: '正丁胺',
//       120499: '正丁醇',
//       120500: '正丁基硫醇',
//       120501: '正丁基缩水甘油醚',
//       120502: '正丁醛',
//       120503: '正庚烷',
//       120505: '萘二异氰酸酯（NDI）',
//       120506: 'N,N-二甲基-3-氨基苯酚',
//       120507: '1,1-二氯乙烯',
//       120508: '甲烷',
//       120509: '正香草酸（高香草酸）',
//       120510: '酚醛树脂',
//       120511: '二溴氯丙烷',
//       120512: '多氯联苯',
//       120513: '1,3-二氯丙烷',
//       120514: '二硫化硒',
//       120515: '三氯乙酸',
//       120516: '氯酸钾',
//       120517: '-3,4 二氯苯基丙酰胺（敌稗）',
//       120518: '丙酮醛（甲基乙二醛）',
//       120519: '双丙酮醇',
//       120520: '钽及其化合物',
//       120521: '吖啶',
//       120522: '环戊酮',
//       120523: '铀及其化合物',
//       120524: '钼酸',
//       120525: '卤化水杨酰苯胺（Ν-水杨酰苯胺）',
//       120526: '邻苯二甲酸二甲酯',
//       120527: '氯化苄烷胺（洁尔灭）',
//       120528: '二硝基苯酚',
//       120529: '三氧化钼',
//       120530: '多氯萘',
//       120531: '氯酸钠',
//       120532: '钾盐镁矾',
//       120533: '多溴联苯',
//       120534: '柴油',
//       120535: '木馏油（焦油）',
//       120536: '锂及其化合物',
//       120537: '亚硝酸乙酯',
//       120538: '甲酸乙酯',
//       120539: '环氧树脂',
//       120540: '乙炔',
//       120541: '五氟氯乙烷',
//       120542: '三氯一氟甲烷',
//       120543: '对氨基酚',
//       120544: '二乙烯二胺（哌嗪）',
//       120545: '乙酸苄酯',
//       120546: '多氯苯',
//       120547: '亚硫酸钠',
//       120548: '四氯乙烷',
//       120549: '氢氧化铵',
//       120550: '铂化物',
//       120551: '巯基乙酸',
//       120552: '聚氯乙烯热解物',
//       120553: '1,2,4-苯三酸酐（TMA）',
//       120554: '围涎树碱',
//       120555: '对溴苯胺',
//       120556: '钼酸铵',
//       120557: '氟乙酰胺',
//       120558: '二苯胍',
//       120559: '烯丙胺',
//       120560: '铜及其化合物',
//       120561: '丙醇',
//       120562: '铝酸钠',
//       120563: '乙基另戊基甲酮（5-甲基-3-庚酮）',
//       120564: '丙烯基芥子油',
//       120565: '氯磺酸',
//       120566: '苯乙醇',
//       120567: '二甲苯酚',
//       120568: '氯乙烷',
//       120569: '二异丙胺基氯乙烷',
//       120570: '二氯乙醚',
//       120571: '脲醛树脂',
//       120572: '蒽醌及其染料',
//       120573: '氯乙基胺',
//       120574: '氯甲酸三氯甲酯（双光气）',
//       120575: '三聚氰胺甲醛树脂',
//       120576: '2-氯苯基羟胺',
//       120577: '氟乙酸钠',
//       120578: '过硫酸盐（过硫酸钾、过硫酸钠、过硫酸铵等）',
//       120579: '磷酸三邻甲苯酯',
//       120580: '二氯酚',
//       120581: '钼酸钠',
//       120582: '碳酸铵',
//       120583: '氧化银',
//       120584: '乙基硫代磺酸乙酯',
//       120585: '二氯化砜（磺酰氯）',
//       120586: '多次甲基多苯基异氰酸酯',
//       120587: '硝基萘',
//       120588: '三氟甲基次氟酸酯',
//       120589: '蒽',
//       120590: '苯肼',
//       120591: '四氯化硅',
//       120592: '碳酸钙',
//       120593: '1,2,3-苯三酚（焦棓酚）',
//       120594: '氯甲酸甲酯',
//       120595: '三乙烯四胺（三乙撑四胺）',
//       120596: '乙酸异丙酯',
//       120597: '羟基香茅醛',
//       120598: '硝基萘胺',
//       120599: '邻茴香胺',
//       120600: '4-氯苯基羟胺',
//       120601: '杀虫脒',
//       120602: '1,6-己二胺',
//       120603: '苯基羟胺（苯胲）',
//       120604: '苄基溴（溴甲苯）',
//       120605: '1,3-二氯-2-丙醇',
//       120606: '溴乙烷',
//       120607: '甲基氨基酚',
//       120608: '四氯化钛',
//       120609: '苯绕蒽酮',
//       120610: '甲酸丁酯',
//       120611: '羟基乙酸',
//       120612: '三甲基己二酸',
//       120613: '硼烷',
//       120614: '三氯化硼',
//       120615: '甲酸甲酯',
//       120616: '对苯二甲酸二甲酯',
//       120617: '丙烷',
//       120618: '4,6-二硝基邻苯甲酚',
//       120619: '多氯酚',
//       120620: '双-(二甲基硫代氨基甲酰基)二硫化物（秋兰姆、福美双）',
//       120621: '3-氯苯基羟胺',
//       120622: '异丙醇胺（1-氨基-2-二丙醇）',
//       120623: '2-溴乙氧基苯',
//       120624: '溴苯',
//       120625: '磷化锌',
//       120626: '磷化铝',
//       120627: '二苯亚甲基二异氰酸酯（MDI）',
//       120628: '四氯苯二酸酐（TCPA）',
//       120630: '氯乙酸',
//       120631: '环氧乙烷',
//       120632: '碘甲烷',
//       120633: '丙酮',
//       120634: '苯胺',
//       120900: 'N-3,4 二氯苯基丙酰胺（敌稗）',
//       120901: '氢氟酸',
//       120902: '硝酸',
//       120903: '烷酸',
//       120904: '氰化氢',
//       120999: '其他化学有害因素',
//       13: '物理有害因素',
//       130001: '噪声',
//       130002: '振动',
//       130003: '高温',
//       130004: '高气压',
//       130006: '微波',
//       130009: '紫外辐射（紫外线）',
//       130300: '超高频电磁场（超高频辐射）',
//       130301: '高频电磁场（高频辐射）',
//       130302: '工频电磁场（工频辐射）',
//       130303: '低气压',
//       130304: '高原低氧',
//       130305: '红外线',
//       130306: '激光',
//       130307: '低温',
//       130999: '其他物理有害因素',
//       14: '生物因素',
//       140001: '布鲁菌属',
//       140002: '炭疽杆菌',
//       140400: '白僵蚕孢子',
//       140401: '枯草杆菌蛋白酶',
//       140402: '工业酶',
//       140403: '森林脑炎病毒',
//       140404: '艾滋病病毒',
//       140405: '伯氏疏螺旋体',
//       140999: '其他生物有害因素',
//       15: '其他职业病危害因素',
//       150001: '电工作业',
//       150002: '高处作业',
//       150003: '压力容器作业',
//       150004: '结核病防治工作',
//       150005: '肝炎病防治工作',
//       150006: '职业机动车驾驶作业',
//       150007: '视屏作业',
//       150008: '高原作业',
//       150009: '航空作业',
//       150999: '其他特殊作业',
//       16: '放射物质类',
//       160002: 'X射线',
//       160501: 'α射线',
//       160502: 'β射线',
//       160503: 'γ射线',
//       160504: '中子',
//       160506: '铀及其化合物',
//       160507: '氡及其短寿命子体',
//       160999: '其他',
//       19: '其他因素',
//       190900: '井下不良作业条件',
//       190901: '金属烟',
//       190902: '刮研作业',
//     };
//     return codeObj[value] || '';
//   }

//   /**
//    * 处理体检结论 3.9
//    * @param {number} value - The value to be processed.
//    * @return {string} - 目前未见异常  复查 疑似职业病 禁忌症 其他疾病或异常
//    */
//   static getConclusion(value) {
//     // value转换为数字
//     value = +value;
//     const codeObj = {
//       12001: '目前未见异常',
//       12002: '复查',
//       12003: '疑似职业病',
//       12004: '禁忌症',
//       12005: '其他疾病或异常',
//     };
//     return codeObj[value] || '';
//   }
//   /** 处理疑似职业病内容 3.13
//    * @param value 编码
//    * @return {string}
//    */
//   static getSuspectedOccupationalDisease(value) {
//     // value转换为数字
//     value = +value;
//     const codeObj = {
//       10009: '职业性慢性铅中毒',
//       10013: '职业性急性四乙基铅中毒',
//       10016: '职业性急性砷化氢中毒',
//       10024: '职业性慢性磷中毒',
//       10025: '职业性急性磷中毒',
//       10026: '职业性黄磷皮肤灼伤',
//       10029: '职业性慢性汞中毒',
//       10034: '职业性急性磷化氢中毒',
//       10037: '职业性慢性锰中毒',
//       10038: '职业性急性钡中毒',
//       10040: '职业性急性钒中毒',
//       10045: '职业性慢性铊中毒',
//       10046: '职业性急性铊中毒',
//       10047: '职业性急性羰基镍中毒',
//       10050: '工业性氟病',
//       10053: '职业性慢性苯中毒',
//       10054: '职业性苯所致白血病',
//       10055: '职业性急性苯中毒',
//       10063: '职业性慢性二硫化碳中毒',
//       10064: '职业性中毒性肝病',
//       10065: '职业性急性四氯化碳中毒',
//       10066: '职业性急性甲醇中毒',
//       10069: '职业性溶剂汽油中毒（慢性）',
//       10070: '汽油致职业性皮肤病',
//       10072: '职业性急性溴甲烷中毒',
//       10074: '职业性慢性正己烷中毒',
//       10077: '职业性急性苯的氨基或硝基化合物中毒',
//       10079: '职业性慢性三硝基甲苯中毒',
//       10080: '职业性三硝基甲苯致白内障',
//       10084: '联苯胺所致膀胱癌',
//       10085: '职业性接触性皮炎',
//       10086: '职业性急性氯气中毒',
//       10089: '职业性急性氮氧化物中毒',
//       10091: '职业性急性氨气中毒',
//       10092: '职业性慢性镉中毒',
//       10093: '职业性急性镉中毒',
//       10094: '金属烟热',
//       10095: '职业性铬鼻病',
//       10096: '职业性铬溃疡',
//       10097: '职业性铬所致皮炎',
//       10098: '职业性铬酸盐制造业工人肺癌',
//       10099: '职业性慢性砷中毒',
//       10100: '职业性砷所致肺癌、皮肤癌',
//       10101: '职业性慢性丙烯酰胺中毒',
//       10102: '职业性急性偏二甲基肼中毒',
//       10103: '职业性急性光气中毒',
//       10104: '职业性急性甲醛中毒',
//       10105: '职业性急性一甲胺中毒',
//       10106: '职业性急性一氧化碳中毒',
//       10109: '职业性急性硫化氢中毒',
//       10111: '职业性慢性氯乙烯中毒',
//       10112: '氯乙烯所致肝血管肉瘤',
//       10113: '职业性急性硫酸二甲酯中毒',
//       10114: '职业性急性氯乙烯中毒',
//       10115: '职业性急性有机磷杀虫剂中毒',
//       10116: '职业性急性三氯乙烯中毒',
//       10117: '职业性急性氨基甲酸酯杀虫剂中毒',
//       10119: '职业性急性拟除虫菊酯中毒',
//       10120: '职业性慢性氯丙烯中毒',
//       10121: '职业性哮喘',
//       10122: '职业性慢性氯丁二烯中毒',
//       10123: '职业性急性氯丁二烯中毒',
//       10124: '职业性急性有机氟中毒',
//       10125: '职业性急性二甲基甲酰胺中毒',
//       10126: '职业性急性氰化物中毒',
//       10127: '职业性急性腈类化合物中毒',
//       10129: '职业性急性酚中毒',
//       10130: '职业性酚皮肤灼伤',
//       10132: '职业性急性五氯酚中毒',
//       10133: '职业性氯甲醚所致肺癌',
//       10137: '矽肺',
//       10138: '煤工尘肺',
//       10140: '石棉肺',
//       10141: '石棉所致肺癌、间皮瘤',
//       10142: '石墨尘肺',
//       10143: '炭黑尘肺',
//       10144: '滑石尘肺',
//       10145: '水泥尘肺',
//       10146: '云母尘肺',
//       10147: '陶工尘肺',
//       10148: '铝尘肺',
//       10149: '电焊工尘肺',
//       10150: '铸工尘肺',
//       10151: '棉尘病',
//       10159: '职业性手臂振动病',
//       10164: '职业性中暑',
//       10190: '减压性骨坏死',
//       10196: '职业性电光性皮炎',
//       10197: '职业性白内障',
//       10228: '职业性慢性高原病',
//       10229: '职业性航空病',
//       10230: '职业性急性汞中毒',
//       10232: '职业性急性1，2-二氯乙烷中毒',
//       10233: '职业性化学性眼灼伤',
//       10234: '职业性牙酸蚀病',
//       10235: '职业性皮肤灼伤',
//       10237: '职业性布氏杆菌病',
//       10238: '职业性炭疽',
//       10239: '职业性急性二氧化硫中毒',
//       10302: '职业性慢性铍病',
//       10303: '职业性铍接触性皮炎',
//       10304: '职业性铍溃疡',
//       10305: '职业性急性铍病',
//       10306: '职业性急性三烷基锡中毒',
//       10307: '职业性慢性中毒性肝病',
//       10308: '职业性慢性溶剂汽油中毒',
//       10309: '职业性急性溶剂汽油中毒',
//       10310: '腕管综合征',
//       10311: '颈肩腕综合征',
//       10312: '肺结核',
//       10313: '慢性肝病',
//       10314: '职业性急性电光性眼炎（紫外线角膜结膜炎）',
//       10315: '职业性急性电光性皮炎',
//       10316: '职业性刺激性化学物致慢性阻塞性肺疾病',
//       10317: '职业性化学性眼部灼伤',
//       10318: '职业性化学性皮肤灼伤',
//       10319: '甲醛致职业性皮肤病',
//       10320: '职业性三氯乙烯药疹样皮炎',
//       10321: '职业性急性化学物中毒性呼吸系统疾病',
//       10322: '职业性焦炉逸散物所致肺癌',
//       10323: '焦炉逸散物所致职业性皮肤病',
//       10324: '职业性急性变应性肺泡炎',
//       10325: '职业性噪声聋',
//       10326: '职业性爆震聋',
//       10327: '急性电光性眼炎(紫外线角膜、结膜炎)',
//       10328: '电光性皮炎',
//       10329: '职业性炭疸',
//       10330: '中枢神经系统器质性疾病',
//       10331: '急性高原病',
//       10332: '甲醛所致职业性哮喘',
//       10333: '急性减压病',
//       10366: '职业性金属及其化合物粉尘肺沉着病',
//       10367: '职业性硬金属肺病',
//       10368: '职业性耳鼻喉口腔疾病',
//       10369: '尘肺',
//     };
//     return codeObj[value] || '';
//   }
//   /** 处理禁忌症内容 3.14
//    * @param value 编码
//    * @return {string} 禁忌症内容
//    */
//   static getOccupationalContraindication(value) {
//     // value转换为数字
//     value = +value;
//     const codeObj = {
//       10007: '卟啉病',
//       10008: '多发性周围神经病',
//       10009: '贫血',
//       10012: '中枢神经系统器质性疾病',
//       10017: '牙本质病变（不包括龋齿）',
//       10018: '下颌骨疾病',
//       10019: '慢性肝炎',
//       10020: '慢性肾炎',
//       10022: '慢性肾脏疾病',
//       10025: '慢性阻塞性肺病',
//       10026: '慢性间质性肺病',
//       10028: '钾代谢障碍',
//       10031: '支气管哮喘',
//       10034: '地方性氟病',
//       10035: '骨关节疾病',
//       10037: '活动性肺结核',
//       10039: '视网膜病变',
//       10040: '过敏性皮肤病',
//       10041: '神经系统器质性疾病',
//       10042: '血清葡萄糖-6-磷酸脱氢酶缺乏症',
//       10045: '尿脱落细胞检查巴氏分级国际标准Ⅳ级及以上',
//       10055: '器质性心脏病',
//       10056: '类风湿关节炎',
//       10057: '全血胆碱酯酶活性明显低于正常者',
//       10058: '致喘物过敏和支气管哮喘',
//       10060: '伴肺功能损害的心血管系统疾病',
//       10063: '活动性肺结核病',
//       10064: '伴肺功能损害的疾病',
//       10070: '雷诺病',
//       10097: '加压试验不合格或氧敏感试验阳性者',
//       10099: '活动性角膜疾病',
//       10100: '白内障',
//       10101: '面、手背和前臂等暴露部位严重的皮肤病',
//       10102: '白化病',
//       10103: '癫痫',
//       10105: '红绿色盲',
//       10107: '四肢关节运动功能障碍',
//       10108: '高血压',
//       10109: '恐高症',
//       10111: '四肢骨关节及运动功能障碍',
//       10113: '未治愈的肺结核病',
//       10117: '暗适应：＞30s',
//       10118: '复视、立体盲、严重视野缺损',
//       10119: '梅尼埃病',
//       10120: '眩晕',
//       10121: '癔病',
//       10122: '震颤麻痹',
//       10124: '痴呆',
//       10125: '影响肢体活动的神经系统疾病',
//       10127: '腕管综合征',
//       10128: '颈椎病',
//       10129: '矫正视力小于4.5',
//       10130: '红细胞增多症',
//       10138: '红或绿色盲',
//       10201: '中度贫血',
//       10202: '已确诊并仍需要医学监护的精神障碍性疾病',
//       10203: '慢性皮肤溃疡',
//       10204: '慢性肾脏病',
//       10205: '骨质疏松症',
//       10206: '萎缩性鼻炎',
//       10207: '未控制的甲状腺功能亢进症',
//       10208: '严重慢性皮肤疾病',
//       10209: '慢性肝病',
//       10210: '慢性器质性心脏病',
//       10211: '视网膜及视神经病',
//       10212: '呼吸系统疾病史及有关症状',
//       10213:
//         '血常规检出有如下异常者：白细胞计数低于4.5×10^9/L； 血小板计数低于8×10^10/L；  红细胞计数男性低于4×10^12/L，女性低于3.5×10^12/L或血红蛋白定',
//       10214: '血系统疾病',
//       10215: '严重慢性皮肤疾患',
//       10216: '伴有气道高反应的过敏性鼻炎',
//       10217: '伴气道高反应的过敏性鼻炎',
//       10218: '严重的皮肤疾病',
//       10219: '牙酸蚀病',
//       10220:
//         '各种原因引起永久性感音神经性听力损失（500Hz、1000Hz和2000Hz中任一频率的纯音气导听阈＞25dB）',
//       10221: '任一耳传导性耳聋，平均语频听力损失≥41dB',
//       10222: '高频段3000Hz，4000Hz，6000Hz双耳平均听阈≥40dB',
//       10223:
//         '噪声敏感者（上岗前职业健康体检纯音听力检查各频率听力损失均≤25dB，但噪声作业1年之内，高频段3000Hz，4000Hz，6000Hz中任一耳，任一频率听阈≥65dB）',
//       10224:
//         '除噪声外各种原因引起的永久性感音神经性听力损失（500Hz，l000Hz和2000Hz中任一频率的纯音气导听阈>25dB）',
//       10225: '未控制的高血压',
//       10226: '未控制的糖尿病',
//       10227: '全身瘢痕面积≥20%以上（工伤标准的八级）',
//       10228: '各类器质性心脏病（风湿性心脏病、心肌病、冠心病、先天性心脏病等）',
//       10229: '器质性心律不齐、直立性低血压、周围血管病',
//       10230: '慢性支气管炎、支气管哮喘、肺结核、结核性胸膜炎、自发性气胸及病史',
//       10231:
//         '食道、胃、十二指肠、肝、胆、脾、胰疾病、慢性细菌性痢疾、慢性肠炎、腹部包块、消化系统、泌尿系统结石',
//       10232: '泌尿、血液、内分泌及代谢系统疾病',
//       10233: '结缔组织疾病，过敏体质',
//       10234: '中枢神经系统及周围神经系统疾病和病史',
//       10235: '癫痫、精神病、晕厥史、神经症和癔病精神活性物质滥用和依赖',
//       10236:
//         '各种原因引起的头颅异常影响戴面罩者，胸廓畸形，脊椎疾病，损伤及进行性病变，脊椎活动范围受限或明显异常，慢性眼腿痛，关节活动受限或疼痛',
//       10237:
//         '多发性肝、肾及骨囊肿，多发性脂肪瘤，瘢痕体质或全身瘢痕面积≥20%以上者',
//       10238:
//         '有颅脑、胸腔及腹腔手术史等外科疾病。阑尾炎术时间未超过半年，腹股沟斜疝和股疝修补术未超过1年者',
//       10239: '脉管炎、动脉瘤、动静脉瘘，静脉曲张',
//       10240: '脱肛，肛瘘，陈旧性肛裂，多发性痔疮及单纯性痔疮经常出血者',
//       10241:
//         '腋臭，头癖，泛发性体癣，疥疮，慢性湿疹，神经性皮炎，白癜风，银屑病',
//       10242: '单眼裸视力不得低于4.8(0.6)，色弱，色盲，夜盲及眼科其他器质性疾患',
//       10243: '外耳畸形耳、鼻、喉及前庭器官的器质性疾病，咽鼓管功能异常者',
//       10244: '手足部习惯性冻疮',
//       10245:
//         '淋病、梅毒、软下疳、性病淋巴肉芽肿、非淋球菌性尿道炎、尖锐湿疣、生殖器疱疹、艾滋病及艾滋病毒携带者',
//       10246:
//         '纯音听力测试任一耳500Hz听力损失不得超过30dB，l000Hz、2000Hz听力损失不得超过25dB，4000Hz听力损失不得超过35dB',
//       10247: '生殖系统疾病',
//       10248: '泛发慢性湿疹',
//       10249: '泛发慢性皮炎',
//       10250: '晕厥（近一年内有晕厥发作史）',
//       10251: '2级及以上高血压（未控制）',
//       10252: '器质性心脏病或各种心律失常',
//       10253: '晕厥，眩晕症',
//       10254: '双耳语言频段平均听力损失>25dB',
//       10255: '器质性心脏病或心律失常',
//       10256: '肺结核',
//       10257: '身高：大型机动车驾驶员＜155cm，小型机动车驾驶员＜150cm',
//       10258: '听力：双耳平均听阈＞30dB（纯音气导）',
//       10259: '深视力：<（－22mm）或>（＋22mm）',
//       10260: '各类精神障碍疾病',
//       10261: '吸食、注射毒品、长期服用依赖性精神药品成瘾尚未戒除者',
//       10262:
//         '远视力（对数视力表）：大型机动车驾驶员：两裸眼＜4.0，并＜5.0（矫正）；小型机动车驾驶员：两裸眼＜4.0，并＜4.9（矫正）',
//       10263:
//         '血压：大型机动车驾驶员：收缩压≥18.7kPa（≥140mmHg）和舒张压≥12kPa（≥90mmHg）；小型机动车驾驶员：2级及以上高血压（未控制）。',
//       10264: '颈肩腕综合征',
//       10265: '2级及以上高血压或低血压',
//       10266: '活动的，潜在的，急性或慢性疾病',
//       10267: '创伤性后遗症',
//       10268: '影响功能的变形，缺损或损伤及影响功能的肌肉系统疾病',
//       10269: '恶性肿瘤或影响生理功能的良性肿瘤',
//       10270: '急性感染性，中毒性精神障碍治愈后留有后遗症',
//       10271: '神经症，经常性头痛，睡眠障碍',
//       10272: '药物成瘾，酒精成瘾者',
//       10273: '中枢神经系统疾病，损伤',
//       10274: '严重周围神经系统疾病及植物神经系统疾病',
//       10275: '呼吸系统慢性疾病及功能障碍，肺结核，自发性气胸，胸腔脏器手术史',
//       10276: '心血管器质性疾病，房室传导阻滞以及难以治愈的周围血管疾病',
//       10277: '严重消化系统疾病，功能障碍或手术后遗症，病毒性肝炎',
//       10278: '泌尿系统疾病，损伤以及严重生殖系统疾病',
//       10279: '造血系统疾病',
//       10280: '新陈代谢，免疫，内分泌系统系统疾病',
//       10281: '运动系统疾病，损伤及其后遗症',
//       10282: '难以治愈的皮肤及其附属器疾病（不含非暴露部位范围小的白癜风）',
//       10283:
//         '任一眼裸眼远视力低于0.7，任一眼裸眼近视力低于1.0；视野异常；色盲，色弱；夜盲治疗无效 者；眼及其附属器疾病治愈后遗有眼功能障碍',
//       10284:
//         '任一耳纯音听力图气导听力曲线在500HZ，1000Hz，2000Hz任一频率听力损失不得超过35dB或3000Hz频率听力损失不得超过50dB',
//       10285:
//         '耳气压功能不良治疗无效者，中耳慢性进行性疾病。内耳疾病或眩晕症不合格',
//       10286:
//         '影响功能的鼻，鼻窦慢性进行性疾病，嗅觉丧失，影响功能且不易矫治的咽喉部慢性进行性疾病者',
//       10287: '影响功能的口腔及颞下领关节慢性进行性疾病',
//       10288: '职业性航空病',
//       10289: '职业性噪声聋',
//       10290: '严重的呼吸系统疾病',
//       10291: '严重的循环系统疾病',
//       10292: '严重的消化系统疾病',
//       10293: '严重的造血系统疾病',
//       10294: '严重的神经和精神系统疾病',
//       10295: '严重的泌尿生殖系统疾病',
//       10296: '严重的内分泌系统疾病',
//       10297: '严重的免疫系统疾病',
//       10299: '严重的视听障碍、严重的听力障碍',
//       10300: '恶性肿瘤、有碍于工作的巨大的、复发性良性肿瘤',
//       10301: '严重的、有碍于工作的残疾，先天畸形和遗传性疾病',
//       10302: '手术后而不能恢复正常功能者',
//       10303: '未完全恢复的放射性疾病或其他职业病等',
//       10304: '有吸毒、酗酒或其他恶习不能改正者',
//       10305:
//         '血常规检出有如下异常者：白细胞计数低于4×10^9/L或中性粒细胞低于2×10^9/L；血小板计数低于8×10^10/L。',
//       10411: '伴肺功能损害的呼吸系统疾病',
//     };
//     return codeObj[value] || '';
//   }
//   static encryptByKey(source, key) {
//     try {
//       return this.encryptByDes(this.pkcs5Pad(source), this.pkcs5Pad(key));
//     } catch (e) {
//       console.error(e);
//       return null;
//     }
//   }
//   static decrypt(source, key) {
//     try {
//       return this.decryptByDes(source, this.pkcs5Pad(key))
//         .trim()
//         .replace(/\0/g, '');
//     } catch (e) {
//       return '';
//     }
//   }
//   static decryptByKey(source, key) {
//     try {
//       return this.decryptByDes(source, this.pkcs5Pad(key)).trim();
//     } catch (e) {
//       console.error(e);
//       return null;
//     }
//   }
//   static encryptByDes(source, key) {
//     const keyHex = CryptoJS.enc.Utf8.parse(key);
//     const encrypted = CryptoJS.DES.encrypt(source, keyHex, {
//       mode: CryptoJS.mode.ECB,
//       padding: CryptoJS.pad.Pkcs7,
//     });
//     return encrypted.ciphertext.toString();
//   }
//   static decryptByDes(ciphertext, key) {
//     const keyHex = CryptoJS.enc.Utf8.parse(key);
//     const decrypted = CryptoJS.DES.decrypt(
//       {
//         ciphertext: CryptoJS.enc.Hex.parse(ciphertext),
//       },
//       keyHex,
//       {
//         mode: CryptoJS.mode.ECB,
//         padding: CryptoJS.pad.Pkcs7,
//       }
//     );
//     return decrypted.toString(CryptoJS.enc.Utf8);
//   }
//   static pkcs5Pad(source) {
//     const blockSize = 8; // DES块大小为8字节
//     const paddingChar = '\u0000'; // 使用ASCII码为0的字符进行填充
//     const padLength = blockSize - (source.length % blockSize);
//     let padded = source;
//     for (let i = 0; i < padLength; i++) {
//       padded += paddingChar;
//     }
//     return padded;
//   }
// }

// // 工具类：福州数据目录扫描和文件处理
// class FzDataUtils {
//   /**
//    * 扫描福州数据目录
//    * @param {Object} options - 扫描选项
//    * @param {string} options.filterType - 过滤类型，默认 'FrombhkDataDownLoad'
//    * @param {Array} options.targetDates - 目标日期列表，为空则扫描所有日期
//    * @param {boolean} options.silent - 是否静默模式，默认 false
//    * @param {boolean} options.includeStats - 是否包含统计信息，默认 true
//    * @return {Promise<Object>} 扫描结果
//    */
//   static async scanFzDataDirectory(options = {}) {
//     const {
//       filterType = 'FrombhkDataDownLoad',
//       targetDates = [],
//       silent = false,
//       includeStats = true,
//     } = options;

//     if (!silent) {
//       console.time('scanFzDataDirectory');
//       console.log('开始扫描福州数据目录...');
//     }

//     const logPath = '/Users/<USER>/代码/zhiwei/oapi/app/public/fzData/';

//     try {
//       // 获取所有日期目录
//       const dateDirectories = await this.getDateDirectories(logPath);

//       if (!silent) {
//         console.log(`找到 ${dateDirectories.length} 个日期目录`);
//       }

//       // 过滤目标日期
//       const targetDateDirs = targetDates.length > 0
//         ? dateDirectories.filter(date => targetDates.includes(date))
//         : dateDirectories;

//       if (!silent && targetDates.length > 0) {
//         console.log(`过滤后处理 ${targetDateDirs.length} 个目录`);
//       }

//       const scanResults = {};
//       const summary = {
//         totalDirectories: targetDateDirs.length,
//         totalFiles: 0,
//         byType: {
//           FromcrptDownLoad: 0,
//           FrombhkDataDownLoad: 0,
//           FromoccdiscaseDownLoad: 0,
//         },
//       };

//       // 扫描每个日期目录
//       for (const dateDir of targetDateDirs) {
//         const dirPath = path.join(logPath, dateDir);
//         const result = await this.scanDateDirectory(dirPath, filterType);
//         scanResults[dateDir] = result;

//         // 更新统计信息
//         if (includeStats) {
//           Object.keys(summary.byType).forEach(type => {
//             const count = result[type] ? result[type].length : 0;
//             summary.byType[type] += count;
//             summary.totalFiles += count;
//           });
//         }

//         if (!silent) {
//           const typeStats = Object.keys(summary.byType)
//             .map(type => `${type}: ${result[type] ? result[type].length : 0}`)
//             .join(', ');
//           console.log(`  ${dateDir}: ${typeStats}`);
//         }
//       }

//       // 构建返回结果
//       const allFiles = this.categorizeFiles(scanResults);

//       const finalResult = {
//         summary,
//         filesByDate: scanResults,
//         allFiles,
//       };

//       if (!silent) {
//         console.log('扫描完成，统计信息:', summary);
//         console.timeEnd('scanFzDataDirectory');
//       }

//       return finalResult;

//     } catch (error) {
//       console.error('扫描目录失败:', error.message);
//       throw error;
//     }
//   }

//   /**
//    * 获取所有日期格式的目录
//    * @param {string} basePath - 基础路径
//    * @return {Promise<string[]>} 日期目录列表
//    */
//   static async getDateDirectories(basePath) {
//     try {
//       const fs = require('fs').promises;
//       const items = await fs.readdir(basePath);
//       const datePattern = /^\d{8}$/;
//       const dateDirectories = [];

//       for (const item of items) {
//         const itemPath = path.join(basePath, item);
//         const stats = await fs.stat(itemPath);
//         if (stats.isDirectory() && datePattern.test(item)) {
//           dateDirectories.push(item);
//         }
//       }

//       return dateDirectories.sort();
//     } catch (error) {
//       console.error('获取日期目录失败:', error.message);
//       return [];
//     }
//   }

//   /**
//    * 扫描单个日期目录
//    * @param {string} dirPath - 目录路径
//    * @param {string} filterType - 过滤类型
//    * @return {Promise<Object>} 扫描结果
//    */
//   static async scanDateDirectory(dirPath, filterType = 'all') {
//     try {
//       const fs = require('fs').promises;
//       const files = await fs.readdir(dirPath);

//       const filterPatterns = {
//         FromcrptDownLoad: /.*FromcrptDownLoad\.zip$/,
//         FrombhkDataDownLoad: /.*FrombhkDataDownLoad\.zip$/,
//         FromoccdiscaseDownLoad: /.*FromoccdiscaseDownLoad\.zip$/,
//         all: null,
//       };

//       const result = {
//         FromcrptDownLoad: [],
//         FrombhkDataDownLoad: [],
//         FromoccdiscaseDownLoad: [],
//         others: [],
//       };

//       for (const file of files) {
//         const filePath = path.join(dirPath, file);
//         try {
//           const stats = await fs.stat(filePath);
//           if (stats.isFile()) {
//             let categorized = false;
//             for (const [ type, pattern ] of Object.entries(filterPatterns)) {
//               if (type !== 'all' && pattern && pattern.test(file)) {
//                 result[type].push({
//                   name: file,
//                   path: filePath,
//                   size: stats.size,
//                   mtime: stats.mtime,
//                 });
//                 categorized = true;
//                 break;
//               }
//             }
//             if (!categorized && filterType === 'all') {
//               result.others.push({
//                 name: file,
//                 path: filePath,
//                 size: stats.size,
//                 mtime: stats.mtime,
//               });
//             }
//           }
//         } catch (fileError) {
//           console.error(`无法读取文件 ${file}:`, fileError.message);
//         }
//       }

//       return result;
//     } catch (error) {
//       console.error(`扫描目录 ${dirPath} 失败:`, error.message);
//       return {
//         FromcrptDownLoad: [],
//         FrombhkDataDownLoad: [],
//         FromoccdiscaseDownLoad: [],
//         others: [],
//       };
//     }
//   }

//   /**
//    * 将文件按类型分类
//    * @param {Object} scanResults - 扫描结果
//    * @return {Object} 分类后的文件
//    */
//   static categorizeFiles(scanResults) {
//     const categorized = {
//       FromcrptDownLoad: [],
//       FrombhkDataDownLoad: [],
//       FromoccdiscaseDownLoad: [],
//     };

//     for (const [ date, result ] of Object.entries(scanResults)) {
//       for (const [ type, files ] of Object.entries(result)) {
//         if (categorized[type]) {
//           categorized[type].push(...files);
//         }
//       }
//     }

//     return categorized;
//   }

//   /**
//    * 从ZIP文件中获取解密数据
//    * @param {string} filePath - ZIP文件路径
//    * @return {Promise<Object|null>} 解密后的数据
//    */
//   static async getOldDataFromZip(filePath) {
//     try {
//       // 检查文件是否存在
//       if (!fs.existsSync(filePath)) {
//         console.error(`文件不存在: ${filePath}`);
//         return null;
//       }

//       // 读取zip文件内容
//       const zipContent = fs.readFileSync(filePath);
//       const zip = new JSZip();
//       const zipData = await zip.loadAsync(zipContent);

//       // 获取data.json文件
//       const data = zipData.files['data.json'];
//       if (!data) {
//         console.error(`zip文件中不存在data.json: ${filePath}`);
//         return null;
//       }

//       // 读取并解密内容
//       const content = await data.async('string');
//       const decryptContent = EncryptUtil.decrypt(content, encryptionKey);
//       const jsonContent = JSON.parse(decryptContent);

//       console.log(`成功从zip文件获取数据: ${filePath}`);
//       return jsonContent;
//     } catch (error) {
//       console.error(`从zip文件获取数据失败: ${filePath}`, error);
//       return null;
//     }
//   }
// }

// // 性能优化：批量处理配置
// const BATCH_CONFIG = {
//   CONCURRENT_FILES: 5, // 同时处理的文件数量
//   BATCH_INSERT_SIZE: 1000, // 批量插入的记录数量
//   PROGRESS_INTERVAL: 10, // 进度报告间隔（文件数）
// };

// /**
//  * 批量处理文件数据
//  * @param {Array} files - 文件列表
//  * @param {number} startIndex - 开始索引
//  * @param {number} endIndex - 结束索引
//  * @return {Promise<Object>} 处理结果
//  */
// async function processBatch(files, startIndex, endIndex) {
//   const batchResults = {
//     normalRecords: [],
//     skipRecords: [],
//     errors: [],
//   };

//   // 并发处理文件
//   const promises = [];
//   for (let i = startIndex; i < endIndex && i < files.length; i++) {
//     promises.push(processFile(files[i], i));
//   }

//   const fileResults = await Promise.allSettled(promises);

//   // 合并结果
//   fileResults.forEach((result, index) => {
//     if (result.status === 'fulfilled') {
//       const fileResult = result.value;
//       batchResults.normalRecords.push(...fileResult.normalRecords);
//       batchResults.skipRecords.push(...fileResult.skipRecords);
//     } else {
//       batchResults.errors.push({
//         fileIndex: startIndex + index,
//         error: result.reason,
//       });
//     }
//   });

//   return batchResults;
// }

// /**
//  * 处理单个文件
//  * @param {Object} fileInfo - 文件信息
//  * @return {Promise<Object>} 文件处理结果
//  */
// async function processFile(fileInfo) {
//   const filePath = fileInfo.path;
//   const result = {
//     normalRecords: [],
//     skipRecords: [],
//   };

//   try {
//     // 解密文件
//     const jsonContent = await FzDataUtils.getOldDataFromZip(filePath);

//     if (!jsonContent) {
//       throw new Error(`文件解密失败: ${fileInfo.name}`);
//     }

//     if (jsonContent.type !== '00') {
//       // 创建跳过记录
//       const skipRecord = {
//         filePath,
//         fileName: fileInfo.name,
//         fileSize: fileInfo.size,
//         sourceData: jsonContent,
//         dataType: jsonContent.type || 'unknown',
//         bhkListCount: jsonContent.bhkList ? jsonContent.bhkList.length : 0,
//         processStatus: 'pending',
//         remark: `检测到 type !== "00" 的体检数据，已跳过处理。实际类型: ${jsonContent.type}`,
//       };
//       result.skipRecords.push(skipRecord);
//     } else {
//       // 处理正常数据
//       if (jsonContent.occdiscasetList && Array.isArray(jsonContent.occdiscasetList)) {
//         for (const jtem of jsonContent.occdiscasetList) {
//           result.normalRecords.push(jtem);
//         }
//       }
//     }

//     return result;
//   } catch (error) {
//     console.error(`处理文件失败: ${fileInfo.name}`, error);
//     throw error;
//   }
// }
// // /**
// //  * 处理单个文件
// //  * @param {Object} fileInfo - 文件信息
// //  * @return {Promise<Object>} 文件处理结果
// //  */
// // async function processFileTj(fileInfo) {
// //   const filePath = fileInfo.path;
// //   const result = {
// //     normalRecords: [],
// //     skipRecords: [],
// //   };

// //   try {
// //     // 解密文件
// //     const jsonContent = await FzDataUtils.getOldDataFromZip(filePath);

// //     if (!jsonContent) {
// //       throw new Error(`文件解密失败: ${fileInfo.name}`);
// //     }

// //     if (jsonContent.type !== '00') {
// //       // 创建跳过记录
// //       const skipRecord = {
// //         filePath,
// //         fileName: fileInfo.name,
// //         fileSize: fileInfo.size,
// //         sourceData: jsonContent,
// //         dataType: jsonContent.type || 'unknown',
// //         bhkListCount: jsonContent.bhkList ? jsonContent.bhkList.length : 0,
// //         processStatus: 'pending',
// //         remark: `检测到 type !== "00" 的体检数据，已跳过处理。实际类型: ${jsonContent.type}`,
// //       };
// //       result.skipRecords.push(skipRecord);
// //     } else {
// //       // 处理正常数据
// //       if (jsonContent.bhkList && Array.isArray(jsonContent.bhkList)) {
// //         for (const jtem of jsonContent.bhkList) {
// //           const processedBadrsnList = [];
// //           if (jtem.badRsnList) {
// //             for (const badRsnItem of jtem.badRsnList) {
// //               processedBadrsnList.push({
// //                 harmFactor: EncryptUtil.getHarmFactors(badRsnItem.badRsnCode),
// //                 examConclusion: EncryptUtil.getConclusion(+badRsnItem.examConclusionCode),
// //                 suspectedOccupationalDisease: EncryptUtil.getSuspectedOccupationalDisease(badRsnItem.yszybCode),
// //                 occupationalContraindications: EncryptUtil.getOccupationalContraindication(badRsnItem.zyjjzCode),
// //                 otherDisease: badRsnItem.qtjbName,
// //               });
// //             }
// //           }

// //           const item = {
// //             bhkCode: jtem.bhkCode,
// //             idc: jtem.idc,
// //             name: jtem.personName,
// //             riskFactorsOfPhysicalExaminations: processedBadrsnList,
// //             ...jtem,
// //             // 存储文件信息
// //             filePath,
// //             fileName: fileInfo.name,
// //             fileSize: fileInfo.size,
// //             processedAt: new Date(),
// //           };
// //           result.normalRecords.push(item);
// //         }
// //       }
// //     }

// //     return result;
// //   } catch (error) {
// //     console.error(`处理文件失败: ${fileInfo.name}`, error);
// //     throw error;
// //   }
// // }

// /**
//  * 批量插入数据到数据库
//  * @param {Array} records - 要插入的记录
//  * @param {Object} model - Mongoose 模型
//  * @param {string} collectionName - 集合名称（用于日志）
//  * @return {Promise<number>} 插入的记录数
//  */
// async function batchInsertRecords(records, model, collectionName) {
//   if (!records || records.length === 0) return 0;

//   try {
//     console.log(`开始批量插入 ${records.length} 条记录到 ${collectionName}...`);

//     // 分批插入，避免单次插入数据过大
//     let totalInserted = 0;
//     const batches = [];

//     for (let i = 0; i < records.length; i += BATCH_CONFIG.BATCH_INSERT_SIZE) {
//       batches.push(records.slice(i, i + BATCH_CONFIG.BATCH_INSERT_SIZE));
//     }

//     for (let i = 0; i < batches.length; i++) {
//       const batch = batches[i];
//       try {
//         await model.insertMany(batch, {
//           ordered: false, // 即使某条记录失败也继续插入其他记录
//           rawResult: false, // 不返回原始结果，提高性能
//         });
//         totalInserted += batch.length;

//         if (batches.length > 1) {
//           console.log(`${collectionName} 批次 ${i + 1}/${batches.length} 完成，插入 ${batch.length} 条记录`);
//         }
//       } catch (batchError) {
//         console.error(`${collectionName} 批次 ${i + 1} 插入失败:`, batchError.message);
//         // 即使某个批次失败，也继续处理下一个批次
//       }
//     }

//     console.log(`${collectionName} 批量插入完成，成功插入 ${totalInserted} 条记录`);
//     return totalInserted;
//   } catch (error) {
//     console.error(`${collectionName} 批量插入失败:`, error);
//     throw error;
//   }
// }

// // const result = [];
// async function main() {
//   const startTime = Date.now();

//   try {
//     console.log('开始扫描福州数据目录...');

//     // 使用新的扫描方法获取 FrombhkDataDownLoad 类型的文件
//     const scanResult = await FzDataUtils.scanFzDataDirectory({
//       filterType: 'FromoccdiscaseDownLoad',
//       silent: false,
//       includeStats: true,
//     });

//     // 获取所有 FrombhkDataDownLoad 文件
//     const bhkDataFiles = scanResult.allFiles.FromoccdiscaseDownLoad || [];

//     if (bhkDataFiles.length === 0) {
//       console.log('没有找到 FrombhkDataDownLoad 类型的文件');
//       return;
//     }

//     console.log(`找到 ${bhkDataFiles.length} 个体检数据文件，开始批量处理...`);
//     console.log(`配置: 并发文件数=${BATCH_CONFIG.CONCURRENT_FILES}, 批量插入大小=${BATCH_CONFIG.BATCH_INSERT_SIZE}`);

//     // 累计数据收集器
//     const allNormalRecords = [];
//     const allSkipRecords = [];
//     let totalErrors = 0;

//     // 批量处理文件
//     for (let i = 0; i < bhkDataFiles.length; i += BATCH_CONFIG.CONCURRENT_FILES) {
//       const endIndex = Math.min(i + BATCH_CONFIG.CONCURRENT_FILES, bhkDataFiles.length);

//       console.log(`\n处理文件批次: ${i + 1}-${endIndex}/${bhkDataFiles.length}`);

//       try {
//         // 处理当前批次的文件
//         const batchResults = await processBatch(bhkDataFiles, i, endIndex);

//         // 收集结果
//         allNormalRecords.push(...batchResults.normalRecords);
//         allSkipRecords.push(...batchResults.skipRecords);
//         totalErrors += batchResults.errors.length;

//         // 报告错误
//         if (batchResults.errors.length > 0) {
//           console.error(`批次中有 ${batchResults.errors.length} 个文件处理失败`);
//         }

//         // 定期进行数据库插入以释放内存
//         if (allNormalRecords.length >= BATCH_CONFIG.BATCH_INSERT_SIZE * 2) {
//           console.log('\n执行中间批量插入...');
//           const insertedNormal = await batchInsertRecords(allNormalRecords, bhkModel, '');
//           console.log(`中间插入正常记录: ${insertedNormal} 条`);
//           allNormalRecords.length = 0; // 清空数组释放内存
//         }

//         if (allSkipRecords.length >= BATCH_CONFIG.BATCH_INSERT_SIZE) {
//           const insertedSkip = await batchInsertRecords(allSkipRecords, fzbhkskipModel, 'fzzhenduanskips');
//           console.log(`中间插入跳过记录: ${insertedSkip} 条`);
//           allSkipRecords.length = 0; // 清空数组释放内存
//         }

//         // 进度报告
//         const progress = (endIndex / bhkDataFiles.length * 100).toFixed(2);
//         console.log(`总体进度: ${progress}% (${endIndex}/${bhkDataFiles.length}文件)`);

//       } catch (batchError) {
//         console.error(`处理批次 ${i + 1}-${endIndex} 失败:`, batchError);
//         totalErrors++;
//       }
//     }

//     // 最终批量插入剩余数据
//     console.log('\n执行最终批量插入...');

//     const finalNormalInserted = await batchInsertRecords(allNormalRecords, bhkModel, 'fzzhenduan');
//     const finalSkipInserted = await batchInsertRecords(allSkipRecords, fzbhkskipModel, 'fzzhenduanskips');

//     // 统计总结
//     const endTime = Date.now();
//     const duration = ((endTime - startTime) / 1000).toFixed(2);

//     console.log('\n=== 处理完成统计 ===');
//     console.log(`总处理时间: ${duration} 秒`);
//     console.log(`处理文件总数: ${bhkDataFiles.length}`);
//     console.log(`正常数据记录: ${finalNormalInserted} 条`);
//     console.log(`跳过数据记录: ${finalSkipInserted} 条`);
//     console.log(`处理错误数: ${totalErrors}`);
//     console.log(`平均处理速度: ${(bhkDataFiles.length / (duration / 60)).toFixed(2)} 文件/分钟`);

//   } catch (error) {
//     console.error('处理过程中发生严重错误:', error);
//     throw error;
//   }
// }
// main()
//   .then(() => {
//     console.log('执行完毕');
//     process.exit(0);
//   })
//   .catch(error => {
//     console.error('执行失败:', error);
//     process.exit(1);
//   });

// // db.bhkovers.aggregate([
// //   {
// //     $group: {
// //       _id: '$bhkCode', // 根据bhkCode字段分组
// //       doc: { $first: '$$ROOT' }, // 在每个分组中只保留第一个文档
// //     },
// //   },
// //   {
// //     $replaceRoot: { newRoot: '$doc' }, // 将文档替换为我们保留的文档
// //   },
// //   {
// //     $out: 'bhkovers2', // 将结果写回到bhk集合中
// //   },
// // ]);
