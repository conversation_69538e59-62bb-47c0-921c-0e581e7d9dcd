const Service = require('egg').Service;
const path = require('path');
const _ = require('lodash');
const IDValidator = require('id-validator-modify');
const GB2260 = require('id-validator-modify/src/GB2260');
const moment = require('moment');
// general是一个公共库，可用可不用
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
  _addToSet,
  _pull,
} = require(path.join(process.cwd(), 'app/service/general'));


class UserService extends Service {

  async find(payload = {}, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.User, payload, {
      query: _.assign({
        state: '1',
      }, query),
      searchKeys: !_.isEmpty(searchKeys) ? searchKeys : [ 'userName', 'phoneNum', 'email' ],
      populate: !_.isEmpty(populate) ? populate : [{
        path: 'category',
        select: 'name _id',
      }],
      files: files ? files : '-password',
    });
    return listdata;

  }

  async count(params = {}) {
    return _count(this.ctx.model.User, params);
  }

  async create(payload, options) {
    return _create(this.ctx.model.User, payload, options);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.User, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.User, values);
  }

  async update(res, _id, payload, query, options) {
    return _update(res, this.ctx.model.User, _id, payload, query, options);
  }

  async addToSet(res, _id, payload) {
    return _addToSet(res, this.ctx.model.User, _id, payload);
  }

  async pull(res, _id, payload) {
    return _pull(res, this.ctx.model.User, _id, payload);
  }

  async item(res, {
    query = {},
    populate = [],
    files = null,
    options,
  } = {}, returnOptions = {}) {
    return _item(res, this.ctx.model.User, {
      query: _.assign({
        state: '1',
      }, query),
      populate: !_.isEmpty(populate) ? populate : [],
      files: files ? files : '-password',
      options,
    }, returnOptions);
  }

  async findRecomend(payload) {
    const {
      current,
      pageSize,
      searchkey,
      area, // 查询来源
      category,
      sortby,
      follow,
      group,
    } = payload;
    let res = [];
    const queryObj = {};
    let count = 0;
    const skip = ((Number(current)) - 1) * Number(pageSize || 10);


    const sortObj = {
      date: -1,
    };

    if (sortby === '1') {
      sortObj.followers = 1;
    }

    let populateArr = [{
      path: 'category',
      select: 'name _id',
    }];

    if (searchkey) {
      const reKey = new RegExp(searchkey, 'i');
      if (area === '1') {
        queryObj.$or = [{
          userName: {
            $regex: reKey,
          },
        }, {
          phoneNum: {
            $regex: reKey,
          },
        }, {
          email: {
            $regex: reKey,
          },
        }];
      } else {
        queryObj.userName = {
          $regex: reKey,
        };
      }
    }

    if (category) {
      queryObj.category = category;
    }

    if (group) {
      queryObj.group = group;
    }

    // console.log('--ctx.session.user---', ctx.session.user);
    if (follow) {
      if (follow === '1' && this.ctx.session.user) {
        queryObj._id = {
          $in: this.ctx.session.user.watchers,
        };
        populateArr = [{
          path: 'category',
          select: 'name _id',
        }];
      }
    }

    res = await this.ctx.model.User.find(queryObj).skip(skip).limit(Number(pageSize))
      .sort(sortObj)
      .popupate(populateArr)
      .exec();
    count = await this.ctx.model.User.count(queryObj).exec();

    return {

      docs: res,
      pageInfo: {
        totalItems: count,
        pageSize: Number(pageSize),
        current: Number(current),
        searchkey,
      },

    };

  }

  async findRecordPDF(params) {
    const { ctx } = this;
    const res = await ctx.model.ZygrRecord.find({ employeeId: params._id });
    const EnterpriseID = res[0].EnterpriseID;
    // let staticPath = res[0].recordFiles[3].systemGenerate.staticName.split(".")[0] + '.pdf'
    const staticPath = res[0].recordFiles[3].systemGenerate.staticName;
    console.log(staticPath);
    const path = `static/enterprise/EPiEgQQys/${staticPath}`;
    console.log(path);
    // let uploadfilePath = `${app.config.static.prefix}${app.config.upload_http_path}/${EnterpriseID}`

    // let filePath = `${app.config.static.prefix}${app.config.enterprise_http_path}/${EnterpriseID}`
    // console.log(uploadfilePath, filePath)
    return { path, EnterpriseID };
  }

  async uploadSignImage(params) {
    const { ctx } = this;
    const res = await ctx.model.Employee.findOneAndUpdate({ _id: params.employeeId }, { $set: { signatrue: params.filename } }, { new: true });
    console.log(res);
  }

  async closeNotify(params) {
    const { ctx } = this;
    await ctx.model.User.findOneAndUpdate({ _id: params.userId }, { $set: { 'aboutTransfer.isRead': '0' } }, { new: true });
    // 还需更新工作场所里的状态

  }

  async confirmPhysicalExamination(params) {
    if (params.EnterpriseID) {
      const emploeeInfo = await this.ctx.model.Employee.findOne({ EnterpriseID: params.EnterpriseID, IDNum: params.idNo });
      await this.ctx.model.Employee.findOneAndUpdate({ _id: emploeeInfo._id }, { $set: { signatrue: params.filename } }, { new: true });
    }
    const { filename = '' } = params;
    const updateInfo = {
      confirmStatus: true,
    };

    if (filename) {
      // 这里构建需要保存的文件对象
      updateInfo.files = {
        originName: filename,
        staticName: filename,
        fileType: 'physicalExaminationSign',
      };
    }

    const updateQuery = { $set: { confirmStatus: true } };

    if (filename) {
      // 使用 $push 将文件对象推送到 files 数组
      updateQuery.$push = { files: updateInfo.files };
    }

    await this.ctx.model.Suspect.findOneAndUpdate(
      { _id: params._id },
      updateQuery
    );
  }
  // 跟新用户所绑定的企业信息
  async updateCompany(_id, query = {}) {
    query.updateTime = Date.now();
    const res = await this.ctx.model.User.update(
      { _id },
      query,
      { new: true }
    );
    if (res.ok === 1) return true;
  }
  // 根据用户手机号码到Employee匹配相应的企业（前提是这个手机号码已经在user注册成功）
  async findCompanyByPhoneNum(phoneNum) {
    const { ctx } = this;
    if (!phoneNum) {
      return [];
    }
    const employees = await ctx.model.Employee.find(
      { phoneNum, status: 1 },
      { EnterpriseID: true, name: true, IDNum: true, _id: true }
    ).populate('EnterpriseID', 'cname');
    return employees;
  }
  async findEmployee(query) {
    const { ctx } = this;
    const employees = await ctx.model.Employee.find(query);
    return employees;
  }
  async updateEmployee(query = {}, params = {}) {
    const { ctx } = this;
    return await ctx.model.Employee.updateOne(
      query, params
    );
  }
  async updateAdminUser(query = {}, params = {}) {
    const { ctx } = this;
    return await ctx.model.AdminUser.updateOne(
      query, params
    );
  }
  // 根据身份证查询信息
  async findInfoByIDNum(IDNum) {
    const Validator = new IDValidator(GB2260);
    const isValid = Validator.isValid(IDNum);
    if (isValid) {
      const getInfo = Validator.getInfo(IDNum);
      return { code: 200, data: getInfo, errMsg: '' };
    }
    return { code: 201, errMsg: '该身份证号无效，请重新检查' };
  }
  // 绑定微信
  async bindWxInfo(data) {
    const { ctx } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    const res = await ctx.model.User.updateOne({ _id: userId }, { $set: data });
    return res;
  }
  // 获取检测结果
  async getStationsCheckResult(stations, EnterpriseID) {
    const { ctx } = this;
    let checkResultItem = [];
    let model = '';
    let matchOrOne = {};
    let project = {};
    const checkResult = {};
    let modelName = '';
    let station = {};
    let checkProjectFields = {};
    for (let i = 0; i < stations.length; i++) {
      station = stations[i];
      station.name = station.name.replace('岗位', '').trim();
      station.workspace = station.workspace.replace('车间', '').trim();
      for (let j = 0; j < station.harmFactorsAndSort.length; j++) {
        checkProjectFields = {};
        if (station.harmFactorsAndSort[j][0] === '化学') {
          model = 'chemistryFactors';
          modelName = '化学';
          checkProjectFields[model + '.formData.checkProject'] = station.harmFactorsAndSort[j][1].trim();
        } else if (station.harmFactorsAndSort[j][0] === '粉尘') {
          model = 'dustFactors';
          modelName = '粉尘';
          checkProjectFields[model + '.formData.checkProject'] = { $regex: station.harmFactorsAndSort[j][1].trim() };
        } else if (station.harmFactorsAndSort[j][0] === '生物') {
          model = 'biologicalFactors';
          modelName = '生物';
          checkProjectFields[model + '.formData.checkProject'] = station.harmFactorsAndSort[j][1].trim();
        } else {
          if (station.harmFactorsAndSort[j][1].indexOf('噪声') !== -1) {
            model = 'noiseFactors';
            modelName = '噪声';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高温') !== -1) {
            model = 'heatFactors';
            modelName = '高温';
          } else if (station.harmFactorsAndSort[j][1].indexOf('超高频辐射') !== -1) {
            model = 'ultraHighRadiationFactors';
            modelName = '超高频辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高频电磁场') !== -1) {
            model = 'highFrequencyEleFactors';
            modelName = '高频电磁场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('工频电磁场') !== -1) {
            model = 'powerFrequencyElectric';
            modelName = '工频电场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('激光') !== -1) {
            model = 'laserFactors';
            modelName = '激光辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('微波') !== -1) {
            model = 'microwaveFactors';
            modelName = '微博辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('紫外线') !== -1) {
            model = 'ultravioletFactors';
            modelName = '紫外线';
          } else if (station.harmFactorsAndSort[j][1].indexOf('振动') !== -1) {
            model = 'handBorneVibrationFactors';
            modelName = '手传振动';
          } else if (station.harmFactorsAndSort[j][1].indexOf('游离二氧化硅') !== -1) {
            model = 'SiO2Factors';
            modelName = '游离二氧化硅';
          }
        }
        if (model) {
          matchOrOne = {};
          project = {};
          matchOrOne[model + '.formData.station'] = { $regex: station.name };
          matchOrOne[model + '.formData.workspace'] = { $regex: station.workspace };
          project[model] = 1;
          project.jobHealthId = 1;
          project['jobHealth.name'] = 1;
          project['jobHealth.reportTime'] = 1;
          project['jobHealth.year'] = 1;
          checkResultItem = await ctx.model.CheckAssessment.aggregate([
            { $match: { EnterpriseID } },
            {
              $lookup: {
                from: 'jobhealths',
                localField: 'jobHealthId',
                foreignField: '_id',
                as: 'jobHealth',
              },
            },
            { $unwind: '$jobHealth' },
            { $unwind: '$' + model + '.formData' },
            {
              $match: {
                ...checkProjectFields,
                $or: [
                  matchOrOne, {
                    $and: [
                      { [model + '.formData.checkAddress']: { $regex: station.name } },
                      { [model + '.formData.checkAddress']: { $regex: station.workspace } },
                    ],
                  },
                ],
              },
            },
            {
              $project: project,
            },
          ]);
          for (let k = 0; k < checkResultItem.length; k++) {
            if (checkResultItem[k] && checkResultItem[k].jobHealth && checkResultItem[k][model].formData) {
              checkResultItem[k][model].formData.checkTime = checkResultItem[k].jobHealth.reportTime ? moment(new Date(checkResultItem[k].jobHealth.reportTime)).format('YYYY-MM-DD') : '';
              checkResultItem[k][model].formData.checkName = checkResultItem[k].jobHealth.name;
              checkResultItem[k][model].formData.checkProject = checkResultItem[k][model].formData.checkProject || modelName;
              checkResultItem[k][model].formData.checkResult = checkResultItem[k][model].formData.checkResult || checkResultItem[k][model].formData.conclusion;
              checkResultItem[k][model].formData.protectiveFacilities = station.protectiveFacilities;
              if (Object.keys(checkResult).indexOf('' + model) === -1) {
                checkResult[model] = { data: [] };
              }
              checkResult[model].model = model;
              checkResult[model].modelName = modelName;
              checkResult[model].data.push(checkResultItem[k][model].formData);
            }
          }
        }
      }
    }
    return Object.values(checkResult);
  }
  // 获取员工当前岗位
  // 获取员工岗位
  async findEmployeeStation(params) {
    this.ctx.auditLog('劳动者app获取员工岗位参数', `${params}`, 'info');
    this.ctx.auditLog('劳动者员工id', `${params.employeeId}`, 'info');
    try {
      const res = await this.ctx.model.MillConstruction.aggregate([
        {
          $match: {
            EnterpriseID: params.EnterpriseID,
          },
        },
        {
          $unwind: '$children',
        },
        {
          $unwind: '$children.children',
        },
        {
          $match: {
            $or: [{ 'children.children.employees': params.employeeId }, { 'children.children.children.employees': params.employeeId }],
          },
        },
      ]);
      const stations = [];
      res.forEach(item => {
        if (item.category === 'workspaces') {
          item.children.workspace = item.name;
          item.children.harmFactorsAndSort = item.children.harmFactors;
          item.children.harmFactors = item.children.harmFactors.map(item2 => item2[1]).join('、');
          delete item.children.children;
          stations.push(item.children);
        } else if (item.category === 'mill') {
          item.children.children.workspace = item.children.name;
          item.children.children.harmFactorsAndSort = item.children.children.harmFactors;
          item.children.children.harmFactors = item.children.children.harmFactors.map(item2 => item2[1]).join('、');
          delete item.children.children.children;
          stations.push(item.children.children);
        }
      });
      return stations;
    } catch (error) {
      console.log(error);
    }
  }

  async findEmployeeById(params) { // 参数员工id
    try {
      const { ctx } = this;
      const EnterpriseID = params.companyId;
      // 查询所在岗位及危害因素
      const stations = await this.findEmployeeStation({ EnterpriseID, employeeId: params.employeeId }); // 参数企业id和员工id
      ctx.auditLog('劳动者app获取员工岗位', `${stations}`, 'info');
      const checkResult = await this.getStationsCheckResult(stations, EnterpriseID);
      return { stations, checkResult };
    } catch (error) {
      console.log(error);
    }
  }


  async getLastcheckResult(params) {
    try {
      const res = await this.findEmployeeById(params);
      const checkResult = res.checkResult;
      this.ctx.auditLog('劳动者app获取检测个人岗位检测结果', `${checkResult}`, 'info');
      return checkResult;
    } catch (error) {
      this.ctx.auditLog('劳动者app获取检测个人岗位检测结果', `${error}`, 'error');
    }

  }
  async getLastPhysicalData(params) {
    const { ctx } = this;
    try {
      // console.log('劳动者获取最近体检数据：', params);
      // 先写死一个id，查找一个有数据的人员'eCNxQGC5X5T'
      // const result = await ctx.model.Suspect.find({ employeeId: params._id });
      ctx.auditLog('员工id', `${params.employeeId}`, 'info');
      const result = await ctx.model.Suspect.find({
        employeeId: params.employeeId,
      }).sort({
        checkDate: -1,
      });
      this.ctx.auditLog('劳动者获取最近体检数据：', `${result}`, 'info');
      return result;
    } catch (error) {
      this.ctx.auditLog('劳动者app获取检测个人体检结果', `${error}`, 'error');
    }

  }
  async savePersonSign(params) {
    const { ctx } = this;
    try {
      const res = await ctx.model.PersonalTraining.updateOne({
        _id: params.personalTrainingId,
      }, { $set: { sign: params.filename } });
      ctx.auditLog('保存签字到personalTraining表中成功', `${res}`, 'info');
      return res;
    } catch (error) {
      console.log(error, '保存图片到personalTraining表中报错');
      ctx.auditLog('保存图片到personalTraining表中报错', `${error}`, 'error');
    }
  }
  async savePersonVerifyImage(params) { // 第一次
    const { ctx } = this;
    try {
      const res = await ctx.model.PersonalTraining.updateOne({
        _id: params.personalTrainingId,
      },
      { $set: { verifyImage: params.filename } });
      // console.log(res, '保存签字到personalTraining表中成功');
      ctx.auditLog('保存签字到personalTraining表中成功', `${res}`, 'info');
      return res;
    } catch (error) {
      console.log(error, '保存VerifyImage到personalTraining表中报错');
      ctx.auditLog('保存VerifyImage到personalTraining表中成功', `${error}`, 'info');
    }
  }

  async saveExmaVerifyImage(params) { // 第二次
    const { ctx } = this;
    try { // 参数： 试卷id 公司id 劳动者id
      // console.log(params.personalTrainingId, '个人培训id');
      // 通过personTraining表中的bigTestList字段来判断他考了几次
      // console.log(params.EnterpriseID, '考试最终验证');
      // let times = 0;
      // const res = await ctx.model.PersonalTraining.findOne({
      //   EnterpriseID: params.EnterpriseID,
      //   _id: params.personalTrainingId,
      // });
      // if (res && res.bigTestList && res.bigTestList.length) {
      //   times = res.bigTestList.length;
      // }
      // let imageLength = '';
      // if (res) {
      //   imageLength = res.exmaVerifyImage.length;
      // }
      // // console.log(imageLength, '存储过照片吗');
      // if (times || Number(times) === 0) { // 进行考试照片的存储
      //   // console.log('这样真的可以吗');
      //   if (imageLength === times && times === 0) { // 第一次考试第一次验证没有上传过照片，直接push
      //     // console.log('第一次上传照片');
      //     await ctx.model.PersonalTraining.updateOne({
      //       EnterpriseID: params.EnterpriseID,
      //       _id: params.personalTrainingId,
      //     }, { $push: {
      //       exmaVerifyImage: { times, url: params.filename, name: params.filename },
      //     } }
      //     );
      //   } else if (times === 0) { // 第一次考试第一次验证不成功，多次验证，需要重新上传图片，直接将之前的覆盖
      //     // console.log('第一次考试补考上传照片');
      //     await ctx.model.PersonalTraining.updateOne({
      //       EnterpriseID: params.EnterpriseID,
      //       _id: params.personalTrainingId,
      //     },
      //     { $set: {
      //       exmaVerifyImage: { times: 1, url: params.filename, name: params.filename },
      //     } }
      //     );
      //   }
      //   if (imageLength === times && times !== 0) {
      //     // console.log('第多少次上传照片');
      //     await ctx.model.PersonalTraining.updateOne({
      //       EnterpriseID: params.EnterpriseID,
      //       _id: params.personalTrainingId,
      //     }, { $push: {
      //       exmaVerifyImage: { times, url: params.filename, name: params.filename },
      //     } }
      //     );
      //   } else if (times) {
      //     // console.log('第多少次补考上传照片', params.EnterpriseID, params.courseID);
      //     await ctx.model.PersonalTraining.updateOne({
      //       EnterpriseID: params.EnterpriseID,
      //       _id: params.personalTrainingId,
      //     },
      //     { $set: { 'exmaVerifyImage.$[i].url': params.filename, 'exmaVerifyImage.$[i].name': params.filename } }, {
      //       arrayFilters: [{
      //         'i.times': times,
      //       }],
      //     }
      //     );
      //   }
      // }
      // const image = await ctx.model.PersonalTraining.findOne({
      //   EnterpriseID: params.EnterpriseID,
      //   _id: params.personalTrainingId,
      // });
      // console.log(image, 'imnageeeeeeeeeeee');
      // if(image.exmaVerifyImage&&image.exmaVerifyImage.length){

      // }
      // console.log(params, 'ppppppppp');
      let times = 0;
      const res1 = await ctx.model.PersonalTraining.findOne({
        _id: params.personalTrainingId,
      }, { bigTestList: 1, exmaVerifyImage: 1, verifyImage: 1 });
      if (res1 && res1.bigTestList && res1.bigTestList.length) {
        times = res1.bigTestList.length;
      }
      await ctx.model.PersonalTraining.updateOne({ _id: params.personalTrainingId }, { $set: { exmaVerifyImage: { times, url: params.filename, name: params.filename } } });
      const res2 = await ctx.model.PersonalTraining.findOne({
        _id: params.personalTrainingId,
      }, { bigTestList: 1, exmaVerifyImage: 1, verifyImage: 1 });
      const url1 = res2.exmaVerifyImage[res2.exmaVerifyImage.length - 1].url;// 考试照片（第一次考考试）
      // console.log(url1, '让我看看你的路径是啥');
      const url2 = res2.verifyImage; // 培训照片
      const faceRes = await this.service.face.test(params.EnterpriseID, url1, url2);
      // console.log(faceRes, '校验结果');
      ctx.auditLog('校验结果???', faceRes, 'info');
      return faceRes;// 返回识别的结果，如果识别失败，需要重新上传，成功，正常进入考试
    } catch (error) {
      console.log(error, '考试保存图片到personTraing表中报错');
      ctx.auditLog('考试保存图片到personTraing表中报错', `${error}`, 'error');
    }
  }

  // 获取体检机构
  async findAllService(ctx, params) {
    console.log(params);
    const { EnterpriseID } = params;
    // 获取当前账号的地址情况
    const address = await ctx.model.Adminorg.aggregate([
      { $match: { _id: EnterpriseID } },
      { $project: { physicalExaminationOrgID: '$physicalExaminationOrgID', districtRegAdd: '$districtRegAdd' } },
    ]);
    const serverIds = address[0] && address[0].physicalExaminationOrgID || [];
    const res = await ctx.model.PhysicalExamOrg.update({ _id: { $in: address[0].physicalExaminationOrgID } }, { $set: { projectNums: 1 } }, { multi: true });
    console.log(res);
    let result = [];
    let totalLength = '';
    if (params.searchkey) {
      console.log('关键词搜索');
      result = await ctx.model.PhysicalExamOrg.aggregate([
        {
          $lookup: {
            from: 'physicalExamUsers',
            foreignField: '_id',
            localField: 'managers',
            as: 'managers',
          },
        },
        {
          $lookup: {
            from: 'physicalExamDetectionMechanism',
            foreignField: '_id',
            localField: 'qualifies',
            as: 'qualifies',
          },
        },
        { $match: { name: { $regex: params.searchkey } } },
        { $sort: { projectNum: -1 } },
      ]);
      totalLength = await ctx.model.ServiceOrg.aggregate([
        { $match: { name: { $regex: params.searchkey } } },
      ]);
    } else if (params.regAddr) {
      console.log('区域查询');
      result = await ctx.model.PhysicalExamOrg.aggregate([
        {
          $lookup: {
            from: 'physicalExamUsers',
            foreignField: '_id',
            localField: 'managers',
            as: 'managers',
          },
        },
        {
          $lookup: {
            from: 'physicalExamDetectionMechanism',
            foreignField: '_id',
            localField: 'qualifies',
            as: 'qualifies',
          },
        },
        { $match: { regAddr: { $all: params.regAddr } } },
        { $sort: { projectNum: -1 } },
      ]);
      totalLength = await ctx.model.ServiceOrg.aggregate([
        {
          $lookup: {
            from: 'physicalExamUsers',
            foreignField: '_id',
            localField: 'managers',
            as: 'managers',
          },
        },
        {
          $lookup: {
            from: 'physicalExamDetectionMechanism',
            foreignField: '_id',
            localField: 'qualifies',
            as: 'qualifies',
          },
        },
        { $match: { regAddr: { $all: params.regAddr } } },
        { $count: 'totalLength' },
      ]);
    } else {
      console.log('普通渲染', address[0].districtRegAdd);
      result = await ctx.model.PhysicalExamOrg.aggregate([
        {
          $lookup: {
            from: 'physicalExamUsers',
            foreignField: '_id',
            localField: 'managers',
            as: 'managers',
          },
        },
        {
          $lookup: {
            from: 'physicalExamDetectionMechanism',
            foreignField: '_id',
            localField: 'qualifies',
            as: 'qualifies',
          },
        },
        { $sort: { projectNum: -1 } },
      ]);
      totalLength = await ctx.model.PhysicalExamOrg.aggregate([
        { $count: 'totalLength' },
      ]);
    }

    const industryArray = await ctx.service.industryCategory.find({ isPaging: '0' }, { sort: { sort: 1 } });
    result.map(async val => {
      if (val.lineOfBusiness && val.lineOfBusiness instanceof Array) {
        console.log('进入了行业分类转换', val.industryCategory);
        const newArrIndustry = [];
        const buildIndustryArray = await this.buildIndustry(industryArray, val.lineOfBusiness);
        if (buildIndustryArray) {
          newArrIndustry.push(buildIndustryArray.join(' ➤ '));
        }
        val.lineOfBusiness = newArrIndustry;
        console.log(newArrIndustry, '转化后');
      }
    });
    const modelPath = `${this.app.config.static.prefix}/upload/images`;
    await ctx.model.ServiceOrg.updateOne({ _id: { $in: serverIds } }, { $set: { projectNums: '' } }, { multi: true });
    console.log(modelPath);
    return { docs: result || [], pageInfo: { totalItems: totalLength[0] ? totalLength[0].totalLength : 0 }, modelPath };
  }

  // ## 体检预约
  async createReservation(data) {
    // 当前是哪个省
    // 查找当前省乙级的检测机构 和其他省份的甲级检测机构
    const result = await this.ctx.model.PhysicalExamOrg.find({ _id: data.id }).populate('managers');
    console.log(result, '结果的');
    const smsObj = {
      sName: result[0].corp, // 体检机构联系人姓名
      _id: result[0]._id, // 体检机构ID
      cId: data.EnterpriseID, // 体检人所属公司ID
      cname: data.company, // 所属公司名称
      uId: data.uid, // 体检人ID
      name: data.name, // 体检人姓名
      phone: data.phoneNum, // 条件人电话
      servicePhone: result[0].managers[0].phoneNum, // 体检机构电话号码
    };
    const msgContent = `${smsObj.sName}您好,现有企业${smsObj.cname}的${smsObj.name}预约体检服务，请尽快与${smsObj.phone}联系沟通。`;
    console.log(smsObj, '==============');
    this.sendBookingMessage(msgContent, smsObj);
  }

  // ## 体检预约 短信发送
  async sendBookingMessage(msgContent, data) {
    const { ctx, app } = this;
    try {
      // title, message, reader, authorID, authorGroup
      const res = await ctx.service.messageNotification.sendMessage(
        '体检预约提醒',
        msgContent,
        [{
          readerID: data._id,
          readerGroup: 'zHLKFCyXD',
          isRead: 0,
        }],
        data.userId,
        app.config.groupID.userGroupID,
        data.name, // 体检人姓名
        data.cname // 给体检预约使用，发送预约人的所属公司
      );
      return res;
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async findNotices(user) {
    const { ctx } = this;
    const notices = [];
    const employeeInfo = await ctx.model.Employee.findOne({ _id: user.employeeId });

    // 查询是否存在转岗记录
    const employeeStatusChange = await ctx.model.EmployeeStatusChange.aggregate([
      { $match: { employee: user.employeeId } },
      {
        $addFields: {
          statusChanges: {
            $arrayElemAt: [
              '$statusChanges',
              {
                $subtract: [
                  {
                    $size: '$statusChanges',
                  },
                  1,
                ],
              },
            ],
          },
        },
      },
      // { $unwind: '$statusChanges' },
      {
        $match: {
          'statusChanges.EnterpriseID': employeeInfo.EnterpriseID,
          'statusChanges.changType': 2,
          'statusChanges.stationsTo.0': { $exists: true },
          'statusChanges.signStatus': { $ne: true },
        },
      },
      // { $sort: { 'statusChanges.timestamp': -1 } },
      // { $group: { _id: '$_id', statusChanges: { $push: '$statusChanges' } } },
    ]) || [];
    console.log(employeeStatusChange, 'employeeStatusChange');
    const statusChanges = employeeStatusChange[0] ? employeeStatusChange[0].statusChanges : null;
    if (statusChanges) {
      let station = {};
      // for (let i = 0; i < statusChanges.length; i++) {
      let skipCurrent = false;
      if (!statusChanges.stationsTo || statusChanges.stationsTo.length === 0) {

        return [];
      }

      // if (!statusChanges.stationFrom || !statusChanges.stationsTo || statusChanges.stationsTo.length === 0) {
      //   console.log('没有转岗记录');

      //   return [];
      // }
      if (statusChanges.stationsTo) {

        for (let k = 0; k < statusChanges.stationsTo.length; k++) {
          station = await this.service.adminuser.getStationInfo(statusChanges.stationsTo[k], employeeInfo.EnterpriseID);
          if (station) {
            statusChanges.stationsTo[k] = station;
          } else {
            statusChanges.stationsTo[k] = '该岗位已被删除';
            skipCurrent = true;
          }
        }
      }

      if (statusChanges.stationFrom) {


        station = await this.service.adminuser.getStationInfo(statusChanges.stationFrom, employeeInfo.EnterpriseID);
        if (station) {
          statusChanges.stationFrom = station;
        } else {
          statusChanges.stationFrom = '该岗位已被删除';
          skipCurrent = true;
        }
      }

      if (skipCurrent) {
        return [];
      }
      const content = `您已转岗至 ${statusChanges.stationsTo.join('、')}, 请确认签字`;
      notices.push({
        type: 'reorientation',
        content,
        id: statusChanges._id,
        time: moment(statusChanges.timestamp).format('YYYY-MM-DD'),
      });

      // }
    }
    console.log(notices, 'notices');

    return notices;
  }

  async findReorientationInfo({ id, employeeId }) {
    const { ctx } = this;
    const res = await ctx.model.EmployeeStatusChange.aggregate([
      { $match: { employee: employeeId } },
      { $unwind: '$statusChanges' },
      { $match: { 'statusChanges._id': id } },
      {
        $lookup: {
          from: 'employees',
          localField: 'employee',
          foreignField: '_id',
          as: 'employee',
        },
      },
      { $unwind: '$employee' },
    ]);
    const employeeStatusChange = res[0];
    const statusChanges = employeeStatusChange.statusChanges;

    let stationsToInfo = {};

    const stationsTo = statusChanges.stationsTo[0];
    const mills = await this.ctx.model.MillConstruction.aggregate([
      {
        $match: {
          EnterpriseID: statusChanges.EnterpriseID,
        },
      },
      {
        $unwind: '$children',
      },
      {
        $match: {
          $or: [{ 'children._id': stationsTo }, { 'children.children._id': stationsTo }],
        },
      },
    ]);

    console.log(444444, JSON.stringify(mills));

    if (mills.length > 0) {
      if (mills[0].category === 'mill') {
        mills[0].children.children.forEach(item => {
          if (item._id === stationsTo) {
            const stationName = item.name;
            stationsToInfo = {
              station: `${mills[0].name}/${mills[0].children.name}/${stationName}`,
              harmFactors: item.harmFactors.map(e => e[1]),
              protectiveFacilities: item.protectiveFacilities,
            };
          }
        });

      } else {
        stationsToInfo = {
          station: `${mills[0].name}/${mills[0].children.name}`,
          harmFactors: mills[0].children.harmFactors.map(e => e[1]),
          protectiveFacilities: mills[0].children.protectiveFacilities,
        };
      }
    }

    let harmFactors = [];
    if (stationsToInfo.harmFactors && stationsToInfo.harmFactors.length > 0) {
      const res = await ctx.model.PhysicalExamination.aggregate([
        { $match: { keyword: { $in: stationsToInfo.harmFactors } } },
        { $unwind: '$status' },
        { $match: { 'status.statusCode': '在岗' } },
      ]);

      harmFactors = res.map(e => {
        return {
          station: stationsToInfo.station,
          protectiveFacilities: stationsToInfo.protectiveFacilities ? stationsToInfo.protectiveFacilities : '',
          name: e.keyword,
          workDiseases: e.status.workDiseases ? e.status.workDiseases : [], // 可能导致的职业病危害
          forbid: e.status.forbid ? e.status.forbid : [], // 职业禁忌证
        };
      });

    } else {
      harmFactors = [{
        station: stationsToInfo.station,
        protectiveFacilities: stationsToInfo.protectiveFacilities ? stationsToInfo.protectiveFacilities : '',
      }];
    }

    const data = {
      name: employeeStatusChange.employee.name,
      times: moment(statusChanges.timestamp).format('YYYY-MM-DD'),
      content: `${stationsToInfo.station ? '转岗至 ' + stationsToInfo.station : '岗位已被删除'}`,
      harmFactors,
      remarks: statusChanges.changStationReason ? statusChanges.changStationReason : '-',
      detailId: statusChanges._id,
      id: employeeStatusChange._id,
      EnterpriseID: employeeStatusChange.employee.EnterpriseID,
    };

    return data;
  }


  async findAllReorientationInfo({ employeeId }) {
    const { ctx } = this;
    const res = await ctx.model.EmployeeStatusChange.aggregate([
      { $match: { employee: employeeId } },
      { $unwind: '$statusChanges' },
      {
        $lookup: {
          from: 'employees',
          localField: 'employee',
          foreignField: '_id',
          as: 'employee',
        },
      },
      { $unwind: '$employee' },
      { $sort: { 'statusChanges.timestamp': -1 } },
    ]);

    const results = await Promise.all(res.map(async employeeStatusChange => {
      const statusChanges = employeeStatusChange.statusChanges;
      let stationsToInfo = {};

      const stationsTo = statusChanges.stationsTo[0];
      const mills = await this.ctx.model.MillConstruction.aggregate([
        {
          $match: {
            EnterpriseID: statusChanges.EnterpriseID,
          },
        },
        {
          $unwind: '$children',
        },
        {
          $match: {
            $or: [{ 'children._id': stationsTo }, { 'children.children._id': stationsTo }],
          },
        },
      ]);

      if (mills.length > 0) {
        if (mills[0].category === 'mill') {
          mills[0].children.children.forEach(item => {
            if (item._id === stationsTo) {
              const stationName = item.name;
              stationsToInfo = {
                station: `${mills[0].name}/${mills[0].children.name}/${stationName}`,
                harmFactors: item.harmFactors.map(e => e[1]),
                protectiveFacilities: item.protectiveFacilities,
              };
            }
          });
        } else {
          stationsToInfo = {
            station: `${mills[0].name}/${mills[0].children.name}`,
            harmFactors: mills[0].children.harmFactors.map(e => e[1]),
            protectiveFacilities: mills[0].children.protectiveFacilities,
          };
        }
      }

      let harmFactors = [];
      if (stationsToInfo.harmFactors && stationsToInfo.harmFactors.length > 0) {
        const res = await ctx.model.PhysicalExamination.aggregate([
          { $match: { keyword: { $in: stationsToInfo.harmFactors } } },
          { $unwind: '$status' },
          { $match: { 'status.statusCode': '在岗' } },
        ]);

        harmFactors = res.map(e => ({
          station: stationsToInfo.station,
          protectiveFacilities: stationsToInfo.protectiveFacilities || '',
          name: e.keyword,
          workDiseases: e.status.workDiseases || [],
          forbid: e.status.forbid || [],
        }));
      } else {
        harmFactors = [{
          station: stationsToInfo.station,
          protectiveFacilities: stationsToInfo.protectiveFacilities || '',
        }];
      }

      return {
        name: employeeStatusChange.employee.name,
        times: moment(statusChanges.timestamp).format('YYYY-MM-DD'),
        content: `${stationsToInfo.station ? '转岗至 ' + stationsToInfo.station : '岗位已被删除'}`,
        harmFactors,
        remarks: statusChanges.changStationReason || '-',
        detailId: statusChanges._id,
        id: employeeStatusChange._id,
        EnterpriseID: employeeStatusChange.employee.EnterpriseID,
      };
    }));

    return results;
  }

  // 确认
  async confirmReorientation(params) {
    const { ctx, app } = this;
    const nowTime = new Date(); // 当前时间

    // 获取公司公章
    const company = await ctx.model.Adminorg.findOne({ _id: params.EnterpriseID }, { officialSeal: 1 });
    let officialSeal = '';
    if (company && company.officialSeal) {
      const officialSealFilePath = path.join(app.config.enterpriseUpload_path, params.EnterpriseID, company.officialSeal);
      try {
        if (app.config.storageType.includes('oss')) {
          // 如果是oss，先将文件下载到本机
          const target = officialSealFilePath;
          const targetSplit = target.replace(/\\/gi, '/').split('/public/');

          await ctx.helper.fGetObject({
            objectPath: targetSplit[targetSplit.length - 1],
            filePath: officialSealFilePath,
          });
        }
      } catch (error) {
        console.log(error);
      }
      officialSeal = officialSealFilePath;
    }
    try {
      if (app.config.storageType.includes('oss')) {
        // 如果是oss，先将文件下载到本机
        const target = app.config.upload_path + '/' + params.EnterpriseID + '/' + params.filename;
        const targetSplit = target.replace(/\\/gi, '/').split('/public/');

        await ctx.helper.fGetObject({
          objectPath: targetSplit[targetSplit.length - 1],
          filePath: path.join(app.config.upload_path, params.EnterpriseID, params.filename),
        });
      }
    } catch (error) {
      console.log(error);
    }

    // 模板渲染数据
    const data = {
      year: nowTime.getFullYear(),
      month: nowTime.getMonth() + 1,
      day: nowTime.getDate(),
      hazardNotifications: JSON.parse(params.harmFactors),
      officialSeal,
      signPath: path.join(app.config.upload_path, params.EnterpriseID, params.filename),
    };

    // 生成 职业病危害告知书
    // 输出路径
    const outPutPath = path.join(app.config.enterprise_path, params.EnterpriseID);
    const outPutFileName = 'hazardInform_' + Math.random().toString(36).substr(2) + new Date().getTime(); // 采用随机名
    const res = await ctx.helper.fillWord(ctx, '职业病危害告知书_2301.docx', data, outPutPath, outPutFileName, '', params.EnterpriseID);
    const updateData = {
      'statusChanges.$[i].signPath': params.filename,
      'statusChanges.$[i].signStatus': true,
      'statusChanges.$[i].signTime': nowTime,
    };

    if (res && res.staticName) {
      updateData['statusChanges.$[i].hazardInform'] = res.staticName;
    }

    // 更新调岗记录
    await ctx.model.EmployeeStatusChange.updateOne(
      { _id: params.reorientationId },
      { $set: updateData },
      { arrayFilters: [{ 'i._id': params.detailId }] }
    );

    try {
      // 更新工作场所里的待确认
      const EmployeeStatusChangeRes = await ctx.model.EmployeeStatusChange.aggregate([
        {
          $match: {
            _id: params.reorientationId,
          },
        },
        {
          $unwind: '$statusChanges',
        },
        {
          $match: {
            'statusChanges._id': params.detailId,
          },
        },
      ]);
      if (EmployeeStatusChangeRes[0] && EmployeeStatusChangeRes[0].statusChanges && EmployeeStatusChangeRes[0].statusChanges.stationsTo) {
        const stationsTo = EmployeeStatusChangeRes[0].statusChanges.stationsTo[0];
        const mills = await this.ctx.model.MillConstruction.aggregate([
          {
            $match: {
              EnterpriseID: params.EnterpriseID,
            },
          },
          {
            $unwind: '$children',
          },
          {
            $match: {
              $or: [{ 'children._id': stationsTo }, { 'children.children._id': stationsTo }],
            },
          },
        ]);

        if (mills.length > 0) {
          if (mills[0].category === 'mill') {
            mills[0].children.children.forEach(async item => {
              if (item._id === stationsTo) {
                await ctx.model.MillConstruction.update(
                  { _id: mills[0]._id },
                  { $set: { 'children.$[i].children.$[y].children.$[z].isPass': '0' } },
                  {
                    arrayFilters: [
                      { 'i._id': mills[0].children._id },
                      { 'y._id': stationsTo },
                      { 'z.employees': EmployeeStatusChangeRes[0].employee },
                    ],
                  }
                );
              }
            });
          } else {
            await ctx.model.MillConstruction.update(
              { _id: mills[0]._id },
              { $set: { 'children.$[i].children.$[z].isPass': '0' } },
              { arrayFilters: [{ 'i._id': stationsTo }, { 'z.employees': EmployeeStatusChangeRes[0].employee }] }
            );
          }
        }
      }

      // 追加一人一档里的告知书
      await ctx.model.ZygrRecord.updateOne(
        { employeeId: params.employeeId },
        {
          $push: {
            'recordFiles.$[i].clientUpload': {
              sort: '系统生成',
              fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              originName: `职业病危害告知书_${params.detailId}.docx`,
              staticName: res.staticName,
            },
          },
        },
        { arrayFilters: [{ 'i.name': '职业病危害告知书' }] }
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  // 解除预警
  async cancelWarning(params) {
    const { ctx } = this;
    const emploeeInfo = await ctx.model.Employee.findOne({ _id: params.employeeId }).select('name');
    console.log(emploeeInfo, '解除预警');
    const res2 = await ctx.model.Warning.findOne({ jobHealthId: params.employeeId, status: { $ne: 3 } });
    if (res2) {
      await ctx.model.Warning.updateOne({ _id: res2._id },
        {
          status: 3,
          rectifyTime: Date.now(),
          $push: {
            process: {
              time: Date.now(),
              thing: `解除预警 (${emploeeInfo.name})`,
            },
          },
        },
        {
          upsert: true,
          new: true,
        }
      );
    }
  }

  // 获取当前员工的信息
  async getUserInfo(userId) {
    const { ctx } = this;
    try {
      if (!ctx.session.user._id) {
        throw new Error('用户信息缺失');
      }
      const res = await this.ctx.model.User.findOne({
        _id: userId || ctx.session.user._id,
      }).populate('employeeId', 'marriage');
      const obj = JSON.parse(JSON.stringify(res));
      // 计算年龄
      const today = new Date();
      const birthday = new Date(obj.birth);
      let age = today.getFullYear() - birthday.getFullYear();
      const m = today.getMonth() - birthday.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthday.getDate())) {
        age--;
      }
      return { ...obj, age };
    } catch (error) {
      throw error;
    }
  }

  // 个人体检指标趋势
  async indicatorsTrend(params) {
    const { ctx } = this;
    const { employeeId, itmcod } = params;
    if (!employeeId) { throw new Error('员工id不能为空'); }
    if (!itmcod) { throw new Error('指标不能为空'); }

    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 4 + '';
    const endYear = currentYear + 1 + '';

    const pipeline = [
      {
        $match: {
          employeeId,
          checkDate: { $gte: new Date(startYear), $lte: new Date(endYear) },
          'bhkSubList.itmcod': itmcod,
        },
      },
      {
        $project: {
          employeeId: 1,
          checkDate: 1,
          bhkSubList: 1,
        },
      },
      {
        $unwind: {
          path: '$bhkSubList',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'bhkSubList.itmcod': itmcod,
        },
      },
      {
        $sort: {
          checkDate: 1,
        },
      },
      {
        $group: {
          _id: {
            $year: '$checkDate',
          },
          latestDocument: {
            $last: '$$ROOT',
          },
        },
      },
      {
        $replaceRoot: {
          newRoot: '$latestDocument',
        },
      },
      {
        $project: {
          year: {
            $year: '$checkDate',
          },
          msrunt: '$bhkSubList.msrunt',
          rgltag: '$bhkSubList.rgltag',
          result: '$bhkSubList.result',
        },
      },
      {
        $sort: {
          year: 1,
        },
      },
    ];
    // console.log('🍊pipeline', pipeline);
    const res = await ctx.model.Suspect.aggregate(pipeline);
    return res;
  }
}

module.exports = UserService;
