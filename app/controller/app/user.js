const Controller = require('egg').Controller;
const { authToken } = require('@utils');
// const pump = require('pump');
const sha1 = require('sha1');
const jwt = require('jsonwebtoken');
const _ = require('lodash');
const validator = require('validator');
const { siteFunc, validatorUtil, tools } = require('@utils');
const AlipaySdk = require('alipay-sdk').default;
const fs = require('fs');
const qs = require('qs');
const axios = require('axios');
const {
  userRule,
} = require('@validate');
const path = require('path');
const awaitWriteStream = require('await-stream-ready').write;
const moment = require('moment');


// 管道读入一个虫洞。
const sendToWormhole = require('stream-wormhole');
// const WXBizMsgCrypt = require('@utils/msgCrypt/WXBizMsgCrypt');
const WecomEncryptor = require('../../utils/qwxCrypt');
const { Readable } = require('stream');
// const { decrypt } = require('@wecom/crypto');


class AdminController extends Controller {
  async getWorkPlace(ctx) {
    ctx.helper.renderSuccess(ctx, {
      data: ctx.app.config.workplaceName || '',
    });
  }
  async wxWorkAuthAction() {
    const { ctx, app } = this;
    try {
      // 接受企业微信返回的code
      const code = ctx.query.code;
      // const isCodeUsed = await ctx.helper.getCache('QY_WX_CODE_' + code);
      // if (isCodeUsed) {
      //   ctx.auditLog('企业微信认证code已经被使用', `${code}`, 'error');
      //   ctx.helper.renderFail(ctx, {
      //     message: 'code已经使用过',
      //   });
      //   return;
      // }
      ctx.auditLog('企业微信认证code', `${code}`, 'info');
      const { corpid = '', corpsecret = '' } = app.config.sxccQyWxAuth;
      if (!corpid || !corpsecret) {
        ctx.auditLog('企业微信认证配置错误', '企业微信认证配置错误', 'error');
        throw new Error('企业微信认证配置错误');
      }
      ctx.auditLog('企业微信认证corpid', `${corpid}`, 'info');
      ctx.auditLog('企业微信认证corpsecret', `${corpsecret}`, 'info');
      const getAccessTokenRes = await axios.get(
        `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpid}&corpsecret=${corpsecret}`
      );
      const access_token = getAccessTokenRes.data.access_token;
      if (!access_token) {
        ctx.auditLog(
          '企业微信认证获取access_token错误',
          '获取access_token错误',
          'error'
        );
        throw new Error('获取access_token错误');
      }
      ctx.auditLog('企业微信认证access_token', `${access_token}`, 'info');

      const userRes = await axios.get(
        `https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=${access_token}&code=${code}`
      );
      ctx.auditLog(
        '企业微信认证登录人员信息',
        JSON.stringify(userRes.data),
        'info'
      );
      const config = {
        method: 'post',
        url: `https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token=${access_token}`,
        data: {
          user_ticket: userRes.data.user_ticket,
        },
      };
      const userInfo = await axios(config);
      ctx.auditLog(
        '企业微信认证登录人员信息',
        JSON.stringify(userInfo.data),
        'info'
      );
      ctx.auditLog(
        '企业微信认证登录手机号码',
        `${userInfo.data.mobile}`,
        'info'
      );
      if (!userInfo.data.mobile) {
        ctx.auditLog('企业微信认证登录手机号码错误', '手机号码错误', 'error');
        ctx.helper.renderFail(ctx, {
          message: '手机号码错误',
        });
        return;
      }

      // 首先查找用户
      const userInfoZyws = await ctx.model.User.findOne({
        phoneNum: userInfo.data.mobile,
      });

      // 如果找不到用户，抛出错误
      if (!userInfoZyws) {
        throw new Error('暂无权限登录');
      }

      // 如果 qwId 不存在，则更新它
      if (!userInfoZyws.qwId) {
        await ctx.model.User.updateOne(
          { _id: userInfoZyws._id },
          { $set: { qwId: userInfo.data.userid } }
        );
      }

      // 如果头像存在，处理头像
      if (
        userInfo.data.avatar &&
        (!userInfoZyws.logo ||
          userInfoZyws.logo === '/static/upload/images/defaultlogo.png' ||
          !userInfoZyws.logo.includes('static'))
      ) {
        const avatarUrl = userInfo.data.avatar;
        const avatarRes = await axios.get(avatarUrl, {
          responseType: 'arraybuffer',
        });
        const buffer = Buffer.from(avatarRes.data);

        // 创建一个 Readable 流
        const stream = new Readable();
        stream.push(buffer);
        stream.push(null); // 表示流的结束
        // const stream = avatarRes.data;
        const filename =
          Math.random().toString(36).substr(2) + new Date().getTime() + '.jpeg';
        const target = path.join(
          this.config.upload_path,
          userInfoZyws.companyId[0],
          filename
        );
        try {
          await ctx.helper.pipe({
            readableStream: stream,
            target,
          });
        } catch (error) {
          ctx.auditLog(
            '企业微信头像上传失败',
            `${userInfo.data.mobile}`,
            'error'
          );
          throw new Error(error.message);
        }
        const logo = `/static${this.config.upload_http_path}/${userInfoZyws.companyId[0]}/${filename}`;
        await ctx.model.User.updateOne(
          { _id: userInfoZyws._id },
          { $set: { logo } }
        );
      }

      // 生成用户Token
      const userToken = jwt.sign(
        {
          _id: userInfoZyws._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: false,
      }); // cookie 有效期30天

      // 记录登录日志
      const clientIp =
        ctx.header['x-forwarded-for'] ||
        ctx.header['x-real-ip'] ||
        ctx.request.ip;
      const loginLog = {
        type: 'login',
        logs: userInfoZyws.userName + ' login，ip:' + clientIp,
      };

      if (!_.isEmpty(ctx.service.systemOptionLog)) {
        await ctx.service.systemOptionLog.create(loginLog);
      }
      // await ctx.helper.setCache('QY_WX_CODE_' + code, true, 1000 * 60 * 5);

      ctx.auditLog(
        '登陆操作',
        `焦煤企业微信正在通过用户名 ${userInfoZyws.userName} 执行登录。`,
        'info'
      );

      ctx.helper.renderSuccess(ctx, {
        data: {
          user: userInfoZyws,
          userToken: userToken || '',
        },
        message: ctx.__('validate_user_loginOk'),
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
      ctx.auditLog('焦煤企业微信认证登录错误', `${error}`, 'error');
    }
  }

  async validateUrlWxWorkAuthAction() {
    const { ctx, app } = this;
    const {
      corpid = '',
      corptoken = '',
      aes_key = '',
    } = app.config.sxccQyWxAuth;
    if (!corpid || !corptoken || !aes_key) {
      ctx.auditLog('企业微信认证配置错误', '企业微信认证配置错误', 'error');
      throw new Error('企业微信认证配置错误');
    }
    const config = {
      corpId: corpid, // 你的企业ID, 公众号不用,但是解密的思路一样的稍微改下代码,参见 /WXMsgCrypt/pkcs7****.js 第 69 行.
      token: corptoken, // token
      aes_key, // key值
    };
    const wecomEncryptor = new WecomEncryptor(
      config.token,
      config.aes_key,
      config.corpId
    );
    const { msg_signature, timestamp, nonce, echostr } = ctx.request.query;
    const verifiedEchostr = wecomEncryptor.verifyURL(
      msg_signature,
      timestamp,
      nonce,
      echostr
    );

    ctx.auditLog('企业微信认证验证URL', `${verifiedEchostr}`, 'info');
    ctx.body = verifiedEchostr;
  }

  async byCallback() {
    const { ctx, app } = this;
    try {
      let errorMessage = '';
      // 接受keycloak返回的code
      const { api_host, client_id, client_secret, redirect_uri, realm_name } =
        this.config.oidc.keyCloak;
      const code = ctx.query.code;
      console.log('code', code);
      // 通过code获取token
      const data = qs.stringify({
        grant_type: 'authorization_code',
        client_id,
        client_secret,
        redirect_uri,
        code,
      });
      const config = {
        method: 'post',
        url: `${api_host}/realms/${realm_name}/protocol/openid-connect/token`,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data,
      };

      const tokenRes = await axios(config);
      // 解析返回结果
      const { access_token } = qs.parse(tokenRes.data);
      console.log(
        // '🚀 ~ file: home.js:4461 ~ HomeController ~ byCallback ~ access_token:',
        access_token
      );
      const authorization = `Bearer ${access_token}`;
      // 通过token获取用户信息
      const userInfo = await axios.get(
        `${api_host}/realms/${realm_name}/protocol/openid-connect/userinfo`,
        {
          headers: {
            Authorization: authorization,
          },
        }
      );
      console.log(userInfo);
      // const userInfo = { data: { user_name: '********' } };
      // // 根据user_name对应unitCode
      // // 先在adminuser表中查找
      const weUserInfo = await ctx.model.User.findOne({
        userName: userInfo.data.user_name,
      }).lean();
      if (!weUserInfo) {
        if (!weUserInfo) {
          throw new Error('暂无权限登录');
        }
      }
      const userToken = jwt.sign(
        {
          _id: weUserInfo._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: false,
      }); // cookie 有效期30天

      // 记录登录日志
      const clientIp =
        ctx.header['x-forwarded-for'] ||
        ctx.header['x-real-ip'] ||
        ctx.request.ip;
      const loginLog = {
        type: 'login',
        logs: weUserInfo.userName + ' login，ip:' + clientIp,
      };

      if (!_.isEmpty(ctx.service.systemOptionLog)) {
        await ctx.service.systemOptionLog.create(loginLog);
      }

      ctx.auditLog(
        '登陆操作',
        `未知用户正在通过用户名 ${weUserInfo.userName} 执行登录。`,
        'info'
      );
      // weUserInfo = JSON.parse(JSON.stringify(weUserInfo));
      // if (weUserInfo.logo) {
      //   weUserInfo.logo = 'https://' + ctx.request.header.host + weUserInfo.logo;
      // }
      // weUserInfo.password = '';
      // 拿企业微信的ACCESS_TOKEN
      let ACCESS_TOKEN = ctx.helper.getCache('QY_WX_ACCESS_TOKEN');
      console.log('ACCESS_TOKEN', ACCESS_TOKEN, app.config.qyWxAuth);
      if (!ACCESS_TOKEN) {
        const getAccessTokenRes = await axios.get(
          `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${app.config.qyWxAuth.corpid}&corpsecret=${app.config.qyWxAuth.corpsecret}`
        );
        if (getAccessTokenRes.data.errcode === 0) {
          console.log('getAccessTokenRes', getAccessTokenRes.data);
          ctx.helper.setCache(
            'QY_WX_ACCESS_TOKEN',
            getAccessTokenRes.data.access_token,
            +getAccessTokenRes.data.expires_in * 1000
          );
          ACCESS_TOKEN = getAccessTokenRes.data.access_token;
          console.log(
            'ACCESS_TOKEN',
            ACCESS_TOKEN,
            ctx.helper.getCache('QY_WX_ACCESS_TOKEN')
          );
        } else {
          errorMessage = getAccessTokenRes.data.errmsg;
        }
      }
      // 获取jsapi_ticket
      let JSAPI_TICKET = ctx.helper.getCache('QY_WX_JSAPI_TICKET');
      if (!JSAPI_TICKET) {
        const getJsapiTicketRes = await axios.get(
          `https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=${ACCESS_TOKEN}&type=agent_config`
        );
        if (getJsapiTicketRes.data.errcode === 0) {
          // console.log('getJsapiTicketRes', getJsapiTicketRes.data);
          ctx.helper.setCache(
            'QY_WX_JSAPI_TICKET',
            getJsapiTicketRes.data.ticket,
            +getJsapiTicketRes.data.expires_in * 1000
          );
          JSAPI_TICKET = getJsapiTicketRes.data.ticket;
        } else {
          errorMessage = getJsapiTicketRes.data.errmsg;
        }
      }
      const toJSonData = {
        userToken: userToken || null,
        companyStatus: weUserInfo.companyStatus,
        phoneNum: weUserInfo.phoneNum,
      };
      console.log('weUserInfo', toJSonData);
      await ctx.render('/by.html', {
        staticRootPath: this.config.static.prefix,
        status: 200,
        data: JSON.stringify(toJSonData),
        errorMessage,
        corpid: app.config.qyWxAuth.corpid, // 企业ID
        agentid: app.config.qyWxAuth.agentid, // 应用ID
        ticket: JSAPI_TICKET, // 应用ticket
        appid: app.config.qyWxAuth.appid, // 小程序appid
        message: ctx.__('validate_user_loginOk'),
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
      ctx.auditLog('oidc认证登录错误', `${error}`, 'error');
    }
  }
  async whEnter() {
    const { ctx } = this;
    const { api_host, client_id, redirect_uri } = this.app.config.oidc.keyCloak;
    const pages = ctx.query.pages || 'admin/dashboard';
    // 环境变量去掉
    try {
      const finalRedirectUri = encodeURIComponent(`${redirect_uri}?type=pc&pages=${pages}`);
      const toUrl = `${api_host}/esc-sso/oidc/authorize?response_type=code&client_id=${client_id}&redirect_uri=${finalRedirectUri}&oauth_timestamp=${new Date().getTime()}`;
      ctx.redirect(toUrl);
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async whH5Enter() {
    const { ctx } = this;
    const { api_host, client_id, redirect_uri } = this.app.config.oidc.keyCloak;
    const pages = ctx.query.pages || 'pages/login/login';
    // 环境变量去掉
    try {
      const finalRedirectUri = encodeURIComponent(`${redirect_uri}?type=app&pages=${pages}`);
      const toUrl = `${api_host}/esc-sso/oidc/authorize?response_type=code&client_id=${client_id}&redirect_uri=${finalRedirectUri}&oauth_timestamp=${new Date().getTime()}`;
      ctx.redirect(toUrl);
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async whCallback() {
    const { ctx, app } = this;
    try {
      // 接受keycloak返回的code
      const {
        api_host,
        client_id,
        client_secret,
        redirect_uri,
        qy_callback_host,
      } = this.config.oidc.keyCloak;
      const code = ctx.query.code;
      const type = ctx.query.type;
      const pages = ctx.query.pages;
      if (type === 'pc' || !type) {
        return await ctx.redirect(`${qy_callback_host}?code=${code}&type=pc&pages=${pages}`);
      }
      // 通过code获取token
      const data = qs.stringify({
        grant_type: 'authorization_code',
        client_id,
        client_secret,
        redirect_uri,
        code,
      });
      const config = {
        method: 'post',
        //         https://地址:端口/ esc-sso/oidc/accessToken?grant_type=authorization_code
        // &oauth_timestamp=[oauth_timestamp]&client_id=[clientId]&client_secret=[clientSecret]
        // &code=[code]&redirect_uri=[appRedirectUrl]
        // url: `${api_host}/realms/${realm_name}/protocol/openid-connect/token`,
        url: `${api_host}/esc-sso/oidc/accessToken?grant_type=authorization_code&oauth_timestamp=${new Date().getTime()}&client_id=${client_id}&client_secret=${client_secret}&code=${code}&redirect_uri=${redirect_uri}`,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data,
      };
      const tokenRes = await axios(config);
      // 解析返回结果
      const { access_token } = qs.parse(tokenRes.data);
      const authorization = `Bearer ${access_token}`;
      // 通过token获取用户信息
      const userInfo = await axios.get(
        // https://地址:端口/ esc-sso/oidc/profile?access_token=[token]
        `${api_host}/esc-sso/oidc/profile?access_token=${access_token}`,
        {
          headers: {
            Authorization: authorization,
          },
        }
      );
      console.log(userInfo);
      // const userInfo = { data: { user_name: '********' } };
      // // 根据user_name对应unitCode
      // // 先在adminuser表中查找
      const weUserInfo = await ctx.model.User.findOne({
        _id: userInfo.data.account_no,
      }).lean();
      if (!weUserInfo) {
        if (!weUserInfo) {
          throw new Error('暂无权限登录');
        }
      }
      const userToken = jwt.sign(
        {
          _id: weUserInfo._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: false,
      }); // cookie 有效期30天

      // 记录登录日志
      const clientIp =
        ctx.header['x-forwarded-for'] ||
        ctx.header['x-real-ip'] ||
        ctx.request.ip;
      const loginLog = {
        type: 'login',
        logs: weUserInfo.userName + ' login，ip:' + clientIp,
      };

      if (!_.isEmpty(ctx.service.systemOptionLog)) {
        await ctx.service.systemOptionLog.create(loginLog);
      }

      ctx.auditLog(
        '登陆操作',
        `未知用户正在通过用户名 ${weUserInfo.userName} 执行登录。`,
        'info'
      );
      const toJSonData = {
        userToken: userToken || null,
        companyStatus: weUserInfo.companyStatus,
        phoneNum: weUserInfo.phoneNum,
      };
      return await ctx.redirect(
        `${this.config.oidc.h5Host}/#/${pages}?data=${JSON.stringify(
          toJSonData
        )}`
      );
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
      ctx.auditLog('oidc认证登录错误', `${error}`, 'error');
    }
  }
  async sendVerificationCode(ctx) {
    try {
      console.log('发送验证码11111111');
      const { config, ctx, app } = this;
      const fields = ctx.query || {};
      const phoneNum = fields.phoneNum.trim();
      const captchaId = fields.captchaId;
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      const {
        showImgCode,
      } = systemConfigs[0];
      if (showImgCode) {
        // 验证滑动验证码是否通过
        if (!captchaId) {
          ctx.helper.renderFail(ctx, {
            message: '请先完成滑动验证',
          });
          return;
        }
        // 从Redis获取验证码信息
        const captchaInfoStr = await app.redis.get(`captcha:${captchaId}`);
        if (!captchaInfoStr) {
          ctx.helper.renderFail(ctx, {
            message: '验证码已过期，请重新验证',
          });
          return;
        }

        const captchaInfo = JSON.parse(captchaInfoStr);
        if (!captchaInfo.verified) {
          ctx.helper.renderFail(ctx, {
            message: '请先完成滑动验证',
          });
          return;
        }
      }
      console.log(phoneNum);
      const { isGroupBranch = '0' } = config;
      console.log('isGroupBranch', isGroupBranch);
      if (isGroupBranch === '1') {
        const isUser = await ctx.model.User.findOne({
          phoneNum,
        });
        console.log('isUser', isUser);
        if (!isUser) {
          ctx.helper.renderFail(ctx, {
            message: '验证码发送失败，请稍后重试',
          });
          return;
        }
      }
      const countryCode = fields.countryCode;
      const messageType = fields.messageType;

      let cacheKey = '';
      let errMsg = '';
      const clientIp =
        ctx.header['x-forwarded-for'] ||
        ctx.header['x-real-ip'] ||
        ctx.request.ip;
      const ipKey = `sendCodeIp:${clientIp}`;
      const phoneKey = `sendCodePhone:${phoneNum}`;
      const ipLastSendTime = await app.redis.get(ipKey);
      const phoneLastSendTime = await app.redis.get(phoneKey);
      const currentTime = Date.now();

      if (
        (ipLastSendTime && currentTime - ipLastSendTime < 59000) ||
        (phoneLastSendTime && currentTime - phoneLastSendTime < 59000)
      ) {
        errMsg = '发送验证码过于频繁，请稍后再试';
      }
      const ipSendCountKey = `sendCodeIpCount:${clientIp}`;
      const phoneSendCountKey = `sendCodePhoneCount:${phoneNum}`;
      const ipSendCount = (await app.redis.get(ipSendCountKey)) || 0;
      const phoneSendCount = (await app.redis.get(phoneSendCountKey)) || 0;

      if (ipSendCount >= 3 || phoneSendCount >= 3) {
        errMsg = '发送验证码次数过多，请10分钟后再试';
      }
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      if (!messageType) {
        errMsg = ctx.__('validate_error_params');
      }

      switch (messageType) {
        case '0': {
          cacheKey = '_sendMessage_reg_';
          break;
        }
        case '1': {
          // 登录
          cacheKey = '_sendMessage_login_';
          break;
        }
        case '7': {
          // 修改手机号
          // 校验现在账号的手机号和需要发送短信的手机号是否为同一个
          const _id =
            fields._id ||
            (ctx.session.user
              ? ctx.session.user._id
              : (
                await authToken.checkToken(
                  ctx.cookies.get('admin_' + app.config.auth_cookie_name),
                  app.config.encrypt_key,
                  ctx
                )
              )._id);
          const options = {
            returnOptions: {
              phoneNum: {
                returnPlaintext: true, // 返回明文密码
              },
            },
          };
          const user = await ctx.service.user.item(
            ctx,
            {
              query: { _id },
              files: this.getAuthUserFields('login'),
            },
            options
          );
          if (user.phoneNum === phoneNum) {
            ctx.helper.renderFail(ctx, {
              message: '对不起！此为原手机号',
            });
            return;
          }
          cacheKey = '_sendMessage_update_';
          break;
        }
        default: {
          errMsg = ctx.__('validate_error_params');
          break;
        }
      }

      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }
      // 删除已使用的滑动验证码信息，防止重复使用
      await app.redis.del(`captcha:${captchaId}`);
      await app.redis.set(ipKey, currentTime, 'EX', 600);
      await app.redis.set(phoneKey, currentTime, 'EX', 600);
      await app.redis.set(ipSendCountKey, ipSendCount + 1, 'EX', 600);
      await app.redis.set(phoneSendCountKey, phoneSendCount + 1, 'EX', 600);

      // 生成短信验证码
      const currentStr = siteFunc.randomString(6, '123456789');

      const endStr = countryCode + phoneNum;
      const currentKey = config.session_secret + cacheKey + endStr;
      console.log(currentStr, '---currentKey---', currentKey);
      await app.redis.set(currentKey, currentStr, 'EX', 900); // 验证码缓存15分钟
      // 发送短消息
      await ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'verificationCode',
          TemplateParam: JSON.stringify({ code: currentStr.toString() }),
          PhoneNumbers: phoneNum,
        },
      });
      console.log(currentStr);

      ctx.helper.renderSuccess(ctx, {
        message: ctx.__('restful_api_response_success', [
          ctx.__('user_action_tips_sendMessage'),
        ]),
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async getSystemConfig() {
    const { ctx } = this;
    try {
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      const {
        showImgCode,
      } = systemConfigs[0];
      ctx.helper.renderSuccess(ctx, {
        data: {
          showImgCode,
        },
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: '获取失败',
      });
    }
  }

  async loginAction() {
    const { ctx, app, config } = this;
    try {
      const fields = ctx.request.body || {};
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      const {
        showImgCode,
      } = systemConfigs[0];
      if (showImgCode) {
        const captchaId = fields.captchaId;
        // 验证滑动验证码是否通过
        if (!captchaId) {
          ctx.helper.renderFail(ctx, {
            message: '请先完成滑动验证',
          });
          return;
        }
        // 从Redis获取验证码信息
        const captchaInfoStr = await app.redis.get(`captcha:${captchaId}`);
        if (!captchaInfoStr) {
          ctx.helper.renderFail(ctx, {
            message: '验证码已过期，请重新验证',
          });
          return;
        }
        const captchaInfo = JSON.parse(captchaInfoStr);
        if (!captchaInfo.verified) {
          ctx.helper.renderFail(ctx, {
            message: '请先完成滑动验证',
          });
          return;
        }
        // 删除已使用的滑动验证码信息，防止重复使用
        await app.redis.del(`captcha:${captchaId}`);
      }

      // let errMsg = '';
      // if (showImgCode && (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)) {
      //   errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      // }

      // if (errMsg) {
      //   ctx.helper.renderFail(ctx, {
      //     message: errMsg,
      //   });
      //   return;
      // }
      // 获取该IP的登录失败次数
      const forwardedIps = (ctx.header['x-forwarded-for'] || '') // x-forwarded-for 可能包含逗号分隔的 IP 链,需要取第一个有效 IP
        .split(',')
        .map(ip => ip.trim());
      const clientIp =
        ctx.request.ip || // Egg.js 的 ctx.request.ip 已经处理了代理服务器的请求头，通常可以直接使用。
        ctx.header['x-real-ip'] ||
        forwardedIps[0];
      const failCountKey = `loginFailCount:${clientIp}`;
      const lockTimeKey = `loginLockTime:${clientIp}`;
      const failCount = await ctx.helper.getCache(failCountKey);
      const lockTime = await ctx.helper.getCache(lockTimeKey);
      // 如果该IP被锁定，返回锁定信息
      if (failCount >= 5) {
        if (lockTime && Date.now() < lockTime) {
          ctx.helper.renderFail(ctx, {
            status: 403,
            message: ctx.__('validate_ip_locked'),
          });
          return;
        }
        // 超过锁定时间，清除锁定状态
        await ctx.helper.delCache(failCountKey);
        await ctx.helper.delCache(lockTimeKey);

      }
      const formObj = {
        userName: fields.userName.trim(),
      };

      ctx.validate(
        userRule.login(ctx),
        Object.assign({}, formObj, {
          password: fields.password,
        })
      );
      let user = await ctx.service.user.item(ctx, {
        query: formObj,
        files: this.getAuthUserFields('login'),
      });

      if (!_.isEmpty(user)) {
        const userPsd = user.password;
        const originPwd = fields.password;
        let hashPassword = fields.password;
        const passwordEncryptionAlgorithm =
          user.passwordEncryptionAlgorithm || '';

        if (passwordEncryptionAlgorithm && fields.password.length < 20) {
          const status = await ctx.helper.verifySm3(
            originPwd,
            passwordEncryptionAlgorithm,
            userPsd
          );
          if (status) {
            hashPassword = userPsd;
            this.ctx.auditLog(
              `HMAC校验成功${passwordEncryptionAlgorithm}:${status}:${hashPassword}:${userPsd}`,
              'HMAC校验成功',
              'info'
            );
          }
        } else {
          console.log('进来了猫猫们');
          hashPassword = ctx.helper.hashSha256(
            fields.password,
            config.salt_sha2_key
          );
        }
        if (userPsd !== hashPassword) {
          // console.log('🍊到这了没');
          // 登录失败，增加失败计数
          let currentFailCount = failCount ? parseInt(failCount) : 0;
          currentFailCount++;
          await ctx.helper.setCache(failCountKey, currentFailCount, 60 * 60 * 1000); // 设置 1 小时过期
          if (currentFailCount >= 5) {
            // 失败5次，锁定IP
            await ctx.helper.setCache(
              lockTimeKey,
              Date.now() + 30 * 60 * 1000,
              60 * 60 * 1000
            ); // 锁定30分钟
          }

          ctx.helper.renderFail(ctx, {
            status: 401,
            message: ctx.__('validate_login_notSuccess_1'),
          });
          return;
        }

        if (!user.enable) {
          ctx.helper.renderFail(ctx, {
            status: 401,
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );

        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: false,
        }); // cookie 有效期30天

        // 记录登录日志
        const clientIp =
          ctx.header['x-forwarded-for'] ||
          ctx.header['x-real-ip'] ||
          ctx.request.ip;
        const loginLog = {
          type: 'login',
          logs: user.userName + ' login，ip:' + clientIp,
        };

        if (!_.isEmpty(ctx.service.systemOptionLog)) {
          await ctx.service.systemOptionLog.create(loginLog);
        }

        ctx.auditLog(
          '登陆操作',
          `未知用户正在通过用户名 ${formObj.userName} 执行登录。`,
          'info'
        );
        user = JSON.parse(JSON.stringify(user));
        if (user.logo) {
          user.logo = 'http://' + ctx.request.header.host + user.logo;
        }
        user.password = '';
        ctx.body = {
          status: 200,
          data: user,
          userToken: userToken || '',
          message: ctx.__('validate_user_loginOk'),
        };
      } else {
        // console.log('🍊到这了没2');
        // 登录失败，增加失败计数
        let currentFailCount = failCount ? parseInt(failCount) : 0;
        currentFailCount++;
        await ctx.helper.setCache(failCountKey, currentFailCount, 60 * 60 * 1000); // 设置 1 小时过期
        if (currentFailCount >= 5) {
          // 失败3次，锁定IP
          await ctx.helper.setCache(
            lockTimeKey,
            Date.now() + 30 * 60 * 1000,
            60 * 60 * 1000
          ); // 锁定30分钟
        }

        ctx.helper.renderFail(ctx, {
          status: 401,
          message: ctx.__('validate_login_notSuccess_1'),
        });
      }
    } catch (err) {
      console.log(1231231, err);
      ctx.helper.renderFail(ctx, {
        status: 401,
        message: err,
      });
    }
  }

  async smLoginAction(ctx) {
    try {
      const { app, ctx } = this;
      const fields = ctx.request.body || {};
      const messageCode = fields.messageCode.trim(),
        phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode,
        type = fields.type,
        cacheKey = '_sendMessage_login_';
      let errMsg = '';
      if (!phoneNum || !validatorUtil.checkPhoneNum(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      const params = countryCode + phoneNum;
      const currentCode = await app.redis.get(
        app.config.session_secret + cacheKey + params
      );

      if (
        !messageCode ||
        !validator.isNumeric(messageCode.toString()) ||
        messageCode.length !== 6
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      if (
        !currentCode ||
        !validator.isNumeric(currentCode.toString()) ||
        currentCode.length !== 6
      ) {
        errMsg = '对不起！验证码错误！请重新发送验证码！';
      }

      if (currentCode !== messageCode) {
        errMsg = '对不起！验证码错误！';
      }

      if (errMsg) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: errMsg,
          status: 401,
        });
        return;
      }

      const queryUserObj = {
        $or: [
          {
            phoneNum,
          },
          {
            phoneNum: '0' + phoneNum,
          },
        ],
        // countryCode,
      };
      const userCount = await ctx.service.user.count(queryUserObj);
      if (userCount > 0) {
        const options = {
          returnOptions: {
            phoneNum: {
              returnPlaintext: true, // 返回明文密码
            },
          },
        };
        let user = await ctx.service.user.item(
          ctx,
          {
            query: queryUserObj,
            files: this.getAuthUserFields('login'),
          },
          options
        );
        if (!user) {
          user = await ctx.model.User.findOne(
            queryUserObj,
            this.getAuthUserFields('login')
          );
        }
        if (!user.enable) {
          ctx.helper.clearCache(params, cacheKey);
          ctx.helper.renderFail(ctx, {
            status: 401,
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );

        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '登陆操作',
          `未知用户正在通过手机号 ${phoneNum} 执行登录。`,
          'info'
        );
        user = JSON.parse(JSON.stringify(user));
        if (user.logo) {
          user.logo = 'http://' + ctx.request.header.host + user.logo;
        }
        user.password = '';
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_loginOk'),
        });
      } else {

        if (type === 'not_regsiter') {
          ctx.helper.renderFail(ctx, {
            status: 401,
            message: '当前手机号未注册',
          });
          return;
        }

        const userObj = {
          userName: phoneNum,
          countryCode,
          phoneNum,
        };
        const user = await ctx.service.user.create(userObj);
        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );

        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '注册操作',
          `用户通过手机号 ${userObj.phoneNum} 注册成功并登录。`,
          'info'
        );
        // 返回数据
        if (user.logo) {
          user.logo = 'http://' + ctx.request.header.host + user.logo;
        }
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_regOk'),
        });
      }
    } catch (err) {
      // console.log(err)
      ctx.helper.renderFail(ctx, {
        status: 401,
        message: err,
      });
    }
  }
  // 专家入驻 注册
  async expertRegAction(ctx) {
    try {
      const { app, ctx } = this;
      const fields = ctx.request.body || {};
      const messageCode = fields.messageCode.trim(),
        phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode || '86',
        name = fields.name.trim(),
        cacheKey = '_sendMessage_login_';
      let errMsg = '';
      if (!phoneNum || !validatorUtil.checkPhoneNum(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      const params = countryCode + phoneNum;
      const currentCode = await app.redis.get(
        app.config.session_secret + cacheKey + params
      );

      if (
        !messageCode ||
        !validator.isNumeric(messageCode.toString()) ||
        messageCode.length !== 6
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      if (
        !currentCode ||
        !validator.isNumeric(currentCode.toString()) ||
        currentCode.length !== 6
      ) {
        errMsg = '对不起！验证码错误！请重新发送验证码！';
      }

      if (currentCode !== messageCode) {
        errMsg = '对不起！验证码错误！看看是不是验证码写错了？';
      }

      if (errMsg) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      const queryUserObj = {
        $or: [{ phoneNum }, { phoneNum: '0' + phoneNum }],
        // countryCode,
      };
      const userCount = await ctx.service.user.count(queryUserObj);
      if (userCount === 1) {
        const user = await ctx.service.user.item(ctx, {
          query: queryUserObj,
          files: 'name role expertApplyDate expertReviewDate',
        });
        if (user.expertApplyDate && !user.expertReviewDate) {
          return ctx.helper.renderFail(ctx, {
            message: '您的账号正在审核中，请问重复提交。',
          });
        }
        if (user.role === 'expert') {
          return ctx.helper.renderFail(ctx, {
            message: '您的账号已申请专家入驻，请勿重复提交。',
          });
        }
        await ctx.service.user.update(ctx, user._id, {
          enable: true,
          name,
          expertApplyDate: new Date(),
        });
        // user = await ctx.service.user.item(ctx, {
        //   query: { _id: user._id },
        //   files: this.getAuthUserFields('login'),
        // });

        // const userToken = jwt.sign({
        //   _id: user._id,
        // }, app.config.encrypt_key, {
        //   expiresIn: '1day',
        // });

        // ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        //   path: '/',
        //   maxAge: app.config.adminUserMaxAge,
        //   signed: true,
        //   httpOnly: true,
        // }); // cookie 有效期1天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '专家入驻注册',
          `用户${name}正在通过手机号 ${phoneNum} 执行专家入驻申请。`,
          'info'
        );
        // user = JSON.parse(JSON.stringify(user));
        // if (user.logo) user.logo = 'http://' + ctx.request.header.host + user.logo;
        user.password = '';
        ctx.helper.renderSuccess(ctx, {
          // data: {
          //   user,
          //   userToken: userToken || '',
          // },
          message: '专家入驻申请成功，请等待审核结果。',
        });
      } else if (userCount === 0) {
        const userObj = {
          userName: phoneNum,
          countryCode,
          phoneNum,
          expertApplyDate: new Date(),
          name,
        };
        const user = await ctx.service.user.create(userObj);
        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: '1day',
          }
        );

        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期1天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '专家入驻注册',
          `用户${name}通过手机号 ${userObj.phoneNum} 注册成功。`,
          'info'
        );
        // 返回数据
        if (user.logo) {
          user.logo = 'http://' + ctx.request.header.host + user.logo;
        }
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: '专家入驻注册成功，请等待审核结果。',
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '您的手机账号异常，请联系在线客服。',
        });
      }
    } catch (err) {
      // console.log(err)
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 发送注册验证码
  async sendVerificationCodeRegister(ctx) {
    try {
      console.log('发送验证码11111111');
      const { config, ctx, app } = this;
      const fields = ctx.query || {};
      const phoneNum = fields.phoneNum.trim();
      const captchaId = fields.captchaId;
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      const {
        showImgCode,
      } = systemConfigs[0];
      if (showImgCode) {
        // 验证滑动验证码是否通过
        if (!captchaId) {
          ctx.helper.renderFail(ctx, {
            message: '请先完成滑动验证',
          });
          return;
        }
        // 从Redis获取验证码信息
        const captchaInfoStr = await app.redis.get(`captcha:${captchaId}`);
        if (!captchaInfoStr) {
          ctx.helper.renderFail(ctx, {
            message: '验证码已过期，请重新验证',
          });
          return;
        }

        const captchaInfo = JSON.parse(captchaInfoStr);
        if (!captchaInfo.verified) {
          ctx.helper.renderFail(ctx, {
            message: '请先完成滑动验证',
          });
          return;
        }
      }
      console.log(phoneNum);
      const { isGroupBranch = '0' } = config;
      console.log('isGroupBranch', isGroupBranch);
      if (isGroupBranch === '1') {
        const isUser = await ctx.model.User.findOne({
          phoneNum,
        });
        console.log('isUser', isUser);
        if (isUser) {
          ctx.helper.renderFail(ctx, {
            message: '当前手机号码已注册',
          });
          return;
        }
      }
      const countryCode = fields.countryCode;
      const messageType = fields.messageType;

      let cacheKey = '';
      let errMsg = '';
      const clientIp =
        ctx.header['x-forwarded-for'] ||
        ctx.header['x-real-ip'] ||
        ctx.request.ip;
      const ipKey = `sendCodeIp:${clientIp}`;
      const phoneKey = `sendCodePhone:${phoneNum}`;
      const ipLastSendTime = await app.redis.get(ipKey);
      const phoneLastSendTime = await app.redis.get(phoneKey);
      const currentTime = Date.now();

      if (
        (ipLastSendTime && currentTime - ipLastSendTime < 59000) ||
        (phoneLastSendTime && currentTime - phoneLastSendTime < 59000)
      ) {
        errMsg = '发送验证码过于频繁，请稍后再试';
      }
      const ipSendCountKey = `sendCodeIpCount:${clientIp}`;
      const phoneSendCountKey = `sendCodePhoneCount:${phoneNum}`;
      const ipSendCount = (await app.redis.get(ipSendCountKey)) || 0;
      const phoneSendCount = (await app.redis.get(phoneSendCountKey)) || 0;

      if (ipSendCount >= 3 || phoneSendCount >= 3) {
        errMsg = '发送验证码次数过多，请10分钟后再试';
      }
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      if (!messageType) {
        errMsg = ctx.__('validate_error_params');
      }

      switch (messageType) {
        case '0': {
          cacheKey = '_sendMessage_reg_';
          break;
        }
        case '1': {
          // 登录
          cacheKey = '_sendMessage_login_';
          break;
        }
        case '7': {
          // 修改手机号
          // 校验现在账号的手机号和需要发送短信的手机号是否为同一个
          const _id =
            fields._id ||
            (ctx.session.user
              ? ctx.session.user._id
              : (
                await authToken.checkToken(
                  ctx.cookies.get('admin_' + app.config.auth_cookie_name),
                  app.config.encrypt_key,
                  ctx
                )
              )._id);
          const options = {
            returnOptions: {
              phoneNum: {
                returnPlaintext: true, // 返回明文密码
              },
            },
          };
          const user = await ctx.service.user.item(
            ctx,
            {
              query: { _id },
              files: this.getAuthUserFields('login'),
            },
            options
          );
          if (user.phoneNum === phoneNum) {
            ctx.helper.renderFail(ctx, {
              message: '对不起！此为原手机号',
            });
            return;
          }
          cacheKey = '_sendMessage_update_';
          break;
        }
        default: {
          errMsg = ctx.__('validate_error_params');
          break;
        }
      }

      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }
      // 删除已使用的滑动验证码信息，防止重复使用
      await app.redis.del(`captcha:${captchaId}`);
      await app.redis.set(ipKey, currentTime, 'EX', 600);
      await app.redis.set(phoneKey, currentTime, 'EX', 600);
      await app.redis.set(ipSendCountKey, ipSendCount + 1, 'EX', 600);
      await app.redis.set(phoneSendCountKey, phoneSendCount + 1, 'EX', 600);

      // 生成短信验证码
      const currentStr = siteFunc.randomString(6, '123456789');

      const endStr = countryCode + phoneNum;
      const currentKey = config.session_secret + cacheKey + endStr;
      console.log(currentStr, '---currentKey---', currentKey);
      await app.redis.set(currentKey, currentStr, 'EX', 900); // 验证码缓存15分钟
      // 发送短消息
      await ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'verificationCode',
          TemplateParam: JSON.stringify({ code: currentStr.toString() }),
          PhoneNumbers: phoneNum,
        },
      });
      console.log(currentStr);

      ctx.helper.renderSuccess(ctx, {
        message: ctx.__('restful_api_response_success', [
          ctx.__('user_action_tips_sendMessage'),
        ]),
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 注册
  async register(ctx) {
    console.log('register');
    try {
      const { app, ctx } = this;
      const fields = ctx.request.body || {};
      console.log(fields);
      const messageCode = fields.messageCode.trim(),
        phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode,
        cacheKey = '_sendMessage_login_';
      let errMsg = '';
      if (!phoneNum || !validatorUtil.checkPhoneNum(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      const params = countryCode + phoneNum;
      const currentCode = await app.redis.get(
        app.config.session_secret + cacheKey + params
      );

      if (
        !messageCode ||
        !validator.isNumeric(messageCode.toString()) ||
        messageCode.length !== 6
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      if (
        !currentCode ||
        !validator.isNumeric(currentCode.toString()) ||
        currentCode.length !== 6
      ) {
        errMsg = '对不起！验证码错误！请重新发送验证码！';
      }

      if (currentCode !== messageCode) {
        errMsg = '对不起！验证码错误！';
      }

      if (errMsg) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: errMsg,
          status: 401,
        });
        return;
      }

      const queryUserObj = {
        $or: [
          {
            phoneNum,
          },
          {
            phoneNum: '0' + phoneNum,
          },
        ],
        // countryCode,
      };
      const userCount = await ctx.service.user.count(queryUserObj);
      if (userCount > 0) {
        const options = {
          returnOptions: {
            phoneNum: {
              returnPlaintext: true, // 返回明文密码
            },
          },
        };
        let user = await ctx.service.user.item(
          ctx,
          {
            query: queryUserObj,
            files: this.getAuthUserFields('login'),
          },
          options
        );
        if (!user) {
          user = await ctx.model.User.findOne(
            queryUserObj,
            this.getAuthUserFields('login')
          );
        }
        if (!user.enable) {
          ctx.helper.clearCache(params, cacheKey);
          ctx.helper.renderFail(ctx, {
            status: 401,
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );

        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '登陆操作',
          `未知用户正在通过手机号 ${phoneNum} 执行登录。`,
          'info'
        );
        user = JSON.parse(JSON.stringify(user));
        if (user.logo) {
          user.logo = 'http://' + ctx.request.header.host + user.logo;
        }
        user.password = '';
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_loginOk'),
        });
      } else {
        const userObj = {
          userName: phoneNum,
          countryCode,
          phoneNum,
          company: fields.company,
          province: fields.province,
          city: fields.city,
        };
        const user = await ctx.service.user.create(userObj);
        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );

        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '注册操作',
          `用户通过手机号 ${userObj.phoneNum} 注册成功并登录。`,
          'info'
        );
        // 返回数据
        if (user.logo) {
          user.logo = 'http://' + ctx.request.header.host + user.logo;
        }
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_regOk'),
        });
      }
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        status: 401,
        message: err,
      });
    }
  }

  // 绑定手机号
  async bindPhoneNum() {
    const { app, ctx } = this;
    const fields = ctx.request.body || {};
    const appid = app.config.wxAutho.appid;
    let phoneNum = '';
    try {
      if (fields.phoneNum) {
        phoneNum = fields.phoneNum.trim();
        const messageCode = fields.messageCode.trim(),
          countryCode = fields.countryCode,
          cacheKey = '_sendMessage_update_';
        let errMsg = '';
        if (!phoneNum || !validatorUtil.checkPhoneNum(phoneNum.toString())) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_phoneNum'),
          ]);
        }

        if (!countryCode) {
          errMsg = ctx.__('validate_selectNull', [
            ctx.__('label_user_countryCode'),
          ]);
        }

        const params = countryCode + phoneNum;
        const currentCode = await app.redis.get(
          app.config.session_secret + cacheKey + params
        );

        if (
          !messageCode ||
          !validator.isNumeric(messageCode.toString()) ||
          messageCode.length !== 6
        ) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_imageCode'),
          ]);
        }

        if (
          !currentCode ||
          !validator.isNumeric(currentCode.toString()) ||
          currentCode.length !== 6
        ) {
          errMsg = '对不起！验证码错误！请重新发送验证码！';
        }

        if (currentCode !== messageCode) {
          errMsg = '对不起！验证码错误！看看是不是验证码写错了？';
        }

        if (errMsg) {
          // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
          // ctx.helper.clearCache(params, cacheKey);
          ctx.helper.renderFail(ctx, {
            message: errMsg,
          });
          return;
        }
      } else {
        // // 去掉了session_key,所以获取session_key
        const res = await ctx.curl('https://api.weixin.qq.com/cgi-bin/token', {
          data: {
            grant_type: 'client_credential',
            appid,
            secret: app.config.wxAutho.secret,
          },
          dataType: 'json',
        });
        if (res.data && res.data.access_token) {
          // 有了res从里面拿token 就可以通过 https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=ACCESS_TOKEN
          const res2 = await ctx.curl(
            'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=' +
            res.data.access_token,
            {
              method: 'POST',
              dataType: 'json',
              data: {
                code: fields.code,
              },
              headers: {
                'content-type': 'application/json',
              },
            }
          );
          if (res2.data.errcode === 0) {
            phoneNum = res2.data.phone_info.phoneNumber;
          } else {
            ctx.auditLog('微信授权登录', '通过微信接口获取手机号失败', 'error');
            ctx.helper.renderFail(ctx, {
              message: '获取手机号失败',
            });
            return;
          }
        } else {
          ctx.auditLog('微信授权登录', '从微信获取access_token失败', 'error');
          ctx.helper.renderFail(ctx, {
            message: '获取access_token失败',
          });
          return;
        }
      }

      const user = await ctx.service.user.item(ctx, {
        query: { phoneNum },
        files: this.getAuthUserFields('login'),
      });
      let userToken = '';
      let newUserInfo = {};
      if (user) {
        const userInfo = await ctx.service.user.item(ctx, {
          query: { _id: fields._id },
        });
        // userInfo.phoneNum = phoneNum;
        const userInfo2 = {
          phoneNum,
          wx: userInfo.wx,
          city: userInfo.city,
          province: userInfo.province,
          country: userInfo.country,
          countryCode: userInfo.countryCode,
          gender: userInfo.gender,
          alipay: userInfo.alipay,
        };

        // delete userInfo2._id;
        // delete userInfo2.id;
        // delete userInfo2.companyStatus;
        delete userInfo2.companyStatus;
        newUserInfo = await ctx.model.User.findOneAndUpdate(
          { phoneNum },
          { $set: userInfo2 },
          { new: true }
        );
        await ctx.model.User.deleteOne({ _id: fields._id });
        userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );
      } else {
        newUserInfo = await ctx.model.User.findOneAndUpdate(
          { _id: fields._id },
          { $set: { phoneNum } },
          { new: true }
        );
        userToken = jwt.sign(
          {
            _id: fields._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );
      }

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      ctx.body = {
        status: 200,
        message: '绑定成功',
        userToken,
        data: newUserInfo,
      };
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 企业微信绑定手机号
  async qyWxBindPhoneNum() {
    const { ctx, app } = this;
    const { encryptedData, iv, userid } = ctx.request.body || {};
    const WXBizDataCrypt = require('../../utils/WXBizDataCrypt.js');

    const session_key = ctx.helper.getCache(`session_key_${userid}`);
    console.log('参数', encryptedData, iv, userid, session_key);
    const appid = app.config.wxAutho.appid;
    const pc = new WXBizDataCrypt(appid, session_key);
    const data = pc.decryptData(encryptedData, iv);

    console.log('解密出来的数据', data);
    if (!data || !data.mobile) {
      return ctx.helper.renderFail(ctx, {
        message: '授权失败',
      });
    }

    // data.mobile = '13116841683'
    const phoneNum = data.mobile;
    // 根据手机号找对应的user
    const user = await ctx.service.user.item(ctx, {
      query: { phoneNum },
      files: this.getAuthUserFields('login'),
    });

    console.log('user：', user);
    // 如果找到user就返回token，否则就让用户自己去登录
    if (user) {
      const updateData = {
        'wx.userId': userid,
        'wx.corpid': app.config.qywxAutho.corpid,
      };
      // 如果用户没有绑定企业，就自动给绑定上
      if (!user.companyId || user.companyId.length === 0) {
        const employee = await ctx.model.Employee.findOne({
          _id: user.employeeId,
        });
        if (employee) {
          updateData.companyId = [ employee.EnterpriseID ];
        }
      }
      const newUserInfo = await ctx.model.User.findOneAndUpdate(
        { phoneNum },
        { $set: updateData },
        { new: true }
      );

      const userToken = jwt.sign(
        {
          _id: user._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      ctx.body = {
        status: 200,
        message: '绑定成功',
        userToken,
        data: newUserInfo,
      };
    } else {
      ctx.body = { status: 500, message: '请登陆' };
    }
  }

  // 微信公众号授权
  async wxH5AuthAction() {
    const { ctx, app } = this;
    try {
      const params = ctx.request.body;
      // 通过code或取access_token
      const res = await ctx.curl(
        'https://api.weixin.qq.com/sns/oauth2/access_token',
        {
          data: {
            appid: app.config.h5WXAuth.appid,
            secret: app.config.h5WXAuth.secret,
            code: params.code,
            grant_type: 'authorization_code',
          },
          dataType: 'json',
        }
      );
      console.log('token===', res);
      if (res.data.errcode) {
        console.log(res.data.errmsg);
      } else {
        const resData = res.data;
        // 通过access_token和openid拉取用户信息
        let userInfo = await ctx.curl(
          'https://api.weixin.qq.com/sns/userinfo',
          {
            data: {
              access_token: resData.access_token,
              openid: resData.openid,
              lang: 'zh_CN',
            },
            dataType: 'json',
          }
        );
        userInfo = userInfo.data;
        if (userInfo.errcode) {
          console.log(userInfo.errmsg);
        } else {
          console.log(userInfo);
          const userRawData = {
            wx: {
              unionid: userInfo.unionid,
              nickName: userInfo.nickname,
              logo: userInfo.headimgurl,
            },
            gender: userInfo.sex - 1,
            city: userInfo.city,
            province: userInfo.province,
            country: userInfo.country,
            countryCode: '86',
          };
          await this.loginActionCommon(
            { 'wx.unionId': userInfo.unionId },
            userRawData
          );
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 福州小游戏微信静默授权
  async fzxyxAuthAction() {
    const { ctx, app } = this;
    const params = ctx.request.body;
    try {
      // ctx.auditLog('小游戏静默授权=====', `code:${params.code}, gameEventId:${params.gameEventId}`, 'info');
      const res = await ctx.curl(
        'https://api.weixin.qq.com/sns/oauth2/access_token',
        {
          data: {
            appid: app.config.wxAuth.appid,
            secret: app.config.wxAuth.secret,
            code: params.code,
            grant_type: 'authorization_code',
          },
          dataType: 'json',
        }
      );
      // ctx.auditLog('小游戏静默授权获取到openid=====', `${res.data.openid}`, 'info');
      if (res.data.openid) {
        const user = await ctx.service.gameUser.getGameUser(
          params.gameEventId,
          res.data.openid
        );
        const gameEvent = await ctx.service.gameEvents.getOne({
          _id: params.gameEventId,
        });
        // ctx.auditLog('小游戏静默授权获取成功=====', '返回结果', 'info');
        ctx.body = {
          status: 200,
          data: {
            user,
            openid: res.data.openid,
            userToken: JSON.stringify(this.config.fzxyx_token),
            gameEventInfo: {
              // 活动信息
              name: gameEvent.name,
              totalTopicNum: gameEvent.totalTopicNum, // 总题数
              getPrizeChanceNum: gameEvent.getPrizeChanceNum, // 获取抽奖机会数
              everyTimeWrongNum: gameEvent.everyTimeWrongNum, // 每次允许错题数
              answerTimeLimit: gameEvent.answerTimeLimit, // 答题时间限制
              gameRule: gameEvent.gameRule, // 游戏规则描述
              isCaptcha: gameEvent.isCaptcha, // 是否开启人机验证
            },
            // heart: 3 - GameRecordCount,
          },
          message: 'success',
        };
      } else {
        ctx.body = {
          status: 500,
          message: '获取openid失败',
        };
      }
      return;
    } catch (error) {
      console.log(error);
    }
  }

  // 获取游戏信息
  async getGameEventInfo(ctx) {
    try {
      const gameEventId = ctx.query.gameEventId;
      if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
      const gameEvent = await ctx.service.gameEvents.getOne({ _id: gameEventId });
      if (!gameEvent) throw new Error('未找到活动信息, gameEventId:' + gameEventId);
      ctx.helper.renderSuccess(ctx, {
        data: {
          _id: gameEvent._id,
          name: gameEvent.name,
          startTime: moment(gameEvent.startTime).format('YYYY-MM-DD HH:mm'),
          endTime: moment(gameEvent.endTime).format('YYYY-MM-DD HH:mm'),
          status: gameEvent.status, // 活动状态 0: 未开始 1: 进行中 2: 已结束
          gameRule: gameEvent.gameRule, // 游戏规则
          prizeRule: gameEvent.prizeRule, // 抽奖规则
          needWinnerInfo: gameEvent.needWinnerInfo, // 是否需要获奖者信息
          winnerInfo: gameEvent.winnerInfo, // 获奖者需填写的信息
          organizer: gameEvent.organizer || '', // 举办方
          isCaptcha: gameEvent.isCaptcha || false, // 是否开启人机验证
        },
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  // 微信授权登录注册
  async authLoginAction() {
    const { app, ctx } = this;
    let session_key = '';
    const fields = ctx.request.body || {};
    const WXBizDataCrypt = require('../../utils/WXBizDataCrypt.js');

    const appid = app.config.wxAutho.appid,
      secret = app.config.wxAutho.secret,
      userRawData = {
        wx: {
          nickName: fields.rawData.nickName,
          logo: fields.rawData.avatarUrl,
        },
        gender: fields.rawData.gender - 1,
        city: fields.rawData.city,
        province: fields.rawData.province,
        country: fields.rawData.country,
        countryCode: '86',
      };
    try {
      let unionId = '';
      let openId = '';
      if (fields.unionId) {
        console.log('参数===', fields);
        unionId = fields.unionId;
      } else {
        // 通过code调用腾讯接口获取openid）
        const res = await ctx.curl(
          'https://api.weixin.qq.com/sns/jscode2session',
          {
            data: {
              appid,
              secret,
              js_code: fields.info.code,
              grant_type: 'authorization_code',
            },
            dataType: 'json',
          }
        );
        session_key = res.data.session_key;
        if (res.status === 200 && res.data.unionid) {
          // 可以直接通过jscode2session获取unionId
          unionId = res.data.unionid;
          openId = res.data.openid;
          userRawData.wx.unionId = unionId;
          userRawData.wx.openId = openId;
          await this.wxloginActionCommon(
            { 'wx.unionId': unionId },
            userRawData
          );
        } else {
          // 对比签名
          console.log('sessionkey2323232', session_key);
          const signature2 = sha1(fields.info.decryRawData + session_key);
          console.log(signature2, fields.info.signature);
          if (fields.info.signature !== signature2) {
            console.log('授权失败');
            return ctx.helper.renderFail(ctx, {
              message: '授权失败',
            });
          }
          // 通过解密获取unoinid
          // 解密
          const pc = new WXBizDataCrypt(appid, session_key);
          const data = pc.decryptData(
            fields.info.encryptedData,
            fields.info.iv
          );
          unionId = data.unionId;
          userRawData.wx.unionId = unionId;
          userRawData.wx.openId = data.openId;
          console.log('解密后====', data);
          await this.wxloginActionCommon(
            { 'wx.unionId': unionId },
            userRawData
          );
          // // 通过解密获取unoinid
          // // 解密
          // const pc = new WXBizDataCrypt(appid, session_key);
          // const data = pc.decryptData(fields.info.encryptedData, fields.info.iv);
          // unionId = data.unionId;
          // userRawData.wx.unionId = unionId;
          // userRawData.wx.openId = data.openId;
          // console.log('解密后====', data);
          // await this.wxloginActionCommon({ 'wx.unionId': unionId }, userRawData);
        }
      }
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 获取企业微信token凭据
  async qyWXGetToken() {
    const { ctx, app } = this;
    const corpid = app.config.qywxAutho ? app.config.qywxAutho.corpid : '';
    const corpsecret = app.config.qywxAutho
      ? app.config.qywxAutho.corpsecret
      : '';
    const res = await ctx.curl(
      `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpid}&corpsecret=${corpsecret}`,
      {
        dataType: 'json',
      }
    );
    if (res && res.data && res.data.access_token) {
      ctx.helper.setCache(
        'qyWXToken',
        res.data.access_token,
        res.data.expires_in * 1000
      );
      return res.data.access_token;
    }
  }
  // 企业微信登陆
  async authLoginQYWx() {
    const { app, ctx } = this;
    const { code } = ctx.request.body;

    // 判断缓存是否存在qyWX的token
    let access_token = ctx.helper.getCache('qyWXToken');
    if (!access_token) {
      console.log('token失效');
      access_token = await this.qyWXGetToken();
    }

    console.log('access_token', access_token);

    if (!access_token) {
      // 获取access_token失败
      ctx.auditLog('企业微信登录', '从微信获取access_token失败', 'error');
      ctx.helper.renderFail(ctx, {
        message: '获取access_token失败',
      });
      return;
    }

    // 获取用户对应的sessionKey
    const jscode2sessionRes = await ctx.curl(
      'https://qyapi.weixin.qq.com/cgi-bin/miniprogram/jscode2session',
      {
        data: {
          access_token,
          js_code: code,
          grant_type: 'authorization_code',
        },
        dataType: 'json',
        method: 'get',
      }
    );

    if (
      jscode2sessionRes &&
      jscode2sessionRes.status === 200 &&
      jscode2sessionRes.data
    ) {
      const userid = jscode2sessionRes.data.userid; // 企业微信的jscode2session返回的是userid，而微信返回的是openid
      const session_key = jscode2sessionRes.data.session_key; // 密钥
      ctx.helper.setCache(`session_key_${userid}`, session_key, 72000);

      const user = await ctx.service.user.item(ctx, {
        query: { 'wx.userId': userid },
        files: this.getAuthUserFields('login'),
      });
      console.log('userTarget', user);
      // 用户存在的话，直接登陆返回token
      if (user) {
        if (!user.enable) {
          return ctx.helper.renderFail(ctx, {
            message: ctx.__('validate_user_forbiden'),
          });
        }

        const userToken = jwt.sign(
          {
            _id: user._id,
          },
          app.config.encrypt_key,
          {
            expiresIn: app.config.jwtUserExpiresIn,
          }
        );
        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天
        // ctx.auditLog('登陆操作', `未知用户正在通过手机号 ${phoneNum} 执行登录。`);
        ctx.body = {
          data: user,
          userToken,
          status: 200,
          message: ctx.__('validate_user_loginOk'),
        };
        return;
      }
      // 用户不存在的话，返回前端去授权手机号
      ctx.body = {
        userid,
        status: 200,
      };
      return;
    }
    // 获取session失败
    ctx.auditLog('企业微信登录', 'code2Session失败', 'error');
    ctx.helper.renderFail(ctx, {
      message: '临时登录凭证校验code2Session失败',
    });
    return;
  }

  // 支付宝授权登陆注册
  async alipayAuthLoginAction() {
    const path = require('path');
    const { app, ctx } = this;
    const fields = ctx.request.body || {};
    let appId = '';
    let privateKey = '';
    if (fields.isH5) {
      appId = app.config.alipayAutho.h5_appid;
      privateKey = fs.readFileSync(
        path.join(__dirname, '../../utils/alipayRSA/private-key-h5.txt'),
        'ascii'
      );
    } else {
      appId = app.config.alipayAutho.mp_appid;
      privateKey = fs.readFileSync(
        path.join(__dirname, '../../utils/alipayRSA/private-key.txt'),
        'ascii'
      );
    }
    const userRawData = {
      countryCode: '86',
      alipay: {
        nickName: fields.rawData.nickName,
        logo: fields.rawData.avatarUrl,
      },
    };
    try {
      let alipayUnionId = '';
      if (fields.userId) {
        alipayUnionId = fields.userId;
      } else {
        // 获取签名
        // 普通公钥模式
        const alipaySdk = new AlipaySdk({
          // 参考下方 SDK 配置
          appId,
          privateKey,
        });
        // mby2iuN_M

        // 通过签名获取accesstoken
        const res = await alipaySdk.exec('alipay.system.oauth.token', {
          grant_type: 'authorization_code',
          code: fields.info.code,
        });
        console.log(res);
        alipayUnionId = res.userId || '';
        await alipaySdk.exec('alipay.user.info.share', {
          auth_token: res.accessToken,
        });
      }
      userRawData.alipay.unionId = alipayUnionId;
      await this.loginActionCommon(
        { 'alipay.unionId': alipayUnionId },
        userRawData
      );
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 登录公共方法
  async wxloginActionCommon(unionIdObj, userRawData) {
    const { ctx, app } = this;
    const userCount = Object.values(unionIdObj)[0]
      ? await ctx.service.user.count(unionIdObj)
      : [];
    if (userCount > 0) {
      const user = await ctx.service.user.item(ctx, {
        query: unionIdObj,
        files: this.getAuthUserFields('login'),
      });
      if (Object.keys(unionIdObj)[0] === 'wx.unionId') {
        if (!user.wx.openId) {
          await ctx.service.user.update(ctx, user._id, {
            'wx.openId': userRawData.wx.openId,
          });
        }
      }
      if (!user.enable) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_user_forbiden'),
        });
        return;
      }
      const userToken = jwt.sign(
        {
          _id: user._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );
      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      // ctx.auditLog('登陆操作', `未知用户正在通过手机号 ${phoneNum} 执行登录。`);
      ctx.body = {
        data: user,
        userToken,
        status: 200,
        message: ctx.__('validate_user_loginOk'),
      };
    } else {
      const user = await ctx.service.user.create(userRawData);
      const userToken = jwt.sign(
        {
          _id: user._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      ctx.body = {
        status: 200,
        data: user,
        userToken: userToken || '',
        message: ctx.__('validate_user_regOk'),
      };
    }
  }
  // 登录公共方法
  async loginActionCommon(unionIdObj, userRawData, session_key = '') {
    // alipayUnionId
    const { ctx, app } = this;
    const userCount = Object.values(unionIdObj)[0]
      ? await ctx.service.user.count(unionIdObj)
      : [];
    if (userCount > 0) {
      const user = await ctx.service.user.item(ctx, {
        query: unionIdObj,
        files: this.getAuthUserFields('login'),
      });
      if (Object.keys(unionIdObj)[0] === 'wx.unionId') {
        if (!user.wx.openId) {
          await ctx.service.user.update(ctx, user._id, {
            'wx.openId': userRawData.wx.openId,
          });
        }
      }
      if (!user.enable) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_user_forbiden'),
        });
        return;
      }
      const userToken = jwt.sign(
        {
          _id: user._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );
      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      // ctx.auditLog('登陆操作', `未知用户正在通过手机号 ${phoneNum} 执行登录。`);
      ctx.body = {
        data: user,
        userToken,
        status: 200,
        session_key: session_key || '',
        message: ctx.__('validate_user_loginOk'),
      };
    } else {
      const user = await ctx.service.user.create(userRawData);
      const userToken = jwt.sign(
        {
          _id: user._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      ctx.body = {
        status: 200,
        data: user,
        userToken: userToken || '',
        session_key: session_key || '',
        message: ctx.__('validate_user_regOk'),
      };
    }
  }

  async regAction() {
    const { config, ctx, app } = this;
    try {
      const fields = ctx.request.body || {},
        countryCode = fields.countryCode,
        messageCode = fields.messageCode.trim();
      let phoneNum = fields.phoneNum,
        password = fields.password,
        confirmPassword = fields.confirmPassword,
        errMsg = '';

      if (!phoneNum || !validatorUtil.checkPhoneNum(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }

      if (!password || !confirmPassword) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_password') ]);
      }

      phoneNum = phoneNum.trim();
      const endStr = countryCode + phoneNum;
      const currentCode = await app.redis.get(
        config.session_secret + '_sendMessage_reg_' + endStr
      );
      if (
        !validator.isNumeric(messageCode.toString()) ||
        messageCode.length !== 6 ||
        currentCode !== messageCode
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }

      password = password.trim();
      confirmPassword = confirmPassword.trim();
      if (confirmPassword !== password) {
        errMsg = '对不起！密码验证错误！';
      }

      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      const userObj = {
        userName: phoneNum,
        countryCode,
        phoneNum,
        password,
      };

      const queryUserObj =
        phoneNum.indexOf('0') !== '0'
          ? {
            $or: [
              {
                phoneNum,
              },
              {
                phoneNum: '0' + phoneNum,
              },
            ],
          }
          : {
            $or: [
              {
                phoneNum,
              },
              {
                phoneNum: phoneNum.substr(1),
              },
            ],
          };

      const user = await ctx.service.user.item(ctx, {
        query: queryUserObj,
      });
      if (!_.isEmpty(user)) {
        ctx.helper.clearCache(endStr, '_sendMessage_reg_');
        ctx.helper.renderFail(ctx, {
          message: '您好！此手机号已经注册过了！',
        });
        return;
      }

      const newUser = await ctx.service.user.create(userObj);
      const userToken = jwt.sign(
        {
          _id: newUser._id,
        },
        app.config.encrypt_key,
        {
          expiresIn: app.config.jwtUserExpiresIn,
        }
      );

      ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: false,
      }); // cookie 有效期30天

      // 重置验证码
      ctx.helper.clearCache(endStr, '_sendMessage_reg_');
      ctx.auditLog(
        '注册操作',
        `用户通过用户名 ${userObj.phoneNum} 密码注册并登录成功。`,
        'info'
      );
      if (newUser) delete newUser.password;
      ctx.helper.renderSuccess(ctx, {
        data: newUser,
        message: ctx.__('validate_user_regOk'),
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 修改用户名或密码
  async updateInfo() {
    const { ctx, app } = this;
    let {
      password,
      phoneNum,
      countryCode,
      messageCode,
      name,
      nickName,
      logo,
      introduction,
      forTrainrole,
      idNo,
    } = ctx.request.body;
    if (!countryCode) countryCode = '86';
    let errMsg = '';
    const _id = ctx.session.user._id;
    const updateData = {}; // 最终需要修改的数据
    if (name) updateData.name = name;
    if (nickName) updateData.nickName = nickName;
    if (logo) updateData.logo = logo;
    if (introduction) updateData.introduction = introduction.trim();
    if (forTrainrole) updateData.forTrainrole = forTrainrole;
    if (idNo) {
      const userId = ctx.session.user._id;
      const userDetail = await ctx.model.User.findOne(
        { _id: userId },
        { employeeId: 1, companyId: 1, idNo: 1 }
      );
      if (!userDetail) {
        ctx.helper.renderFail(ctx, {
          message: 'userId找不到',
        });
      }
      if ((userDetail.idNo && userDetail.idNo !== idNo) || !userDetail.idNo) {
        const employeeId = userDetail.employeeId;
        const idNo = ctx.request.body.idNo ? ctx.request.body.idNo.trim() : '';
        const count = await ctx.model.User.count({ idNo });
        if (count) {
          return ctx.helper.renderFail(ctx, {
            message: '该身份证号已经被人使用',
            data: idNo,
          });
        }
        await ctx.model.User.updateOne({ _id: userId }, { idNo });
        await ctx.model.AdminUser.updateOne({ userId }, { IDcard: idNo });
        if (employeeId) {
          await ctx.model.Employee.updateOne(
            { _id: employeeId },
            { IDNum: idNo }
          );
        }
      }
    }
    if (password) {
      password = password.trim();
      if (password.indexOf('$') === -1) {
        updateData.password = password;
        // await ctx.service.user.update(ctx, _id, {
        //   password,
        // });
        // ctx.helper.renderSuccess(ctx, {
        //   message: `修改 ${ctx.__('restful_api_response_success', [ ctx.__('lc_password') ])}`,
        // });
      } else {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_error_params'),
        });
      }
    }
    if (app.config.branch === 'gx') {
      updateData.companyName = ctx.request.body.companyName || '';
    }
    if (phoneNum && messageCode && countryCode) {
      phoneNum = phoneNum.trim();
      messageCode = messageCode.trim();
      countryCode = countryCode.trim();
      const cacheKey = '_sendMessage_update_';
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }
      const queryUserObj = {
        $or: [
          {
            phoneNum,
          },
          {
            phoneNum: '0' + phoneNum,
          },
        ],
        // countryCode,
      };
      const userCount = await ctx.model.User.find(queryUserObj, {
        phoneNum: 1,
        name: 1,
      });
      if (
        userCount.length > 1 ||
        (userCount.length === 1 && userCount[0]._id !== _id)
      ) {
        errMsg = '该手机号已被使用！';
      }
      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }
      // 验证码校验
      if (messageCode !== app.config.defaultMessageCode) {
        if (
          !messageCode ||
          !validator.isNumeric(messageCode.toString()) ||
          messageCode.length !== 6
        ) {
          errMsg = ctx.__('validate_inputCorrect', [
            ctx.__('label_user_imageCode'),
          ]);
          ctx.helper.renderFail(ctx, {
            message: errMsg,
          });
          return;
        }
        const params = countryCode + phoneNum;
        const currentCode = await app.redis.get(
          app.config.session_secret + cacheKey + params
        );
        if (
          !currentCode ||
          !validator.isNumeric(currentCode.toString()) ||
          currentCode.length !== 6
        ) {
          errMsg = '对不起！手机号错误！';
        }
        if (currentCode !== messageCode) {
          // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
          // ctx.helper.clearCache(params, cacheKey);
          ctx.helper.renderFail(ctx, {
            message: '对不起！验证码错误！看看是不是验证码写错了？',
          });
          return;
        }
      }
      updateData.phoneNum = phoneNum;
      // await ctx.service.user.update(ctx, _id, {
      //   phoneNum,
      // });
      // ctx.helper.renderSuccess(ctx, {
      //   status: 200,
      //   message: `修改 ${ctx.__('restful_api_response_success', [ ctx.__('label_user_phoneNum') ])}`,
      // });
    }
    // 开始更新数据
    await ctx.service.user.update(ctx, _id, updateData);
    ctx.helper.renderSuccess(ctx, {
      message: '信息修改成功',
      data: updateData,
    });
  }

  async logOutAction() {
    const { ctx, app } = this;
    ctx.auditLog('退出登录', '当前用户进行了退出登录操作。', 'info');
    ctx.session = null;
    // 获取当前token
    const token =
      ctx.cookies.get('admin_' + app.config.auth_toolscookie_name) ||
      ctx.get('Authorization');

    // 如果存在token,加入黑名单
    if (token) {
      await ctx.helper.setCache(
        `token_blacklist_${token}`,
        true,
        app.config.jwtUserExpiresIn * 1000
      );
    }

    ctx.cookies.set('admin_' + app.config.auth_cookie_name, null);
    ctx.helper.renderSuccess(ctx, { message: '退出登录成功' });
  }

  getAuthUserFields(type = '') {
    let fieldStr =
      'id enable _id email userName logo phoneNum name companyId company idNo companyStatus employeeId forTrainrole companyName ';
    if (type === 'login') {
      fieldStr =
        'employeeId enable password _id email userName logo phoneNum name companyId company idNo companyStatus employeeId role forTrainrole wx companyName passwordEncryptionAlgorithm';
    } else if (type === 'base') {
      fieldStr =
        'id userName name password  group logo date enable state phoneNum idNo companyStatus employeeId company countryCode email watchers followers comments idNo favorites favoriteCommunityContent despises comments profession experience industry introduction birth gender forTrainrole companyName';
    } else if (type === 'session') {
      fieldStr =
        'id userName name password  group logo date enable state phoneNum companyStatus company employeeId countryCode watchers followers praiseContents praiseMessages praiseCommunityContent watchSpecials watchCommunity watchTags favorites favoriteCommunityContent despises despiseMessage despiseCommunityContent idNo position gender vip email comments forTrainrole companyName';
    }
    return fieldStr;
  }

  // 上传留言图片
  async uploadCommentImage() {
    const { ctx, app } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    const parts = ctx.multipart({ autoFields: true });
    let part;
    while ((part = await parts()) != null) {
      if (!part.filename) {
        // 注意如果没有传入直接返回
        continue;
      }
      console.log(
        'file: ' + part.filename,
        part.encoding,
        part.mime,
        parts.field
      );
      if (!userId) return ctx.helper.renderFail(ctx, { message: '用户未登录' });
      const uploadPath = `${app.config.upload_path}/comment_files/${userId}`;
      const writePath = path.join(uploadPath, `/${part.filename}`);
      console.log('写入文件：', writePath);
      if (!fs.existsSync(`${app.config.upload_path}/comment_files`)) {
        fs.mkdirSync(`${app.config.upload_path}/comment_files`);
      }
      if (!fs.existsSync(uploadPath)) fs.mkdirSync(uploadPath);
      // 生成一个文件写入 文件流
      const writeStream = fs.createWriteStream(writePath);
      try {
        // 异步把文件流 写入
        await awaitWriteStream(part.pipe(writeStream));
        // 插入一条留言
        const newComment = {
          role: 1,
          img: part.filename,
        };
        const userComent = await ctx.service.comment.findByUserId(userId);
        if (userComent) {
          // 添加
          ctx.service.comment.update(userComent._id, newComment).then(res => {
            if (res.ok === 1) {
              ctx.helper.renderSuccess(ctx, {
                message: '图片留言成功',
                data: {},
              });
            } else {
              ctx.helper.renderFail(ctx, { message: '图片留言失败', data: {} });
            }
          });
        } else {
          // 创建
          ctx.service.comment
            .create({
              userId,
              comments: [ newComment ],
            })
            .then(res => {
              ctx.helper.renderSuccess(ctx, {
                data: res.comments,
                message: '图片留言成功',
              });
            });
        }
      } catch (error) {
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(part);
        writeStream.destroy();
        throw error;
      }
    }
  }

  // 上传投诉建议图片
  async uploadComplaintsImage() {
    const { ctx, app } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    const parts = ctx.multipart({ autoFields: true });
    let part;
    while ((part = await parts()) != null) {
      if (!part.filename) {
        // 注意如果没有传入直接返回
        continue;
      }
      // console.log('file: ' + part.filename, part.encoding, part.mime, parts.field);
      if (!userId) return ctx.helper.renderFail(ctx, { message: '用户未登录' });
      const uploadPath = `${app.config.upload_path}/complaints_files/${userId}`;
      const writePath = path.join(uploadPath, `/${part.filename}`);
      console.log('写入文件：', writePath);
      // 创建文件夹
      if (!fs.existsSync(`${app.config.upload_path}/complaints_files`)) {
        fs.mkdirSync(`${app.config.upload_path}/complaints_files`);
      }
      if (!fs.existsSync(uploadPath)) fs.mkdirSync(uploadPath);
      // 生成一个文件写入 文件流
      const writeStream = fs.createWriteStream(writePath);
      try {
        // 异步把文件流 写入
        await awaitWriteStream(part.pipe(writeStream));
        ctx.helper.renderSuccess(ctx, {
          data: part.filename,
          message: '图片保存成功',
        });
      } catch (error) {
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        console.log('出错啦error', error);
        await sendToWormhole(part);
        writeStream.destroy();
        throw error;
      }
    }
  }
  // 查询身份证信息
  async findInfoByIDNum(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.user.findInfoByIDNum(params.IDNum);
    ctx.body = res;
  }
  // 将转岗的一系列通知关闭
  async closeNotify() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      console.log('关闭通知接口进来了====', params);
      const res = await ctx.service.user.closeNotify(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '操作成功',
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 上传防护用品领用签名
  async ppeSign() {
    const { ctx, app } = this;
    try {
      const params = await this.parseData(ctx, app);
      console.log(params, '2222');
      let res;
      if (params.type === 'receive' || params.type === 'reject') {
        res = await ctx.service.adminuser.receiveProducts({
          _id: params._id,
          employee: params.employee,
          type: params.type,
          claimType: params.claimType,
          sign: params.filename,
          planId: params.planId,
          EnterpriseID: params.EnterpriseID,
          products: params.products,
          product: params.product,
          productionDate: params.productionDate,
        });
      } else {
        res = params.filename;
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '操作成功',
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 上传用户签名图片
  async uploadSignImage() {
    const { ctx, app } = this;
    try {
      const params = await this.parseData(ctx, app);
      const res = await ctx.service.user.uploadSignImage(params);

      // 调岗确认
      if (params.reorientationId) {
        await ctx.service.user.confirmReorientation(params);
      }
      // 解除预警
      await ctx.service.user.cancelWarning(params);

      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '操作成功',
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 上传用户签名图片通用
  async uploadSignImg() {
    const { ctx, app } = this;
    let res = null;
    try {
      // 从缓存里面获取用户信息
      const parts = await ctx.multipart({ autoFields: true });
      const params = {};
      let filename = '';
      let stream;
      while ((stream = await parts()) != null) {
        if (!stream.filename) {
          // 注意如果没有传入直接返回
          return;
        }
        params.EnterpriseID = parts.field.EnterpriseID;
        console.log(parts.field, 'parts.field');
        const configFilePath = path.join(
          app.config.upload_path,
          params.EnterpriseID
        );
        filename =
          Math.random().toString(36).substr(2) +
          new Date().getTime() +
          path.extname(stream.filename).toLocaleLowerCase();
        params.filename = filename;
        // 图片存放在静态资源public/img文件夹下
        // tools.makeEnterpriseDir(configFilePath);
        const target = path.resolve(configFilePath, filename);
        // 生成一个文件写入 文件流
        try {
          // 异步把文件流 写入
          res = await ctx.helper.pipe({
            readableStream: stream,
            target,
          });
        } catch (error) {
          // 如果出现错误，关闭管道,防止浏览器响应卡死
          await sendToWormhole(stream);
          // writeStream.destroy();
          throw error;
        }
      }
      const allPath = await ctx.helper.concatenatePath({
        path: `${this.app.config.upload_http_path}/${params.EnterpriseID}/${filename}`,
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          ...res,
          filename,
          allPath,
        },
        message: '操作成功',
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  //  文件解析
  async parseData(ctx, app) {
    console.log('成功进入解析文件====');
    const parts = await ctx.multipart({ autoFields: true });
    const params = {};
    let stream;
    while ((stream = await parts()) != null) {
      if (!stream.filename) {
        // 注意如果没有传入直接返回
        return;
      }
      console.log(parts.field, 'parts.field');
      params.EnterpriseID = parts.field.EnterpriseID;
      params.employeeId = parts.field.employeeId;
      params.signUserId = parts.field.signUserId;
      params.imageUserId = parts.field.imageUserId;
      params.personalTrainingId = parts.field.personalTrainingId;
      params.detailId = parts.field.detailId;
      params.reorientationId = parts.field.reorientationId;
      params.harmFactors = parts.field.harmFactors;
      params._id = parts.field._id;
      params.type = parts.field.type;
      params.planId = parts.field.planId;
      params.employee = parts.field.employee;
      params.claimType = parts.field.claimType;
      params.receiveDate = parts.field.receiveDate;
      params.product = parts.field.product;
      params.productionDate = parts.field.productionDate;
      try {
        params.products = JSON.parse(parts.field.products);
      } catch (error) {
        console.log(3333, error);
      }
      if (parts.field.id || parts.field.userId) {
        // lht++
        params.defendproductsId = parts.field.id; // 个人防护用品的id
        params.num = parts.field.num; // 个人防护用品的领用数量
        params.userId = parts.field.userId; // 个人防护用品的领用人
      }
      const configFilePath = path.join(
        app.config.upload_path,
        params.EnterpriseID
      );
      const filename =
        Math.random().toString(36).substr(2) +
        new Date().getTime() +
        path.extname(stream.filename).toLocaleLowerCase();
      params.filename = filename;
      // 图片存放在静态资源public/img文件夹下
      tools.makeEnterpriseDir(configFilePath);
      const target = path.resolve(configFilePath, filename);
      // 生成一个文件写入 文件流
      try {
        // 异步把文件流 写入
        await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
      } catch (error) {
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(stream);
        // writeStream.destroy();
        throw error;
      }
    }
    console.log('最后结果===', params);
    return params;
  }

  // 验证微信公众号的token
  async checkToken() {
    try {
      const { ctx } = this;
      const params = ctx.request.query;
      const signature = params.signature;
      const token = '123456';
      const timestamp = params.timestamp;
      const nonce = params.nonce;
      const echostr = params.echostr;
      const signature2 = sha1([ nonce, timestamp, token ].sort().join(''));
      if (signature2 === signature) {
        ctx.body = echostr;
      } else {
        ctx.body = '验证失败';
      }
    } catch (error) {
      console.log(error);
    }
  }
  // 获取user用户信息
  async getInfo() {
    const { ctx } = this;
    const query = { _id: ctx.query.userId };
    if (ctx.query.role) query.role = ctx.query.role;
    const res = await ctx.model.User.findOne(query, {
      name: 1,
      introduction: 1,
      role: 1,
      logo: 1,
    });
    if (res && res._id) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: res,
      });
    }
  }
  async personSign() {
    // 如果库里已经有了签字的图片，那么返回给前端，前端就不用再次上传签字了
    const { ctx, app } = this;
    const params = await this.parseData(ctx, app);
    console.log(params, 'ppppppppppp');
    const res = await ctx.service.user.savePersonSign(params);
    console.log(res, '保存签字是否成功');
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '签名上传成功',
    });
    ctx.auditLog('保存签字是否成功', `${res}`, 'info');
  }
  async pxVerifyImage() {
    const { ctx, app } = this;
    // console.log('摄像头方法');
    const params = await this.parseData(ctx, app);
    // console.log('调用摄像头拍摄', params);
    const res = await ctx.service.user.savePersonVerifyImage(params);
    // console.log(res, '保存照片结果');
    ctx.auditLog('保存照片结果', `${res}`, 'info');
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '图片上传成功',
    });
  }
  async ExmaVerifyImage() {
    const { ctx, app } = this;
    // console.log('考试摄像头方法?????');
    const params = await this.parseData(ctx, app); // 写入图片
    const res = await ctx.service.user.saveExmaVerifyImage(params);
    // 接受service返回的是true/false,
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '考试人脸识别验证结果',
    });
  }
  async getSign(ctx, app) {
    if (app.config.isTrainingLooseMode) {
      return ctx.helper.renderSuccess(ctx, {
        data: true, // 为了兼容之前的版本，如果不是严格模式，直接返回true
        message: '宽松模式，默认有签名',
      });
    }
    const params = ctx.request.body;
    // let EnterpriseID = '';
    // if (params.companyId instanceof Array) {
    //   EnterpriseID = params.companyId[0];
    // } else {
    //   EnterpriseID = params.companyId;
    // }
    // 查找签名
    const res = await ctx.model.PersonalTraining.findOne({
      _id: params.personalTrainingId,
    });
    let isHaveSign = false;
    if (res.sign) {
      isHaveSign = true;
    }
    ctx.helper.renderSuccess(ctx, {
      data: isHaveSign,
      message: 'success',
    });
  }
  // 查找第一次的时候录入的照片
  async getFacePicture(ctx, app) {
    if (app.config.isTrainingLooseMode) {
      return ctx.helper.renderSuccess(ctx, {
        data: true, // 为了兼容之前的版本，如果不是严格模式，直接返回true
        message: '宽松模式，默认有照片。',
      });
    }
    const params = ctx.request.body;
    const res = await ctx.model.PersonalTraining.findOne({
      _id: params.personalTrainingId,
    });
    // 如果有照片，返回true
    let isHaveverifyImage = false;
    if (res.verifyImage) {
      isHaveverifyImage = true;
    }
    ctx.helper.renderSuccess(ctx, {
      data: isHaveverifyImage,
      message: 'success',
    });
  }

  async findNotices(ctx) {
    // const _id = ctx.session.user._id;
    const data = ctx.request.body;
    // 获取首页的通知内容
    const notices = await ctx.service.user.findNotices(data);
    await ctx.helper.renderSuccess(ctx, {
      data: notices,
      // modelPath: `${this.app.config.static.prefix}/${this.app.config.upload_http_path}/${user.companyId[user.companyId.length - 1]}`,
      message: '校验成功',
    });
  }

  async getReorientationInfo(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.user.findReorientationInfo({
      id: data.id,
      employeeId: data.employeeId,
    });

    if (res && res.EnterpriseID) {
      const doc = await ctx.model.Adminorg.findOne(
        { _id: res.EnterpriseID },
        { officialSeal: 1 }
      );
      if (doc && doc.officialSeal) {
        res.officialSeal = doc.officialSeal;
      }
    }

    await ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '校验成功',
    });
  }

  async getAllReorientationInfo(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.user.findAllReorientationInfo({
      employeeId: data.employeeId,
    });

    console.log(res, 'getAllReorientationInfo的resresres');

    // if (res && res.EnterpriseID) {
    //   const doc = await ctx.model.Adminorg.findOne(
    //     { _id: res.EnterpriseID },
    //     { officialSeal: 1 }
    //   );
    //   if (doc && doc.officialSeal) {
    //     res.officialSeal = doc.officialSeal;
    //   }
    // }

    await ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '校验成功',
    });
  }

  async getHbdpData() {
    const { ctx, app } = this;
    try {
      console.log('🍊hbdp');
      const params = ctx.query;
      const { district_id, year } = params;
      const res = await axios({
        url: `${app.config.iService2Host}/hebei/hbdp`,
        method: 'get',
        params: {
          district_id,
          year,
        },
      });
      // console.log('🍊hbdp', res.data);
      if (res.data.code === 200) {
        ctx.helper.renderSuccess(ctx, {
          data: res.data.data,
          message: '数据获取成功',
        });
      } else {
        throw new Error(res.message);
      }
    } catch (error) {
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  }
}

module.exports = AdminController;
