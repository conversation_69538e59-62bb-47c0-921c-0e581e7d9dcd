/**
 * @param app
 * @file 防护用品仓库模型
 * @description 用于管理防护用品的仓库，支持按车间岗位进行分区管理
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const WarehouseSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 30,
    },
    EnterpriseID: {
      type: String,
      required: true,
    },
    managementScope: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      millConstructionId: {
        type: String,
        required: true,
      },
      fullId: {
        type: String,
        required: true,
      },
      level: {
        type: String,
        enum: [ 'mill', 'workspaces', 'stations' ],
        required: true,
      },
    }],
    isPublic: {
      type: Boolean,
      default: false,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  }, {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
    versionKey: false, // 禁用 __v 字段
  });

  // 添加索引
  WarehouseSchema.index({ EnterpriseID: 1 });
  WarehouseSchema.index({ name: 1, EnterpriseID: 1 }, { unique: true });
  WarehouseSchema.index({ isPublic: 1 });
  WarehouseSchema.index({ 'managementScope.fullId': 1 });

  return mongoose.model('Warehouse', WarehouseSchema);
};
