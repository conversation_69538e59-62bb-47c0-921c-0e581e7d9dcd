const path = require('path');
const basicAuth = require('basic-auth');
// const isDocker = process.env.BUILD_ENV === 'docker';
// const mongohost = isDocker ? 'mongodb' : '127.0.0.1:27017';
// const mongobin = isDocker ? '' : '/Users/<USER>/Documents/frame/softs/mongodb/bin/';

module.exports = appInfo => {

  return {
    proxy: false,
    admin_root_path: 'http://localhost',
    // DEV_CONFIG_MODULES_BEGIN
    dev_modules: [],
    // DEV_CONFIG_MODULES_END
    mongoose: {
      client: {
        // url: '****************************************************************************',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-sxcc?authSource=admin',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/frameData?authSource=admin',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-sxcc?authSource=admin',
        url: 'mongodb://127.0.0.1:27017/zyws-by1218',
        // url: 'mongodb://127.0.0.1:27017/zyws-sxcc',
        // url: 'mongodb://127.0.0.1:27017/zyws-by1223',
        // url: '**********************************************************************************************************************************************',
        // url: '****************************************************************',
        // url: 'mongodb://vango:<EMAIL>:62717/zyws-by823?authSource=admin',
        options: {
          useUnifiedTopology: true,
        },
      },
      tools: {
        url: 'mongodb://dbadmin:<EMAIL>:25000/tools?authSource=admin',
        // url: 'mongodb://127.0.0.1:27017/tools',
        options: {
          useUnifiedTopology: true,
        },
      },
    },
    redis: {
      client: {
        port: 62719,
        host: 'vanbox.beasts.wang',
        password: 'Tc666888.',
        // port: 6379,
        // host: '127.0.0.1',
        // password: '',
        db: 0,
      },
    },
    // mongodb相关路径
    mongodb: {
      binPath: '',
      backUpPath: path.join(appInfo.baseDir, 'databak/'),
    },
    storageType: process.env.storageType
      ? process.env.storageType.replace('\n', '')
      : 'oss', // 存储类型： local, oss,
    oss: {
      accessKeyId: process.env.accessKey
        ? process.env.accessKey.replace('\n', '')
        : 'LTAI5tQphaDkN48ZFkT2Tqwz',
      accessKeySecret: process.env.secretKey
        ? process.env.secretKey.replace('\n', '')
        : '******************************',
      endPoint: process.env.endPoint
        ? process.env.endPoint.replace('\n', '')
        : 'https://zyws-net.oss-cn-hangzhou.aliyuncs.com',
      region: process.env.region
        ? process.env.region.replace('\n', '')
        : 'oss-cn-hangzhou',
      timeout: process.env.ossTimeout
        ? process.env.ossTimeout.replace('\n', '')
        : '60s',
      buckets: {
        default: {
          name: process.env.bucket
            ? process.env.bucket.replace('\n', '')
            : 'zyws-net',
          accessPolicy: process.env.accessPolicy
            ? process.env.accessPolicy.replace('\n', '')
            : 'private', // 是否是私有的bucket，默认是空，private私有
        },
      },
    },
    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为true  注意：手动更新优先级大于自动更新
    isVersionUpdate: false,
    iServiceHost: 'https://iservice.xixids.com',
    // 是否开启静态界面自动更新 默认为true  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: true,
    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为false  注意：手动更新优先级大于自动更新
    // cors: {
    //   origin: '*', // 允许的跨域请求的来源
    //   allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS',
    // },
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'app/public'),
        path.join(appInfo.baseDir, 'backstage/dist'),
        // 'E:/jkqy/app/public',
        // 'E:/jkqySuper/app/public',
        // 'E:/jkqyService/app/public',
        // 'E:/operate/app/public', // xxn的本地运营端
      ],
      maxAge: 31536000,
    },
    logger: {
      dir: path.join(appInfo.baseDir, 'logs'),
    },
    server_path: 'http://127.0.0.1:7009',
    server_api: 'http://127.0.0.1:7009/api',
    // 阿里云视频
    aliVideo: {
      accessKeyId: process.env.aliVideo_accessKeyId,
      secretAccessKey: process.env.aliVideo_secretAccessKey,
      userId: process.env.aliVideo_userId,
    },
    // 人脸识别
    facebody: {
      accessKeyId: process.env.facebody_accessKeyId,
      accessKeySecret: process.env.facebody_accessKeySecret,
    },
    pay: {
      wxpay: {
        appid: 'wx8d9eada10facd67a', // 这里填写微信小程序的id，用于掉起支付
      },
    },
    // 不同端的域名
    domainNames: {
      px: 'http://127.0.0.1:3001',
      opt: 'http://127.0.0.1:7005',
    },
    // rabbitmq_url: 'amqp://guest:<EMAIL>:5673',
    rabbitMq: {
      url: 'amqp://guest:<EMAIL>:5673',
      queues: [
        {
          name: 'shaanxiHealthExamRecordData',
          exchange: 'shaanxi',
          service: 'shaanxiData',
          callback: 'uploadShaanxiHealthExamRecordData',
          routingKey: 'shaanxiHealthExamRecordData',
        },
        {
          name: 'whPersonInfo',
          exchange: 'whohs',
          service: 'whData',
          callback: 'processPersonInfo',
          routingKey: 'whPersonInfo',
        },
        {
          name: 'whAccountData',
          exchange: 'whohs',
          service: 'whData',
          callback: 'processPersonAndDingtree',
          routingKey: 'whAccountData',
        },
        {
          name: 'whOrgData',
          exchange: 'whohs',
          service: 'whData',
          callback: 'processRedisData',
          routingKey: 'whOrgData',
        },
        {
          name: 'whPostInfo',
          exchange: 'whohs',
          service: 'whData',
          callback: 'postInfoReceive',
          routingKey: 'whPostInfo',
        },
        {
          name: 'whPostAndPersonInfo',
          exchange: 'whohs',
          service: 'whData',
          callback: 'postAndPersonInfoReceive',
          routingKey: 'whPostAndPersonInfo',
        },
        // {
        //   name: 'dingTalkWorkFlow',
        //   exchange: 'dingApproval',
        //   service: 'dingTalkWorkFlow',
        //   callback: 'handleDingApproval',
        //   routingKey: 'dingTalkWorkFlow',
        // },
      ],
      // 其余配置 参考 https://www.npmjs.com/package/amqplib
    },
    // upload_path: '/opt/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/enterprise',
    upload_path: '/opt/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    // storageType: process.env.storageType
    //   ? process.env.storageType.replace('\n', '')
    //   : 'oss', // 存储类型： local, oss,
    // oss: {
    //   accessKeyId: process.env.accessKey
    //     ? process.env.accessKey.replace('\n', '')
    //     : 'LTAI5tQphaDkN48ZFkT2Tqwz',
    //   accessKeySecret: process.env.secretKey
    //     ? process.env.secretKey.replace('\n', '')
    //     : '******************************',
    //   endPoint: process.env.endPoint
    //     ? process.env.endPoint.replace('\n', '')
    //     : 'https://zyws-net.oss-cn-hangzhou.aliyuncs.com',
    //   region: process.env.region
    //     ? process.env.region.replace('\n', '')
    //     : 'oss-cn-hangzhou',
    //   timeout: process.env.ossTimeout
    //     ? process.env.ossTimeout.replace('\n', '')
    //     : '60s',
    //   buckets: {
    //     default: {
    //       name: process.env.bucket
    //         ? process.env.bucket.replace('\n', '')
    //         : 'zyws-net',
    //       accessPolicy: process.env.accessPolicy
    //         ? process.env.accessPolicy.replace('\n', '')
    //         : 'private', // 是否是私有的bucket，默认是空，private私有
    //     },
    //   },
    // },
    branch: 'by',
    isGetFZData: false, // 是否启动获取福州数据的定时任务 除了福州其他地方不要启动否则导致数据丢失
    oidc: {
      // 北元oidc认证系统
      keyCloak: {
        api_host: process.env.KEYCLOAK_API_HOST, // keycloak的api地址
        realm_name: process.env.KEYCLOAK_REALM_NAME, // keycloak的realm名称
        client_id: process.env.KEYCLOAK_CLIENT_ID, // keycloak的client_id
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET, // keycloak的client_secret
        redirect_uri: process.env.KEYCLOAK_REDIRECT_URI, // keycloak的登录回调地址
        post_logout_redirect_uri: process.env.KEYCLOAK_POST_LOGOUT_REDIRECT_URI, // keycloak的退出回调地址
        qy_callback_host:
          process.env.KEYCLOAK_QY_HOST || 'http://127.0.0.1:7001/whcallback', // keycloak的企业微信登录地址
      },
      h5Host: process.env.ZYWS_H5_HOST || 'http://localhost:8080',
    },
    // 微信支付宝回调地址--本地需要内网穿透
    wxpay_notify_url: 'https://pay.xixids.com/app/pay/wxNotify',
    wxpay_refund_notify_url: 'https://pay.xixids.com/app/pay/wxRefundsNotify',
    alipay_notify_url: 'https://pay.xixids.com/app/pay/alipaysNotify',
    isGetByData: process.env.isGetByData, // 是否启动获取北元数据的定时任务 除了北元其他地方不要启动否则导致数据丢失 true表示启动
    // isGetSxccData: process.env.isGetSxccData, // 是否启动获取山西焦煤数据的定时任务 除了山西焦煤其他地方不要启动否则导致数据丢失 true表示启动
    getByDataInitializationTime: process.env.getByDataInitializationTime
      ? process.env.getByDataInitializationTime.replace('\n', '')
      : '', //  格式为2024-03-08, // 北元数据初始化时间
    byMonitor: {
      piApi: 'http://pi.by.xixids.com',
      isMock: 'N', // 是否开启北元监控模拟数据
      scheduleJob: false, // 是否开启北元监控定时任务
      scheduleCron: '*/5 * * * * *', // 定时任务时间
    },
    byNotice: {
      // 北元接口地址
      api_host: 'https://iip.sxbychem.com',
      // 北元通知的ak
      ak: '03022cab4fa8aca3eb8a48764cd7aaa5',
      // 北元通知的sk
      sk: '39c2ac32e149e24f4f92101936df9db8',
    },
    swaggerdoc: {
      dirScanner: './app/controller', // 需要扫描的控制器目录，默认为 app/controller--还有lib下面的Controller也要扫描
      apiInfo: {
        title: '职卫云OAPI',
        description: '职卫云',
        version: '1.0.0',
      },
      schemes: [ 'http' ], // 配置支持的协议，可以配置多个，如 ['http', 'https']
      consumes: [ 'application/json' ], // 指定处理请求的提交内容类型（Content-Type），如 ['application/json']
      produces: [ 'application/json' ], // 指定返回的响应内容类型，如 ['application/json']
      enableSecurity: true, // 是否启用授权，默认 false
      securityDefinitions: {
        // 配置 Basic 认证
        basic: {
          type: 'basic',
        },
      },
      enable: true,
      securityHandlers: {
        basic: async function basicAuthMiddleware(ctx, next) {
          const credentials = basicAuth(ctx.req);
          // 写校验用户名和密码的方法
          let flag = false;
          if (
            credentials &&
            credentials.name === 'admin' &&
            credentials.pass === '123456'
          ) {
            flag = true;
          }
          if (!credentials || !flag) {
            ctx.status = 401;
            ctx.set('WWW-Authenticate', 'Basic realm="Authorization Required"');
            ctx.body = { error: 'Authorization required.' };
            return;
          }

          await next();
        },
      },
      // ... 其他配置项
    },
    fzDataApiHost: 'http://*************:8002/BhkDataService', // 福州数据接口地址
    // 焦煤企业微信授权配置
    sxccQyWxAuth: {
      corpid: process.env.sxccQyWxAuth_corpid,
      corpsecret: process.env.sxccQyWxAuth_corpsecret,
      aggentid: process.env.sxccQyWxAuth_aggentid,
      corptoken: process.env.sxccQyWxAuth_corptoken,
      aes_key: process.env.sxccQyWxAuth_aes_key,
    },
    isSxccPhysicalCron: process.env.isSxccPhysicalCron, // 是否启动获取山西焦煤数据的定时任务 除了山西焦煤其他地方不要启动否则导致数据丢失 true表示启动
    // 山西焦煤体检签到
    sxccIndividualCheckCron: {
      // 每小时执行一次
      cron: '0 * * * *', // 在每小时的开始时执行
      // disable: !this.isSxccPhysicalCron, // 是否禁用,false表示不禁用
      disable: true, // 是否禁用,false表示不禁用
      immediate: true, // 启动服务时立即执行一次任务
      type: 'worker', // 指定每次只有一个随机的 worker 执行
    },
    // 山西焦煤讯康体检接口地址
    sxccXkHost: 'http://health.1.zhihuitijian.com',
    // sxccXkHost: process.env.sxccXkHost,
    // 是否全量推送
    isFullPushXk: process.env.isFullPushXk === 'true' || false,
    // 证书配置
    certificateConfig: {
      effectiveYear: 3, // 证书有效时间 单位：年
    },
    subscriptionVerification: false, // 套餐支付校验开关
    // 体检信息对接是否用企业id和员工身份证号查询
    isUseOrgIdAndIdCardQuery: false,
    // 福州国密url
    fzgmBaseUrl: process.env.FZGM_BASE_URL || 'http://*************',
    // 福州国密业务key
    fzgmKey: process.env.FZGM_KEY || 'n6beoq8b9g7te7e5',
    // 签名算法
    hmacSignAlgorithm: process.env.HMAC_SIGN_ALGORITHM || 'FZsm3',
    // 密码加密算法
    passwordEncryptionAlgorithm:
      process.env.PASSWORD_ENCRYPTION_ALGORITHM || 'FZsm3',
    // 是否开启数据库加密
    dbEncryption: process.env.DB_ENCRYPTION === 'true',
    // 是否允许福州国密接口调用
    ALLOW_FZGM: process.env.ALLOW_FZGM === 'true',

    fzxyx_token: {
      agentId: 'vw_ff3YYW',
      appKey: '6af7Wa_b9_6b4YeYw6Ww',
      appSecret:
        '361a04227280fd26741e44067dd0a6363ce41f51f175e8ef5fde0832370d4d9c',
    }, // 25000:framdData
    // wh请求地址集合
    whRequest: {
      // wh用户岗位推送返回接口
      userPositionPush:
        process.env.WH_USER_POSITION_PUSH ||
        'http://127.0.0.1:7001/api/testDataPermission',
      host: process.env.WH_HOST || 'https://127.0.0.1:7001',
      qy: process.env.WH_QY || 'https://ohs-qy.cloudqas.whchem.com',
      h5sp:
        process.env.WH_H5SP ||
        'https://ohs-h5.cloudqas.whchem.com/#/pages_user/pages/user/approvalfh',
      username: process.env.WH_USER_NAME || 'user_ohs',
      password: process.env.WH_PASSWORD || 'Ohs@2024',
    },
    whDepartData: {
      // disable: process.env.whDepartData !== 'true',
      disable: true,
      immediate: true,
      interval: '20m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whPolicyData: {
      // disable: process.env.whDepartData !== 'true',
      disable: true,
      immediate: true,
      interval: '20m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whBaseData: {
      // disable: process.env.whBaseData !== 'true',
      disable: true,
      immediate: true,
      interval: '60m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    shaanxiDataInterfaceLog: {
      // disable: process.env.whBaseData !== 'true',
      disable: false,
      immediate: false,
      interval: '1d',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // master和集团分支功能区分开关， 0为功能相同不需要区分，1为需要区分
    isGroupBranch: process.env.isGroupBranch || '1',
    // model字段值是否唯一
    isUnique: false,
    // 体检报告对接是否允许创建企业和员工
    isAllowCreateOrgAndEmployee: false,
    // 是否全量处理陕西标准interfaceLog数据
    isAllHandle: true,
    // 工作场所名称
    workplaceName: {
      mill: '部门',
      workspaces: '装置/部门',
      stations: '工序/模块',
    },
    // workplaceName: {
    //   mill: '厂房',
    //   workspaces: '车间',
    //   stations: '岗位',
    // },
    // 文件保存路径EnterpriseID取值开关, 默认1 master, 0 表示集团
    tjReportEnterpriseID: process.env.TJREPORT_ENTERPRISE_ID || '1',
    // user中为adminuer管理员的分组_id
    adminGroupId: process.env.ADMIN_GROUP_ID || 'xxxxxx',
  };
};
