<template>
  <div class="management-scope-selector">
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 公司选择 -->
      <el-card class="panel-card" shadow="never">
        <div slot="header" class="panel-header">
          <span style="display: flex; align-items: center; gap: 8px;">
            <i class="el-icon-office-building" style="color: #3b82f6;"></i>
            选择公司
          </span>
          <el-badge :value="filteredCompanies.length" type="primary"></el-badge>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
          <el-input
            v-model="companySearchText"
            placeholder="搜索公司..."
            prefix-icon="el-icon-search"
            size="small"
            clearable
            @input="handleCompanySearch"
          />
        </div>

        <div class="company-list-container" v-loading="companiesLoading">
          <el-empty v-if="!companiesLoading && filteredCompanies.length === 0" description="暂无公司数据">
            <i class="el-icon-office-building" style="font-size: 64px; color: #C0C4CC;"></i>
          </el-empty>

          <div v-else-if="!companiesLoading" class="company-list">
            <div
              v-for="company in filteredCompanies"
              :key="company.fullId"
              class="company-item"
              :class="{
                selected: selectedCompany && selectedCompany.fullId === company.fullId,
                disabled: company.disabled
              }"
              @click="!company.disabled && selectCompany(company)"
            >
              <el-card shadow="hover" :class="{ 'disabled-card': company.disabled }">
                <div class="company-content">
                  <div class="company-info">
                    <h4>{{ company.label }}</h4>
                    <p>{{ getCompanyChildrenCount(company) }} 个工作场所</p>
                    <el-tag v-if="company.disabled" size="mini" type="danger" style="margin-top: 4px;">已占用</el-tag>
                  </div>
                  <i v-if="!company.disabled" class="el-icon-arrow-right"></i>
                  <i v-else class="el-icon-lock" style="color: #f56565;"></i>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 工作场所树 -->
      <el-card class="panel-card" shadow="never">
        <div slot="header" class="panel-header">
          <span style="display: flex; align-items: center; gap: 8px;">
            <i class="el-icon-menu" style="color: #10b981;"></i>
            工作场所
            <span v-if="selectedCompany" style="color: #3b82f6; font-weight: normal; font-size: 14px;">
              ({{ selectedCompany.label }})
            </span>
          </span>
          <el-button v-if="selectedCompany" type="text" icon="el-icon-refresh" @click="refreshTree" style="color: #6b7280;">
            刷新
          </el-button>
        </div>

        <!-- 工作场所搜索框 -->
        <div v-if="selectedCompany" class="search-container">
          <el-input
            v-model="workplaceSearchText"
            placeholder="搜索工作场所..."
            prefix-icon="el-icon-search"
            size="small"
            clearable
            @input="handleWorkplaceSearch"
          />
        </div>

        <div class="tree-container" v-loading="workplacesLoading">
          <el-empty v-if="!selectedCompany" description="请先选择一个公司">
            <i class="el-icon-arrow-left" style="font-size: 64px; color: #C0C4CC;"></i>
          </el-empty>

          <el-empty v-else-if="filteredTreeData.length === 0" description="该公司暂无工作场所数据">
            <i class="el-icon-folder-opened" style="font-size: 64px; color: #C0C4CC;"></i>
          </el-empty>

          <el-tree
            v-else
            ref="tree"
            :data="filteredTreeData"
            show-checkbox
            node-key="fullId"
            :props="treeProps"
            :check-strictly="false"
            :checked-keys="checkedKeys"
            :disabled="disabled"
            @check="onTreeCheck"
            :default-expand-all="false"
            :expand-on-click-node="false"
            empty-text="暂无可管理的工作场所数据"
            class="custom-tree"
            :filter-node-method="filterNode"
            :render-content="renderTreeNode"
          ></el-tree>
        </div>
      </el-card>

      <!-- 已选择范围 -->
      <el-card class="panel-card" shadow="never">
        <div slot="header" class="panel-header">
          <span style="display: flex; align-items: center; gap: 8px;">
            <i class="el-icon-check" style="color: #10b981;"></i>
            已选择范围
          </span>
          <el-badge :value="optimizedItems.length" type="success"></el-badge>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-card">
          <div class="stats-main">
            <div class="stats-number">{{ optimizedItems.length }}</div>
            <div class="stats-label">个管理范围</div>
          </div>
          <div class="stats-details">
            <div class="stats-item">
              <span class="stats-desc-label">原始选择:</span>
              <span class="stats-desc-value">{{ rawCheckedKeys.length }} 个</span>
            </div>
            <div class="stats-item">
              <span class="stats-desc-label">优化后:</span>
              <span class="stats-desc-value">{{ optimizedItems.length }} 个</span>
            </div>
            <div v-if="rawCheckedKeys.length > optimizedItems.length" class="stats-item optimization">
              <span class="stats-desc-label">智能优化:</span>
              <span class="stats-desc-value">节省 {{ rawCheckedKeys.length - optimizedItems.length }} 个</span>
            </div>
          </div>
        </div>

        <!-- 选择列表 -->
        <div class="selected-list">
          <el-empty v-if="optimizedItems.length === 0" description="暂未选择任何范围">
            <i class="el-icon-document" style="font-size: 64px; color: #C0C4CC;"></i>
          </el-empty>

          <div v-else>
            <div 
              v-for="item in optimizedItems" 
              :key="item.fullId"
              class="selected-item"
              :class="{ complete: item.isComplete }"
            >
              <el-card shadow="hover">
                <div class="item-content">
                  <div class="item-header">
                    <div class="item-name">
                      <span>{{ item.name }}</span>
                      <el-tag v-if="item.isComplete" size="mini" type="success">
                        完整
                      </el-tag>
                      <el-tag v-if="item.childCount > 0" size="mini" type="info">
                        优化合并
                      </el-tag>
                    </div>
                    <el-button 
                      type="danger" 
                      icon="el-icon-delete" 
                      size="mini"
                      circle
                      :disabled="disabled"
                      @click="removeItem(item.fullId)"
                    ></el-button>
                  </div>
                  <div class="item-path">{{ item.path }}</div>
                  <div v-if="item.childCount" class="item-count">
                    <i class="el-icon-user"></i>
                    包含 {{ item.childCount }} 个子项
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions-footer">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-button @click="clearAll" :disabled="disabled" type="danger" class="clear-button">
                <i class="el-icon-delete"></i>
                <span>清空</span>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ManagementScopeSelector',
  
  props: {
    // 当前选中的管理范围
    value: {
      type: Array,
      default: () => []
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false
    },
    // 当前编辑的仓库ID
    currentWarehouseId: {
      type: String,
      default: null
    },
    // 主要公司ID（用于编辑时自动选择公司）
    primaryCompanyId: {
      type: String,
      default: null
    }
  },
  
  data() {
    return {
      selectedCompany: null,
      checkedKeys: [], // 用于树组件显示
      rawCheckedKeys: [], // 保存原始选择，用于统计

      // 数据相关
      companiesData: [], // 独立的公司列表数据
      workplacesData: [], // 当前选中公司的工作场所数据
      companiesLoading: false,
      workplacesLoading: false,

      // 搜索相关
      companySearchText: '',
      workplaceSearchText: '',

      // 树形控件配置
      treeProps: {
        children: 'children',
        label: 'label'
      },

      // 节点查找缓存
      nodeCache: new Map()
    };
  },
  
  computed: {
    // 过滤后的公司列表
    filteredCompanies() {
      if (!this.companySearchText) {
        return this.companiesData;
      }
      return this.companiesData.filter(company =>
        company && company.label && company.label.toLowerCase().includes(this.companySearchText.toLowerCase())
      );
    },

    // 过滤后的树数据
    filteredTreeData() {
      if (!this.workplaceSearchText) {
        return this.workplacesData;
      }
      return this.filterTreeData(this.workplacesData, this.workplaceSearchText);
    },
    
    // 已选择项目（原始）
    selectedItems() {
      // 使用原始选择数据
      if (!this.rawCheckedKeys || !Array.isArray(this.rawCheckedKeys)) {
        return [];
      }

      return this.rawCheckedKeys.map(fullId => {
        if (!fullId) {
          return null;
        }

        const item = this.findNodeByFullId(fullId);
        if (!item) {
          // 如果找不到节点，返回基本信息
          return {
            fullId: fullId,
            name: fullId,
            level: this.getLevelFromFullId(fullId),
            path: fullId,
            millConstructionId: this.extractMillConstructionId(fullId)
          };
        }

        return {
          fullId: item.fullId,
          name: item.label || item.name || fullId,
          level: item.level || 'unknown',
          path: item.label || item.name || fullId,
          millConstructionId: this.extractMillConstructionId(item.fullId)
        };
      }).filter(Boolean);
    },
    
    // 优化后的选择项目（智能合并）
    optimizedItems() {
      if (this.selectedItems.length === 0) return [];

      // 执行智能合并优化
      const optimized = this.optimizeManagementScope(this.selectedItems);

      return optimized.map(item => ({
        ...item,
        isComplete: item.isComplete || false,
        childCount: item.childCount || 0
      }));
    }
  },
  
  created() {
    // 组件创建时初始化
    this.initializeComponent();
  },

  watch: {
    companiesData() {
      this.nodeCache.clear();
      if (this.selectedCompany && this.companiesData.length > 0) {
        const exists = this.companiesData.find(c => c.fullId === this.selectedCompany.fullId);
        if (!exists) {
          this.selectedCompany = null;
          this.checkedKeys = [];
          this.workplacesData = [];
        }
      }
    },

    value: {
      immediate: true,
      handler(newValue) {
        const newCheckedKeys = Array.isArray(newValue) && newValue.length > 0
          ? newValue.map(item => item && item.fullId).filter(Boolean)
          : [];

        if (JSON.stringify(newCheckedKeys) !== JSON.stringify(this.checkedKeys)) {
          this.checkedKeys = newCheckedKeys;
          // 当外部传入空值时，总是清空 rawCheckedKeys
          // 当外部传入有值且 rawCheckedKeys 为空时，设置 rawCheckedKeys
          if (newCheckedKeys.length === 0) {
            this.rawCheckedKeys = [];
          } else if (this.rawCheckedKeys.length === 0) {
            this.rawCheckedKeys = [...newCheckedKeys];
          }
          this.$nextTick(() => {
            if (this.$refs.tree) {
              this.$refs.tree.setCheckedKeys(newCheckedKeys);
            }
          });
        }
      }
    },

    rawCheckedKeys: {
      handler(newKeys, oldKeys) {
        if (!Array.isArray(newKeys) || JSON.stringify(newKeys) === JSON.stringify(oldKeys)) {
          return;
        }

        const optimizedScope = this.optimizedItems.map(item => ({
          fullId: item.fullId,
          level: item.level,
          millConstructionId: item.millConstructionId
        }));

        this.$emit('input', optimizedScope);
      }
    },

    // 监听主要公司ID变化
    primaryCompanyId: {
      immediate: true,
      handler(newCompanyId) {
        if (newCompanyId && this.companiesData.length > 0) {
          this.autoSelectCompanyById(newCompanyId);
        }
      }
    }
  },
  
  methods: {
    // 初始化组件
    async initializeComponent() {
      await this.fetchCompanyList();
      // 如果有主要公司ID，自动选择对应的公司
      if (this.primaryCompanyId && this.companiesData.length > 0) {
        await this.autoSelectCompanyById(this.primaryCompanyId);
      }
    },

    // 获取公司列表
    async fetchCompanyList() {
      this.companiesLoading = true;
      try {
        const { getCompanyList } = await import('@/api/warehouse');
        const res = await getCompanyList({
          currentWarehouseId: this.currentWarehouseId
        });

        if (res.status === 200 || res.code === 200) {
          this.companiesData = res.data || [];
          // 如果有主要公司ID，在获取公司列表后自动选择
          if (this.primaryCompanyId && this.companiesData.length > 0) {
            await this.autoSelectCompanyById(this.primaryCompanyId);
          }
        } else {
          this.$message.error(res.message || '获取公司列表失败');
        }
      } catch (error) {
        console.error('获取公司列表失败:', error);
        this.$message.error('获取公司列表失败');
      } finally {
        this.companiesLoading = false;
      }
    },

    // 根据公司ID自动选择公司
    async autoSelectCompanyById(companyId) {
      if (!companyId || !this.companiesData || this.companiesData.length === 0) {
        return;
      }

      // 查找匹配的公司
      const targetCompany = this.companiesData.find(company =>
        company.fullId === companyId || company._id === companyId
      );

      if (targetCompany && !targetCompany.disabled) {
        console.log('自动选择公司:', targetCompany);
        // 保持现有选择，不清空
        await this.selectCompany(targetCompany, true);

        // 等待工作场所数据加载完成后，恢复选中状态
        this.$nextTick(() => {
          if (this.value && this.value.length > 0) {
            const checkedKeys = this.value.map(item => item.fullId).filter(Boolean);
            this.checkedKeys = checkedKeys;
            this.rawCheckedKeys = [...checkedKeys];

            if (this.$refs.tree) {
              this.$refs.tree.setCheckedKeys(checkedKeys);
            }
          }
        });
      } else {
        console.warn('未找到可选择的公司:', companyId, targetCompany);
      }
    },

    // 智能合并管理范围
    optimizeManagementScope(selectedItems) {
      if (!selectedItems || selectedItems.length === 0) {
        return [];
      }

      // 按层级分组
      const itemsByLevel = this.groupItemsByLevel(selectedItems);

      // 构建层级关系映射（传入selectedItems）
      const hierarchyMap = this.buildHierarchyMap(selectedItems);

      // 执行自底向上的合并优化
      const optimized = this.performBottomUpOptimization(itemsByLevel, hierarchyMap);
      return optimized;
    },

    // 按层级分组选中项
    groupItemsByLevel(selectedItems) {
      const groups = {
        mill: [],
        workspaces: [],
        stations: []
      };

      selectedItems.forEach(item => {
        if (item.level && groups[item.level]) {
          groups[item.level].push(item);
        }
      });

      return groups;
    },

    // 构建层级关系映射（基于fullId解析）
    buildHierarchyMap(selectedItems) {
      const hierarchyMap = {
        children: new Map(), // 父节点 -> 子节点列表
        parent: new Map(),   // 子节点 -> 父节点
        allNodes: new Map()  // fullId -> 节点信息
      };

      // 从selectedItems中构建层级关系
      selectedItems.forEach(item => {
        const fullId = item.fullId;

        // 记录节点信息
        hierarchyMap.allNodes.set(fullId, {
          fullId,
          label: item.name,
          level: item.level,
          parent: null // 稍后设置
        });

        // 根据fullId格式解析父节点
        const parentFullId = this.getParentFullId(fullId);
        if (parentFullId) {
          hierarchyMap.parent.set(fullId, parentFullId);

          if (!hierarchyMap.children.has(parentFullId)) {
            hierarchyMap.children.set(parentFullId, []);
          }
          hierarchyMap.children.get(parentFullId).push(fullId);

          // 更新父节点信息
          const nodeInfo = hierarchyMap.allNodes.get(fullId);
          if (nodeInfo) {
            nodeInfo.parent = parentFullId;
          }
        }
      });

      console.log('构建的层级关系映射:', {
        children: Array.from(hierarchyMap.children.entries()),
        parent: Array.from(hierarchyMap.parent.entries()),
        allNodes: Array.from(hierarchyMap.allNodes.entries())
      });

      return hierarchyMap;
    },

    // 根据fullId获取父节点fullId（支持多种层级结构）
    getParentFullId(fullId) {
      if (!fullId || typeof fullId !== 'string') {
        return null;
      }

      console.log(`解析父节点 - fullId: ${fullId}`);

      // 情况1：stations层级
      if (fullId.includes('_child_stations_')) {
        // 可能的格式：
        // - millId_child_workspaces_workspaceId_child_stations_stationId
        // - workspaceId_child_stations_stationId
        const parts = fullId.split('_child_stations_');
        const parentFullId = parts[0];
        console.log(`  stations层级，父节点: ${parentFullId}`);
        return parentFullId; // 返回父级（可能是workspace或mill下的workspace）
      }

      // 情况2：workspaces层级（在mill下）
      if (fullId.includes('_child_workspaces_')) {
        // 格式：millId_child_workspaces_workspaceId
        const parts = fullId.split('_child_workspaces_');
        const parentFullId = parts[0];
        console.log(`  workspaces层级（在mill下），父节点: ${parentFullId}`);
        return parentFullId; // 返回mill的fullId
      }

      // 情况3：mill层级或独立的workspaces层级
      // 这些没有父节点
      console.log(`  顶级节点，无父节点`);
      return null;
    },



    // 执行自底向上的优化
    performBottomUpOptimization(itemsByLevel, hierarchyMap) {
      const selectedFullIds = new Set();
      const optimizedItems = [];

      // 收集所有选中的fullId
      Object.values(itemsByLevel).forEach(items => {
        items.forEach(item => selectedFullIds.add(item.fullId));
      });

      console.log('选中的fullId集合:', Array.from(selectedFullIds));

      // 从最底层开始优化（stations -> workspaces -> mill）
      const levels = ['stations', 'workspaces', 'mill'];
      const processedNodes = new Set();

      levels.forEach(level => {
        console.log(`处理层级: ${level}`);

        itemsByLevel[level].forEach(item => {
          if (processedNodes.has(item.fullId)) {
            console.log(`节点 ${item.fullId} 已被处理，跳过`);
            return;
          }

          // 检查是否可以被父节点合并
          const canBeMerged = this.canBeMergedByParent(
            item.fullId,
            selectedFullIds,
            hierarchyMap,
            processedNodes
          );

          if (!canBeMerged) {
            // 不能被合并，添加到优化结果中
            const optimizedItem = this.createOptimizedItem(item, hierarchyMap, selectedFullIds);
            optimizedItems.push(optimizedItem);
            console.log(`添加优化项: ${item.fullId} (${item.level})`);
          } else {
            console.log(`节点 ${item.fullId} 可以被父节点合并，跳过`);
          }

          processedNodes.add(item.fullId);
        });
      });

      return optimizedItems;
    },

    // 检查节点是否可以被父节点合并
    canBeMergedByParent(fullId, selectedFullIds, hierarchyMap, processedNodes) {
      const parentFullId = hierarchyMap.parent.get(fullId);

      console.log(`检查节点 ${fullId} 是否可以被父节点合并:`);
      console.log(`  父节点: ${parentFullId}`);

      if (!parentFullId) {
        console.log(`  没有父节点，不能被合并`);
        return false;
      }

      // 检查父节点是否也被选中
      if (!selectedFullIds.has(parentFullId)) {
        console.log(`  父节点 ${parentFullId} 未被选中，不能合并`);
        return false;
      }

      // 获取父节点的所有子节点（需要从所有可能的子节点中查找）
      const allPossibleChildren = this.getAllChildrenOfParent(parentFullId, selectedFullIds);
      const selectedChildren = allPossibleChildren.filter(childId => selectedFullIds.has(childId));

      console.log(`  父节点 ${parentFullId} 的所有可能子节点:`, allPossibleChildren);
      console.log(`  父节点 ${parentFullId} 的已选中子节点:`, selectedChildren);

      // 检查是否所有子节点都被选中
      const allChildrenSelected = allPossibleChildren.length > 0 &&
                                  allPossibleChildren.every(childId => selectedFullIds.has(childId));

      if (allChildrenSelected) {
        console.log(`  节点 ${fullId} 的父节点 ${parentFullId} 的所有子节点都被选中，可以合并`);

        // 标记所有子节点为已处理（因为它们都会被父节点代替）
        allPossibleChildren.forEach(childId => processedNodes.add(childId));

        return true;
      }

      console.log(`  不满足合并条件，不能合并`);
      return false;
    },

    // 获取父节点的所有子节点（包括未选中的）
    getAllChildrenOfParent(parentFullId, selectedFullIds) {
      const children = [];

      // 遍历所有选中的节点，找出属于该父节点的子节点
      selectedFullIds.forEach(fullId => {
        const nodeParentId = this.getParentFullId(fullId);
        if (nodeParentId === parentFullId) {
          children.push(fullId);
        }
      });

      console.log(`父节点 ${parentFullId} 的子节点:`, children);
      return children;
    },

    // 检查父节点是否应该被包含在优化结果中
    shouldIncludeParent(parentFullId, selectedFullIds) {
      // 如果父节点本身也被选中，且所有子节点都被选中，则应该用父节点替代子节点
      if (!selectedFullIds.has(parentFullId)) {
        return false;
      }

      const allChildren = this.getAllChildrenOfParent(parentFullId, selectedFullIds);
      const allChildrenSelected = allChildren.length > 0 &&
                                  allChildren.every(childId => selectedFullIds.has(childId));

      console.log(`检查父节点 ${parentFullId} 是否应该包含:`, {
        parentSelected: selectedFullIds.has(parentFullId),
        allChildren,
        allChildrenSelected,
        shouldInclude: allChildrenSelected
      });

      return allChildrenSelected;
    },

    // 创建优化后的项目
    createOptimizedItem(item, hierarchyMap, selectedFullIds) {
      const nodeInfo = hierarchyMap.allNodes.get(item.fullId);
      const children = hierarchyMap.children.get(item.fullId) || [];

      // 计算子项数量（包括间接子项）
      const childCount = this.countAllChildren(item.fullId, hierarchyMap);

      // 检查是否为完整选择（所有子项都被选中）
      const isComplete = children.length > 0 && children.every(childId =>
        selectedFullIds.has(childId) || this.isChildFullySelected(childId, selectedFullIds, hierarchyMap)
      );

      return {
        ...item,
        name: nodeInfo ? nodeInfo.label : item.name,
        path: this.buildNodePath(item.fullId, hierarchyMap),
        icon: this.getNodeIcon(item.level),
        isComplete,
        childCount,
        level: item.level,
        millConstructionId: item.millConstructionId
      };
    },

    // 递归计算所有子项数量
    countAllChildren(fullId, hierarchyMap) {
      const children = hierarchyMap.children.get(fullId) || [];
      let count = children.length;

      children.forEach(childId => {
        count += this.countAllChildren(childId, hierarchyMap);
      });

      return count;
    },

    // 检查子项是否完全被选中
    isChildFullySelected(fullId, selectedFullIds, hierarchyMap) {
      if (selectedFullIds.has(fullId)) {
        return true;
      }

      const children = hierarchyMap.children.get(fullId) || [];
      return children.length > 0 && children.every(childId =>
        this.isChildFullySelected(childId, selectedFullIds, hierarchyMap)
      );
    },

    // 构建节点路径
    buildNodePath(fullId, hierarchyMap) {
      const path = [];
      let currentId = fullId;

      while (currentId) {
        const nodeInfo = hierarchyMap.allNodes.get(currentId);
        if (nodeInfo) {
          path.unshift(nodeInfo.label);
        }
        currentId = hierarchyMap.parent.get(currentId);
      }

      return path.join(' > ');
    },

    // 自定义渲染树节点
    renderTreeNode(h, { node, data }) {
      // 创建标签元素
      const tags = [];

      if (data.level === 'mill') {
        tags.push(h('el-tag', { props: { size: 'mini', type: 'primary' } }, '厂房'));
      } else if (data.level === 'workspaces') {
        tags.push(h('el-tag', { props: { size: 'mini', type: 'success' } }, '车间'));
      } else if (data.level === 'stations') {
        tags.push(h('el-tag', { props: { size: 'mini', type: 'warning' } }, '岗位'));
      }

      if (data.disabled) {
        const occupiedText = data.occupiedBy ? `已占用(${data.occupiedBy})` : '已占用';
        tags.push(h('el-tag', { props: { size: 'mini', type: 'danger' } }, occupiedText));
      }

      return h('span', { class: 'tree-node-content' }, [
        h('span', { class: 'node-label' }, [
          h('span', { class: `level-${data.level}` }, node.label),
          ...tags
        ])
      ]);
    },

    // 获取工作场所数据
    async fetchWorkplaces(EnterpriseID) {
      this.workplacesLoading = true;
      try {
        const { getMillConstructionTree } = await import('@/api/warehouse');
        const res = await getMillConstructionTree({
          EnterpriseID,
          currentWarehouseId: this.currentWarehouseId
        });

        console.log('工作场所接口响应:', res);

        if (res.status === 200 || res.code === 200) {
          this.workplacesData = res.data || [];
          console.log('获取工作场所成功:', this.workplacesData);
        } else {
          console.error('工作场所接口返回错误:', res);
          this.$message.error(res.message || '获取工作场所失败');
        }
      } catch (error) {
        console.error('获取工作场所失败:', error);
        this.$message.error('获取工作场所失败');
      } finally {
        this.workplacesLoading = false;
      }
    },



    // 选择公司
    async selectCompany(company, preserveSelection = false) {
      try {
        console.log('选择公司:', company, '保持选择:', preserveSelection);

        // 验证公司数据
        if (!company || !company.fullId) {
          console.error('无效的公司数据:', company);
          this.$message.error('选择的公司数据无效');
          return;
        }

        // 设置选中的公司
        this.selectedCompany = company;

        // 如果不保持选择，清空之前的选择
        if (!preserveSelection) {
          this.checkedKeys = [];
          this.rawCheckedKeys = [];

          // 重置树形控件的选中状态
          this.$nextTick(() => {
            if (this.$refs.tree) {
              this.$refs.tree.setCheckedKeys([]);
            }
          });
        }

        // 获取选中公司的工作场所数据
        await this.fetchWorkplaces(company.fullId);

        console.log('公司选择完成，当前树数据:', this.currentTreeData);

      } catch (error) {
        console.error('选择公司时发生错误:', error);
        this.$message.error('选择公司失败，请重试');
      }
    },
    
    // 树节点选择事件
    onTreeCheck(data, { checkedKeys }) {
      try {
        console.log('树节点选择变化:', { data, checkedKeys });

        if (!Array.isArray(checkedKeys)) {
          console.warn('checkedKeys 不是数组:', checkedKeys);
          return;
        }

        // 同时更新两个数组
        this.checkedKeys = [...checkedKeys]; // 用于树组件显示
        this.rawCheckedKeys = [...checkedKeys]; // 保存原始选择

      } catch (error) {
        console.error('处理树节点选择时发生错误:', error);
      }
    },
    
    // 获取公司子项数量
    getCompanyChildrenCount(company) {
      return company.totalWorkplaces || 0;
    },
    
    // 获取节点图标
    getNodeIcon(level) {
      if (!level || typeof level !== 'string') {
        return '📁';
      }

      const icons = {
        mill: '🏭',
        workspaces: '🏢',
        stations: '👷'
      };
      return icons[level] || '📁';
    },
    
    // 根据fullId查找节点
    findNodeByFullId(fullId) {
      if (this.nodeCache.has(fullId)) {
        return this.nodeCache.get(fullId);
      }

      let result = null;
      const findInTree = (nodes) => {
        if (!Array.isArray(nodes)) return false;

        for (const node of nodes) {
          if (node && node.fullId === fullId) {
            result = node;
            return true;
          }
          if (node && node.children && findInTree(node.children)) {
            return true;
          }
        }
        return false;
      };

      findInTree(this.companiesData) || findInTree(this.workplacesData);

      if (result) {
        this.nodeCache.set(fullId, result);
      }
      return result;
    },
    
    // 获取节点路径
    getNodePath(node) {
      const parts = node.fullId.split('_child_');
      const pathParts = [];
      
      // 解析fullId构建路径
      if (parts.length >= 1) {
        const millNode = this.findNodeByFullId(parts[0]);
        if (millNode) pathParts.push(millNode.label);
      }
      
      if (parts.length >= 2 && parts[1].startsWith('workspaces_')) {
        const workspaceId = parts[1].replace('workspaces_', '').split('_child_')[0];
        const workspaceFullId = `${parts[0]}_child_workspaces_${workspaceId}`;
        const workspaceNode = this.findNodeByFullId(workspaceFullId);
        if (workspaceNode) pathParts.push(workspaceNode.label);
      }
      
      if (parts.length >= 2 && parts[1].includes('_child_stations_')) {
        const stationFullId = node.fullId;
        const stationNode = this.findNodeByFullId(stationFullId);
        if (stationNode) pathParts.push(stationNode.label);
      }
      
      return pathParts.join(' > ');
    },
    
    // 提取millConstructionId
    extractMillConstructionId(fullId) {
      if (!fullId || typeof fullId !== 'string') {
        return '';
      }
      return fullId.split('_child_')[0];
    },

    // 从fullId推断层级
    getLevelFromFullId(fullId) {
      if (!fullId) return 'unknown';

      const parts = fullId.split('_child_');
      if (parts.length === 1) return 'mill';
      if (parts[1] && parts[1].startsWith('workspaces_') && !parts[1].includes('_child_stations_')) return 'workspaces';
      if (parts[1] && parts[1].includes('_child_stations_')) return 'stations';
      return 'unknown';
    },
    

    
    // 移除选中项
    removeItem(fullId) {
      // 移除选中项及其所有子项
      const newCheckedKeys = this.rawCheckedKeys.filter(checkedKey =>
        checkedKey !== fullId && !checkedKey.startsWith(fullId + '_child_')
      );
      this.checkedKeys = newCheckedKeys;
      this.rawCheckedKeys = newCheckedKeys;
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys(newCheckedKeys);
      }
    },
    
    // 刷新树
    async refreshTree() {
      try {
        if (this.selectedCompany) {
          // 如果有选中的公司，刷新工作场所数据
          await this.fetchWorkplaces(this.selectedCompany.fullId);
          this.$message.success('工作场所数据已刷新');
        } else {
          // 否则刷新公司列表
          await this.fetchCompanyList();
          this.$message.success('公司列表已刷新');
        }
      } catch (error) {
        console.error('刷新失败:', error);
        this.$message.error('刷新失败，请重试');
      }
    },

    // 工作场所搜索处理
    handleWorkplaceSearch() {
      if (this.$refs.tree) {
        this.$refs.tree.filter(this.workplaceSearchText);
      }
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.label.toLowerCase().includes(value.toLowerCase());
    },

    // 递归过滤树数据
    filterTreeData(treeData, searchText) {
      const filtered = [];

      treeData.forEach(node => {
        const nodeMatches = node.label.toLowerCase().includes(searchText.toLowerCase());
        let childrenMatches = [];

        if (node.children && node.children.length > 0) {
          childrenMatches = this.filterTreeData(node.children, searchText);
        }

        if (nodeMatches || childrenMatches.length > 0) {
          filtered.push({
            ...node,
            children: childrenMatches.length > 0 ? childrenMatches : node.children
          });
        }
      });

      return filtered;
    },
    
    // 清空所有选择
    clearAll() {
      if (this.rawCheckedKeys.length === 0) {
        this.$message.info('当前没有选择任何项目');
        return;
      }

      this.$confirm('确认清空所有选择？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.checkedKeys = [];
        this.rawCheckedKeys = [];
        if (this.$refs.tree) {
          this.$refs.tree.setCheckedKeys([]);
        }
        this.$message.success('已清空所有选择');
      });
    },

    // 重置组件状态（供外部调用）
    resetComponent() {
      this.selectedCompany = null;
      this.checkedKeys = [];
      this.rawCheckedKeys = [];
      this.workplacesData = [];
      this.companySearchText = '';
      this.workplaceSearchText = '';
      this.nodeCache.clear();

      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([]);
      }
    },

    // 强制刷新数据（供外部调用）
    async forceRefresh() {
      // 重新初始化组件，获取最新的占用状态
      await this.initializeComponent();

      // 如果有选中的公司，重新获取工作场所数据
      if (this.selectedCompany) {
        await this.fetchWorkplaces(this.selectedCompany.fullId);
      }
    }
  }
};
</script>

<style scoped>
.management-scope-selector {
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}



.main-content {
  display: grid;
  gap: 16px;
  padding: 16px;
  grid-template-columns: 320px 1fr 400px; /* 增加右侧面板宽度 */
  height: 100%; /* 占满整个容器 */
  min-height: 600px; /* 确保最小高度 */
}

.panel-card {
  height: 100%; /* 占满容器高度 */
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  background: #f9fafb;
  flex-shrink: 0;
  font-weight: 600;
  color: #374151;
}

/* 搜索框容器 */
.search-container {
  height: 48px; /* 固定搜索框高度 */
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  background: white;
  flex-shrink: 0;
}

/* 修复表单验证导致的红色边框问题 */
.search-container :deep(.el-input__inner) {
  border-color: #dcdfe6 !important;
  box-shadow: none !important;
}

.search-container :deep(.el-input__inner:focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

.search-container :deep(.el-input__inner:hover) {
  border-color: #c0c4cc !important;
}

/* 确保搜索框不受表单验证状态影响 */
.search-container :deep(.el-input.is-error .el-input__inner) {
  border-color: #dcdfe6 !important;
  box-shadow: none !important;
}

.search-container :deep(.el-input.is-success .el-input__inner) {
  border-color: #dcdfe6 !important;
  box-shadow: none !important;
}

/* 公司列表容器 */
.company-list-container {
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.company-list {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto;
  padding: 8px 16px;
}

.company-item {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0; /* 防止在flex容器中被压缩 */
}

.company-item:hover:not(.disabled) {
  border-color: #60a5fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.company-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.company-item.disabled .disabled-card {
  background-color: #f9f9f9 !important;
  border-color: #e5e5e5 !important;
}

.company-item.selected {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  background: #eff6ff;
}

.company-item:last-child {
  margin-bottom: 8px; /* 最后一项减少底部间距 */
}

.company-content {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
}

.company-info h4 {
  margin: 0 0 4px 0;
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.company-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.tree-container {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto;
  padding: 8px;
  background: white;
}

.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-icon {
  font-size: 14px;
}

.level-mill {
  color: #2563eb;
  font-weight: 600;
}
.level-workspaces {
  color: #16a34a;
  font-weight: 500;
}
.level-stations {
  color: #E6A23C;
}

.selected-list {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto;
  padding: 8px;
  min-height: 200px; /* 确保列表区域有足够高度 */
}

.selected-item {
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
}

.selected-item.complete {
  border-left: 4px solid #10b981;
}

.item-content {
  padding: 16px;
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.item-path {
  color: #6b7280;
  font-size: 12px;
  margin-bottom: 4px;
}

.item-count {
  color: #16a34a;
  font-size: 12px;
  font-weight: 500;
}

.stats-card {
  height: 70px; /* 减少高度，更紧凑 */
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  margin: 8px 8px 12px; /* 调整边距 */
  border-radius: 8px;
  padding: 12px 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
  display: flex;
  flex-direction: row; /* 水平布局 */
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.stats-main {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 2px;
}

.stats-label {
  opacity: 0.9;
  font-size: 12px;
  line-height: 1;
}

.stats-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  line-height: 1;
}

.stats-desc-label {
  opacity: 0.8;
  white-space: nowrap;
}

.stats-desc-value {
  font-weight: 600;
  white-space: nowrap;
}

.stats-item.optimization .stats-desc-value {
  color: #fbbf24;
  font-weight: 700;
}

.actions-footer {
  height: 60px; /* 固定操作按钮区域高度 */
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
  background: #f9fafb;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.clear-button {
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6px !important;
  padding: 0 16px !important;
  box-sizing: border-box !important;
}

.clear-button i {
  font-size: 14px;
  margin: 0 !important;
}

.clear-button span {
  font-size: 14px;
  line-height: 1;
}

/* Element UI 组件样式优化 */
:deep(.el-card__header) {
  padding: 0;
  flex-shrink: 0; /* 防止头部被压缩 */
}

:deep(.el-card__body) {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1; /* 确保body占据剩余空间 */
  min-height: 0; /* 关键：允许flex子项收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

:deep(.el-empty) {
  padding: 32px 0;
}

:deep(.el-empty__image) {
  width: 64px;
  height: 64px;
}

/* 树形控件样式优化 */
:deep(.custom-tree) {
  background: transparent;
}

:deep(.custom-tree .el-tree-node) {
  position: relative;
}

:deep(.custom-tree .el-tree-node__content) {
  height: auto;
  padding: 10px 8px;
  border-radius: 6px;
  margin: 2px 4px;
  transition: all 0.2s;
  border: 1px solid transparent;
}

:deep(.custom-tree .el-tree-node__content:hover) {
  background-color: #eff6ff;
  border-color: #dbeafe;
}

:deep(.custom-tree .el-tree-node__expand-icon) {
  color: #3b82f6;
  font-size: 14px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
}

/* 隐藏没有子节点的展开图标 */
:deep(.custom-tree .el-tree-node.is-leaf > .el-tree-node__content > .el-tree-node__expand-icon) {
  display: none;
  background: #f3f4f6;
  transition: all 0.2s;
}

:deep(.custom-tree .el-tree-node__expand-icon:hover) {
  background: #3b82f6;
  color: white;
}

:deep(.custom-tree .el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.custom-tree .el-tree-node__children) {
  padding-left: 20px;
  border-left: 2px solid #f3f4f6;
  margin-left: 10px;
}

:deep(.custom-tree .el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

:deep(.custom-tree .el-checkbox__input.is-disabled + .el-checkbox__label) {
  color: #9ca3af;
}

/* 树节点层级缩进 */
:deep(.custom-tree .el-tree-node__children .el-tree-node__content) {
  padding-left: 24px;
}

:deep(.custom-tree .el-tree-node__children .el-tree-node__children .el-tree-node__content) {
  padding-left: 40px;
}

/* 响应式设计优化 */
@media (max-width: 1600px) {
  .main-content {
    grid-template-columns: 300px 1fr 380px; /* 保持右侧面板合理宽度 */
  }
}

@media (max-width: 1400px) {
  .main-content {
    grid-template-columns: 280px 1fr 360px; /* 适当减少但保持可用性 */
  }
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 12px;
    min-height: auto; /* 移动端不限制高度 */
  }

  .panel-card {
    height: auto;
    min-height: 400px; /* 增加最小高度确保内容可见 */
  }
}

@media (max-width: 1000px) {
  .main-content {
    grid-template-columns: 1fr;
    padding: 12px;
  }

  .panel-card {
    min-height: 350px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 8px;
    gap: 8px;
  }

  .stats-card {
    margin: 4px 4px 12px;
    padding: 10px 12px;
    height: 60px; /* 移动端进一步减少高度 */
    flex-direction: column; /* 移动端改为垂直布局 */
    text-align: center;
    gap: 8px;
  }

  .stats-main {
    align-items: center;
  }

  .stats-details {
    align-items: center;
    gap: 2px;
  }

  .stats-item {
    font-size: 10px;
  }

  .actions-footer {
    padding: 12px;
  }

  .selected-list {
    padding: 4px;
    min-height: 150px; /* 移动端减少最小高度 */
  }

  .panel-card {
    min-height: 300px; /* 移动端减少最小高度 */
  }
}

/* 滚动条样式优化 */
:deep(.company-list::-webkit-scrollbar),
:deep(.tree-container::-webkit-scrollbar),
:deep(.selected-list::-webkit-scrollbar) {
  width: 8px;
}

:deep(.company-list::-webkit-scrollbar-track),
:deep(.tree-container::-webkit-scrollbar-track),
:deep(.selected-list::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 4px;
}

:deep(.company-list::-webkit-scrollbar-thumb),
:deep(.tree-container::-webkit-scrollbar-thumb),
:deep(.selected-list::-webkit-scrollbar-thumb) {
  background: #cbd5e1;
  border-radius: 4px;
}

:deep(.company-list::-webkit-scrollbar-thumb:hover),
:deep(.tree-container::-webkit-scrollbar-thumb:hover),
:deep(.selected-list::-webkit-scrollbar-thumb:hover) {
  background: #94a3b8;
}

/* Firefox 滚动条样式 */
.company-list,
.tree-container,
.selected-list {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}
</style>
