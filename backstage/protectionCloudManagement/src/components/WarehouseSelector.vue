<template>
  <div class="warehouse-selector">
    <!-- 调试信息 -->
    <div v-if="$route.query.debug" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
      <p>调试信息：</p>
      <p>仓库列表长度: {{ warehouseList.length }}</p>
      <p>当前选中仓库: {{ selectedWarehouse }}</p>
      <p>加载状态: {{ loading }}</p>
      <p>仓库列表: {{ JSON.stringify(warehouseList) }}</p>
    </div>

    <div class="selector-container">
      <span class="selector-label">当前仓库：</span>
      <el-select
        v-model="selectedWarehouse"
        placeholder="请选择仓库"
        @change="handleWarehouseChange"
        style="width: 200px;"
        :loading="loading"
      >
        <el-option
          v-for="item in warehouseList"
          :key="item._id"
          :label="item.name"
          :value="item._id"
        >
          <span style="float: left">{{ item.name }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">
            {{ item.isPublic ? '公共' : '独立' }}
          </span>
        </el-option>
      </el-select>
      
      <el-button
        type="primary"
        size="small"
        @click="goToWarehouseManagement"
        style="margin-left: 10px;"
        v-if="showManagementButton"
      >
        <i class="el-icon-setting" />
        仓库管理
      </el-button>
    </div>

    <!-- 权限提示 -->
    <div v-if="showNoPermissionTip" class="no-permission-tip">
      <el-alert
        title="您暂无可管理的防护用品仓库，如需管理防护用品，请联系超级管理员进行配置。"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { getWarehouseList } from '@/api/index';

export default {
  name: 'WarehouseSelector',
  
  props: {
    value: {
      type: String,
      default: ''
    },
    showManagementButton: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      warehouseList: [],
      selectedWarehouse: this.value || '',
      loading: false
    };
  },

  computed: {
    showNoPermissionTip() {
      return this.warehouseList.length === 0 && !this.loading;
    }
  },

  watch: {
    value(newVal) {
      this.selectedWarehouse = newVal;
    }
  },

  created() {
    this.fetchWarehouseList();
  },

  methods: {
    // 获取仓库列表
    async fetchWarehouseList() {
      this.loading = true;
      try {
        const res = await getWarehouseList();
        console.log('仓库列表API响应:', res); // 添加调试日志

        // 兼容不同的响应格式
        if (res.status === 200 || res.code === 200) {
          // 适配新的数据格式：list接口直接返回数组
          this.warehouseList = res.data || [];
          console.log('解析后的仓库列表:', this.warehouseList); // 添加调试日志

          // 如果有仓库且没有选中的仓库，默认选择第一个
          if (this.warehouseList.length > 0 && !this.selectedWarehouse) {
            this.selectedWarehouse = this.warehouseList[0]._id;
            // 初始化时触发仓库变更事件
            this.handleWarehouseChange(this.selectedWarehouse);
          }
        } else {
          this.$message.error(res.message || '获取仓库列表失败');
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
        this.$message.error('获取仓库列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 处理仓库切换
    handleWarehouseChange(warehouseId) {
      this.$emit('input', warehouseId);
      this.$emit('change', warehouseId);
      
      // 找到选中的仓库信息
      const selectedWarehouse = this.warehouseList.find(item => item._id === warehouseId);
      this.$emit('warehouse-change', selectedWarehouse);
    },

    // 跳转到仓库管理页面
    goToWarehouseManagement() {
      // 在新窗口打开仓库管理页面
      window.open('/admin/ppeWarehouse', '_blank');
    },

    // 刷新仓库列表
    refresh() {
      this.fetchWarehouseList();
    }
  }
};
</script>

<style lang="scss" scoped>
.warehouse-selector {
  .selector-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .selector-label {
    font-weight: 500;
    color: #495057;
    margin-right: 10px;
  }

  .no-permission-tip {
    margin-top: 20px;
  }

  ::v-deep .el-select {
    .el-input__inner {
      border-radius: 4px;
    }
  }

  ::v-deep .el-option {
    height: auto;
    line-height: 1.5;
    padding: 8px 20px;
  }
}
</style>
