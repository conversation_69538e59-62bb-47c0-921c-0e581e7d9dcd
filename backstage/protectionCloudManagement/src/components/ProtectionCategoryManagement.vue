<template>
  <div class="protection-category-management">
    <!-- 顶部操作栏 -->
    <div class="top-bar">
      <div class="left-actions">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增分类</el-button>
        <el-button type="info" icon="el-icon-upload2" @click="handleImport">Excel导入</el-button>
        <el-button
          type="success"
          icon="el-icon-check"
          :disabled="!hasSelection"
          @click="handleBatchEnable"
        >
          批量启用
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-close"
          :disabled="!hasSelection"
          @click="handleBatchDisable"
        >
          批量禁用
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          :disabled="!hasSelection"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>

      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索分类名称或编码"
          style="width: 300px;"
          clearable
          @input="handleSearch"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
    </div>

    <!-- 分类表格 -->
    <el-table
      ref="categoryTable"
      v-loading="loading"
      :data="filteredTableData"
      row-key="_id"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      class="category-table"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column prop="name" label="分类名称" min-width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
          <el-tag
            v-if="scope.row.level"
            :type="getLevelTagType(scope.row.level)"
            size="mini"
            style="margin-left: 8px;"
          >
            {{ getLevelText(scope.row.level) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="code" label="分类编码" width="120" />

      <el-table-column label="适用危害因素" min-width="200">
        <template slot-scope="scope">
          <el-tag
            v-for="factor in scope.row.harmFactors"
            :key="factor._id || factor"
            size="mini"
            style="margin-right: 5px; margin-bottom: 2px;"
          >
            {{ factor.name || factor }}
          </el-tag>
          <span v-if="!scope.row.harmFactors || scope.row.harmFactors.length === 0" class="text-muted">
            未设置
          </span>
        </template>
      </el-table-column>

      <el-table-column label="默认领用周期" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.defaultCycle">
            {{ scope.row.defaultCycle.time }}{{ getCycleUnitText(scope.row.defaultCycle.timeUnit) }}
          </span>
          <span v-else class="text-muted">未设置</span>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

      <el-table-column label="状态" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isActive"
            @change="(value) => handleToggleStatus(scope.row, value)"
          />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleAddChild(scope.row)">添加子分类</el-button>
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="categoryForm"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类名称" prop="name">
              <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类编码" prop="code">
              <el-input v-model="categoryForm.code" placeholder="请输入分类编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="父级分类">
          <el-cascader
            v-model="categoryForm.parentId"
            :options="parentCategoryOptions"
            :props="cascaderProps"
            placeholder="请选择父级分类（可选）"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="适用危害因素">
          <el-select
            v-model="categoryForm.harmFactors"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请搜索并选择危害因素"
            :remote-method="searchHarmFactors"
            :loading="harmFactorsLoading"
            style="width: 100%"
          >
            <el-option
              v-for="item in harmFactorsOptions"
              :key="item._id"
              :label="item.name || item.showName || item.harmFactorName"
              :value="item._id"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认领用周期">
              <el-input-number
                v-model="categoryForm.defaultCycle.time"
                :min="1"
                :max="999"
                style="width: 120px"
              />
              <el-select
                v-model="categoryForm.defaultCycle.timeUnit"
                style="width: 80px; margin-left: 10px"
              >
                <el-option label="天" value="d" />
                <el-option label="周" value="w" />
                <el-option label="月" value="M" />
                <el-option label="季" value="Q" />
                <el-option label="年" value="y" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-switch v-model="categoryForm.isActive" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="分类描述">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>

        <!-- 扩展属性 -->
        <el-form-item label="扩展属性">
          <div class="attributes-container">
            <div v-if="categoryForm.attributes.length === 0" class="text-muted">
              暂无扩展属性，点击下方按钮添加
            </div>
            <div
              v-for="(attr, index) in categoryForm.attributes"
              :key="index"
              class="attribute-item"
            >
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-input v-model="attr.key" placeholder="属性键" />
                </el-col>
                <el-col :span="6">
                  <el-input v-model="attr.label" placeholder="显示名称" />
                </el-col>
                <el-col :span="4">
                  <el-select v-model="attr.type" placeholder="类型">
                    <el-option label="字符串" value="string" />
                    <el-option label="数字" value="number" />
                    <el-option label="布尔" value="boolean" />
                    <el-option label="日期" value="date" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-input v-model="attr.value" placeholder="默认值" />
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="removeAttribute(index)"
                  />
                </el-col>
              </el-row>
            </div>
            <el-button
              type="dashed"
              icon="el-icon-plus"
              style="width: 100%; margin-top: 10px"
              @click="addAttribute"
            >
              添加扩展属性
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSave">
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 导入弹窗 -->
    <category-import-dialog
      :visible.sync="importDialogVisible"
      @import-completed="handleImportCompleted"
    />
  </div>
</template>

<script>
import {
  getProtectionCategoryTree,
  createProtectionCategory,
  updateProtectionCategory,
  deleteProtectionCategory,
  batchProtectionCategory,
  findHarmFactors
} from '@/api/protectionCategory'
import CategoryImportDialog from '@/components/CategoryImportDialog.vue'

export default {
  name: 'ProtectionCategoryManagement',
  data() {
    return {
      loading: false,
      tableData: [],
      filteredTableData: [],
      searchKeyword: '',
      selectedRows: [],
      
      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,
      
      // 对话框
      dialogVisible: false,
      dialogTitle: '新增分类',
      isEditMode: false,
      saveLoading: false,

      // 导入对话框
      importDialogVisible: false,
      
      // 表单
      categoryForm: {
        name: '',
        code: '',
        parentId: null,
        harmFactors: [],
        defaultCycle: {
          time: 1,
          timeUnit: 'M'
        },
        attributes: [],
        description: '',
        isActive: true
      },
      
      categoryRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      
      // 级联选择器
      parentCategoryOptions: [],
      cascaderProps: {
        value: '_id',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      
      // 危害因素
      harmFactorsOptions: [],
      harmFactorsLoading: false
    }
  },
  
  computed: {
    hasSelection() {
      return this.selectedRows.length > 0
    }
  },
  
  created() {
    // 组件创建时的初始化
  },

  mounted() {
    // 组件挂载时加载数据
    this.loadCategoryTree()
  },

  activated() {
    // 如果数据为空，重新加载
    if (this.tableData.length === 0) {
      this.loadCategoryTree()
    }
  },
  
  methods: {
    // 加载分类树
    async loadCategoryTree() {
      this.loading = true
      try {
        const response = await getProtectionCategoryTree({
          activeOnly: false // 获取所有分类，包括禁用的
        })

        if (response.status === 200) {
          this.tableData = response.data || []
          // 处理危害因素显示名称
          await this.populateHarmFactorNames(this.tableData)
          // 更新显示数据和分页
          this.updateDisplayData()
        } else {
          this.$message.error(response.message || '获取分类数据失败')
          this.tableData = []
          this.filteredTableData = []
          this.total = 0
        }
      } catch (error) {
        this.$message.error('加载分类数据失败: ' + (error.message || '未知错误'))
        this.tableData = []
        this.filteredTableData = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 填充危害因素名称
    async populateHarmFactorNames(categories) {
      // 收集所有危害因素ID
      const allHarmFactorIds = new Set()
      const collectIds = (items) => {
        items.forEach(item => {
          if (item.harmFactors && Array.isArray(item.harmFactors)) {
            item.harmFactors.forEach(id => {
              if (typeof id === 'string') {
                allHarmFactorIds.add(id)
              }
            })
          }
          if (item.children && item.children.length > 0) {
            collectIds(item.children)
          }
        })
      }
      collectIds(categories)

      if (allHarmFactorIds.size === 0) return

      // 批量查询危害因素信息
      const harmFactorsMap = new Map()
      try {
        // 一次性查询所有危害因素，避免重复调用接口
        const ids = Array.from(allHarmFactorIds)
        if (ids.length > 0) {
          const response = await findHarmFactors({
            query: JSON.stringify({
              current: 1,
              pageSize: ids.length, // 设置足够大的页面大小
              query: JSON.stringify({ _id: { $in: ids } }), // 使用$in操作符批量查询
              searchkey: '',
              order: 1
            })
          })

          if (response.status === 200 && response.data.data && response.data.data.length > 0) {
            // 将查询结果存储到Map中
            response.data.data.forEach(factor => {
              harmFactorsMap.set(factor._id, {
                _id: factor._id,
                name: factor.showName || factor.harmFactorName || (Array.isArray(factor.chineseName) ? factor.chineseName[0] : factor.chineseName) || '未知危害因素'
              })
            })
          }
        }
      } catch (error) {
        console.error('批量查询危害因素失败:', error)
      }

      // 填充名称
      const fillNames = (items) => {
        items.forEach(item => {
          if (item.harmFactors && Array.isArray(item.harmFactors)) {
            item.harmFactors = item.harmFactors.map(id => {
              if (typeof id === 'string') {
                return harmFactorsMap.get(id) || { _id: id, name: id }
              }
              return id // 如果已经是对象，直接返回
            })
          }
          if (item.children && item.children.length > 0) {
            fillNames(item.children)
          }
        })
      }
      fillNames(categories)
    },

    // 扁平化树形数据
    flattenTree(tree) {
      let result = []
      tree.forEach(node => {
        result.push(node)
        if (node.children && node.children.length > 0) {
          result = result.concat(this.flattenTree(node.children))
        }
      })
      return result
    },
    
    // 搜索处理
    handleSearch() {
      // 重置到第一页
      this.currentPage = 1
      // 更新显示数据（包含搜索和分页逻辑）
      this.updateDisplayData()
    },
    
    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 获取层级标签类型
    getLevelTagType(level) {
      const types = ['', 'primary', 'success', 'warning', 'danger']
      return types[level] || 'info'
    },
    
    // 获取层级文本
    getLevelText(level) {
      const texts = ['', '一级', '二级', '三级', '四级', '五级']
      return texts[level] || `${level}级`
    },
    
    // 获取周期单位文本
    getCycleUnitText(unit) {
      const units = {
        'd': '天',
        'w': '周', 
        'M': '月',
        'Q': '季',
        'y': '年'
      }
      return units[unit] || unit
    },
    
    // 新增分类
    handleAdd() {
      this.dialogTitle = '新增分类'
      this.isEditMode = false
      this.resetForm()
      this.loadParentCategoryOptions()
      this.dialogVisible = true
    },
    
    // 添加子分类
    handleAddChild(row) {
      this.dialogTitle = '新增子分类'
      this.isEditMode = false
      this.resetForm()
      this.categoryForm.parentId = row._id
      this.loadParentCategoryOptions()
      this.dialogVisible = true
    },
    
    // 编辑分类
    handleEdit(row) {
      this.dialogTitle = '编辑分类'
      this.isEditMode = true

      // 处理危害因素数据格式
      let harmFactors = []
      if (row.harmFactors && Array.isArray(row.harmFactors)) {
        harmFactors = row.harmFactors.map(factor => {
          // 如果是对象，返回ID；如果是字符串，直接返回
          return typeof factor === 'object' && factor._id ? factor._id : factor
        })
      }

      this.categoryForm = {
        ...row,
        harmFactors,
        defaultCycle: row.defaultCycle || { time: 1, timeUnit: 'M' },
        attributes: row.attributes || []
      }

      // 预加载危害因素选项用于显示
      if (row.harmFactors && Array.isArray(row.harmFactors)) {
        this.harmFactorsOptions = row.harmFactors.filter(factor =>
          typeof factor === 'object' && factor._id && factor.name
        )
      }

      this.loadParentCategoryOptions()
      this.dialogVisible = true
    },
    
    // 切换状态
    async handleToggleStatus(row, newValue) {
      const originalStatus = !newValue // 保存原始状态
      try {
        await updateProtectionCategory({
          id: row._id,
          isActive: newValue
        })

        this.$message.success(`${newValue ? '启用' : '禁用'}成功`)

        // 优化：直接更新本地数据，避免重新加载导致的页面抖动
        this.updateLocalCategoryStatus(row._id, newValue)
      } catch (error) {
        // 如果请求失败，恢复原始状态
        row.isActive = originalStatus
        this.$message.error('操作失败')
      }
    },
    
    // 删除分类
    handleDelete(row) {
      this.$confirm(`确定要删除分类"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteProtectionCategory({ id: row._id })
          this.$message.success('删除成功')
          this.loadCategoryTree()
        } catch (error) {
          this.$message.error((error.response && error.response.data && error.response.data.message) || '删除失败')
        }
      })
    },
    
    // 批量启用
    handleBatchEnable() {
      this.batchOperation('enable', '启用')
    },
    
    // 批量禁用
    handleBatchDisable() {
      this.batchOperation('disable', '禁用')
    },
    
    // 批量删除
    handleBatchDelete() {
      this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个分类吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchOperation('delete', '删除')
      })
    },
    
    // 批量操作
    async batchOperation(action, actionText) {
      try {
        const ids = this.selectedRows.map(row => row._id)

        await batchProtectionCategory({ action, ids })
        this.$message.success(`批量${actionText}成功`)

        // 对于启用/禁用操作，使用本地更新避免页面抖动
        if (action === 'enable' || action === 'disable') {
          const newStatus = action === 'enable'
          this.updateLocalCategoriesStatus(ids, newStatus)
        } else {
          // 对于删除操作，保存滚动位置后重新加载
          // const scrollPosition = this.saveScrollPosition()
          await this.loadCategoryTree()
          // this.$nextTick(() => {
          //   this.restoreScrollPosition(scrollPosition)
          // })
        }

        this.$refs.categoryTable.clearSelection()
      } catch (error) {
        this.$message.error((error.response && error.response.data && error.response.data.message) || `批量${actionText}失败`)
      }
    },

    // 本地更新分类状态（避免重新加载导致的页面抖动）
    updateLocalCategoryStatus(categoryId, newStatus) {
      const updateInTree = (nodes) => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i]._id === categoryId) {
            nodes[i].isActive = newStatus
            return true
          }
          if (nodes[i].children && nodes[i].children.length > 0) {
            if (updateInTree(nodes[i].children)) {
              return true
            }
          }
        }
        return false
      }

      // 更新原始数据
      updateInTree(this.tableData)
      // 更新显示数据
      this.updateDisplayData()
    },

    // 批量更新本地分类状态
    updateLocalCategoriesStatus(categoryIds, newStatus) {
      const updateInTree = (nodes) => {
        nodes.forEach(node => {
          if (categoryIds.includes(node._id)) {
            node.isActive = newStatus
          }
          if (node.children && node.children.length > 0) {
            updateInTree(node.children)
          }
        })
      }

      // 更新原始数据
      updateInTree(this.tableData)
      // 更新显示数据
      this.updateDisplayData()
    },

    // 保存滚动位置
    saveScrollPosition() {
      const tableWrapper = this.$el.querySelector('.el-table__body-wrapper')
      if (tableWrapper) {
        return {
          scrollTop: tableWrapper.scrollTop,
          scrollLeft: tableWrapper.scrollLeft
        }
      }
      return { scrollTop: 0, scrollLeft: 0 }
    },

    // 恢复滚动位置
    restoreScrollPosition(position) {
      if (!position) return

      const tableWrapper = this.$el.querySelector('.el-table__body-wrapper')
      if (tableWrapper) {
        tableWrapper.scrollTop = position.scrollTop
        tableWrapper.scrollLeft = position.scrollLeft
      }
    },

    // 保存分类
    async handleSave() {
      try {
        await this.$refs.categoryForm.validate()
        this.saveLoading = true
        
        if (this.isEditMode) {
          await updateProtectionCategory({
            id: this.categoryForm._id,
            ...this.categoryForm
          })
          this.$message.success('更新成功')
        } else {
          await createProtectionCategory(this.categoryForm)
          this.$message.success('创建成功')
        }
        
        this.dialogVisible = false
        this.loadCategoryTree()
      } catch (error) {
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('保存失败')
        }
      } finally {
        this.saveLoading = false
      }
    },
    
    // 重置表单
    resetForm() {
      this.categoryForm = {
        name: '',
        code: '',
        parentId: null,
        harmFactors: [],
        defaultCycle: {
          time: 1,
          timeUnit: 'M'
        },
        attributes: [],
        description: '',
        isActive: true
      }

      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.clearValidate()
      }
    },

    // 添加扩展属性
    addAttribute() {
      this.categoryForm.attributes.push({
        key: '',
        value: '',
        label: '',
        type: 'string',
        required: false
      })
    },

    // 移除扩展属性
    removeAttribute(index) {
      this.categoryForm.attributes.splice(index, 1)
    },
    
    // 对话框关闭
    handleDialogClose() {
      this.resetForm()
    },
    
    // 加载父级分类选项
    async loadParentCategoryOptions() {
      try {
        const response = await getProtectionCategoryTree({
          activeOnly: true // 父级分类选项只显示启用的分类
        })
        if (response.status === 200) {
          this.parentCategoryOptions = response.data || []
        }
      } catch (error) {
        // 加载父级分类失败，使用空数组
      }
    },
    
    // 搜索危害因素
    async searchHarmFactors(query) {
      if (!query) {
        this.harmFactorsOptions = []
        return
      }

      this.harmFactorsLoading = true
      try {
        // 将参数包装在query字段中，这样后端可以正确接收
        const response = await findHarmFactors({
          query: JSON.stringify({
            current: 1,
            pageSize: 50,
            query: JSON.stringify({}),
            searchkey: query,
            order: 1
          })
        })
        if (response.status === 200) {
          // 处理返回的数据，确保每个项目都有正确的name字段
          const data = response.data.data || []

          // 在前端进行严格过滤，只保留名称字段中真正包含搜索关键词的结果
          const filteredData = data.filter(item => {
            const showName = item.showName || ''
            const chineseName = Array.isArray(item.chineseName) ? item.chineseName.join('，') : (item.chineseName || '')
            const harmFactorName = item.harmFactorName || ''

            // 检查是否在主要的名称字段中包含搜索关键词（不区分大小写）
            const searchLower = query.toLowerCase()

            // 更严格的匹配：只在showName和chineseName中搜索
            const showNameMatch = showName.toLowerCase().includes(searchLower)
            const chineseNameMatch = chineseName.toLowerCase().includes(searchLower)
            const harmFactorNameMatch = harmFactorName.toLowerCase().includes(searchLower)

            return showNameMatch || chineseNameMatch || harmFactorNameMatch
          })

          console.log(`搜索"${query}"，后端返回${data.length}条，前端过滤后${filteredData.length}条`)

          this.harmFactorsOptions = filteredData.map(item => ({
            ...item,
            name: item.showName || item.harmFactorName || (Array.isArray(item.chineseName) ? item.chineseName[0] : item.chineseName) || '未知危害因素'
          }))
        }
      } catch (error) {
        console.error('搜索危害因素失败:', error)
        this.harmFactorsOptions = []
      } finally {
        this.harmFactorsLoading = false
      }
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.handleCurrentChange(1)
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.updateDisplayData()
    },

    // 更新显示数据（实现分页）
    updateDisplayData() {
      if (!this.tableData || this.tableData.length === 0) {
        this.filteredTableData = []
        this.total = 0
        return
      }

      // 扁平化树形数据用于分页
      const flatData = this.flattenTree(this.tableData)

      // 应用搜索过滤
      let filteredData = flatData
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase()
        filteredData = flatData.filter(item => {
          return item.name.toLowerCase().includes(keyword) ||
                 (item.code && item.code.toLowerCase().includes(keyword))
        })
      }

      // 更新总数
      this.total = filteredData.length

      // 计算分页
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      const pagedData = filteredData.slice(start, end)

      // 对于分页后的数据，我们直接显示为扁平列表，不重建树形结构
      // 因为分页会打断树形结构的完整性
      this.filteredTableData = pagedData.map(item => ({
        ...item,
        children: undefined // 移除children，显示为扁平列表
      }))
    },


    // ==================== 导入功能 ====================

    // 打开导入对话框
    handleImport() {
      this.importDialogVisible = true
    },

    // 导入完成处理
    handleImportCompleted(result) {
      this.$message.success(`导入完成！成功：${result.success}条，跳过：${result.skipped}条，错误：${result.errors}条`)

      // 重新加载分类数据
      this.loadCategoryTree()

      // 关闭导入对话框
      this.importDialogVisible = false
    }
  },

  components: {
    CategoryImportDialog
  }
}
</script>

<style scoped lang="scss">
.protection-category-management {
  padding: 20px;
  
  .top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .search-box {
      margin-left: auto;
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
  
  .dialog-footer {
    text-align: right;
  }

  .attributes-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;

    .attribute-item {
      margin-bottom: 15px;
      padding: 10px;
      background-color: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

::v-deep .el-table {
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 表格样式优化，减少滚动条变化导致的页面抖动
.category-table {
  // 设置表格最小高度，避免数据变化时高度跳动
  min-height: 400px;

  // 表格体固定高度，确保滚动条稳定
  ::v-deep .el-table__body-wrapper {
    min-height: 350px;
    // 平滑滚动
    scroll-behavior: smooth;
  }

  // 优化表格行的过渡效果
  ::v-deep .el-table__row {
    transition: all 0.2s ease;
  }

  // 状态切换时的平滑过渡
  ::v-deep .el-switch {
    transition: all 0.3s ease;
  }
}

// 分页组件样式
.pagination-wrapper {
  margin-top: 20px;
  text-align: right;

  // 固定分页高度，避免布局跳动
  min-height: 32px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
