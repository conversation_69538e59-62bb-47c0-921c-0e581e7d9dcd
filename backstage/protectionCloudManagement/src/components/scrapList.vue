<template>
  <div style="text-align: left">
    <el-tabs type="border-card" v-model="isSelected" @tab-click="changeMouth">
      <el-tab-pane v-for="(item, i) in dateFilter" :key="i" :label="item" :name="i">
        <div class="topBar">
          <div class="left">
            <span style="font-size: 14px;">年份：</span>
            <el-date-picker
              v-model="year"
              type="year"
              clearable="false"
              placeholder="选择年份"
              size="mini"
              style="margin-right: 10px"
              @change="handleCommand"
            >
            </el-date-picker>
            <el-button type="primary" icon="el-icon-download" size="mini" @click="exportMouth" style="margin-bottom: 15px">导出月度</el-button>
          </div>
          <div class="right">
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              clearable
              size="mini"
              style="width: 150px; margin-right: 10px;"
              @change="search">
              <el-option
                v-for="category in categoryOptions"
                :key="category.value"
                :label="category.label"
                :value="category.value">
              </el-option>
            </el-select>
            <el-select
              v-model="selectedWarehouse"
              placeholder="选择仓库"
              clearable
              size="mini"
              style="width: 150px; margin-right: 10px;"
              @change="search">
              <el-option
                v-for="warehouse in warehouseOptions"
                :key="warehouse.value"
                :label="warehouse.label"
                :value="warehouse.value">
              </el-option>
            </el-select>
            <el-input
              placeholder="请输入内容"
              suffix-icon="el-icon-search"
              v-model="searchKey"
              style="width: 240px; margin-right: 10px;"
              @change="search(searchKey)"
              clearable
              size="mini">
            </el-input>
          </div>
        </div>
        <el-table
          :data="listData"
          ref="table"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column
            :selectable="getSelectIndex"
            type="selection"
            align="center"
          ></el-table-column>
          <el-table-column
            label="序号"
            type="index"
            width="60"
            align="center"
          ></el-table-column>

          <el-table-column
            v-for="item in tableHeader"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
            align="center"
          >
            <template slot-scope="scope">
              <div v-if="item.prop === 'products'">
                <div v-for="product in scope.row.products" @click="openInfo(product)" class="tableContainer">
                  {{ product.product }}
                  <span v-if="product.modelNumber">({{ product.modelNumber }})</span>
                  <span v-if="product.productSpec"> - {{ product.productSpec }}</span>
                  × {{ product.number }}
                </div>
              </div>
              <div v-else-if="item.prop === 'categoryName'">
                <div>{{ scope.row.categoryName || scope.row.categoryPath || '-' }}</div>
              </div>
              <div v-else-if="item.prop === 'warehouseName'">
                <div>{{ (scope.row.warehouseInfo && scope.row.warehouseInfo.name) || scope.row.warehouseName || scope.row.warehouseId || '-' }}</div>
              </div>
              <div v-else-if="item.prop === 'status'">
                <span v-if="scope.row.status === 0" class="undoBtn">未审核</span>
                <span v-else class="doBtn">已报废</span>
              </div>
              <div v-else-if="item.prop === 'employeeInfo'">
                <div>{{ scope.row.employeeInfo.length > 0 ? scope.row.employeeInfo[0].name : '' }}</div>
              </div>
              <div v-else-if="item.prop === 'operator'">
                <div>{{ scope.row.operator ? scope.row.operator.name : '' }}</div>
              </div>
              <div v-else-if="item.prop === 'workshop'">
                <div>
                  <span v-if="scope.row.workshop || scope.row.workspaces || scope.row.workstation">
                    {{ scope.row.workshop || '' }}
                    <span v-if="scope.row.workspaces"> / {{ scope.row.workspaces }}</span>
                    <span v-if="scope.row.workstation"> / {{ scope.row.workstation }}</span>
                  </span>
                  <span v-else>-</span>
                </div>
              </div>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="scope">
              <!-- <span v-if="scope.row.status === 0" class="undo" @click="confirm(scope.row)">确认</span>
              <span v-else class="done">已通过</span> -->
              <!-- <span class="delete" @click="">删除</span> -->
              <div class="btn-box" v-if="branch === 'wh'">
                <div class="btn btn1" style="width: 100%;">
                  <el-button v-if="scope.row.status === 0" @click.prevent="confirm(scope.row)" type="primary" size="small">确认</el-button>
                </div>
              </div>
              <div class="btn-box" v-else>
                <div class="btn btn1">
                  <el-button v-if="scope.row.status === 0" @click.prevent="confirm(scope.row)" type="primary" size="small">确认</el-button>
                </div>
                <div class="btn btn2" >
                  <el-button @click.prevent="deleteRecord(scope.row._id, 'line')" type="danger" size="small">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pagebreak">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="current"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalLength"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <ProductInfo v-if="productInfoShow" :productInfo="productInfo" @closeDialog="handleCloseDialog" />
  </div>
</template>

<script>
import { getScrapProducts, confirmScrap, deleteScrapRecord, getWarehouseList } from "@/api";
import { scrapOutputFn } from '../utils/output.js'
import moment from "moment";
import ProductInfo from "./ProductInfo";
import { mapGetters } from "vuex";
export default {
  components: {
    ProductInfo,
  },
  data() {
    return {
      year: "",
      selectedTime: 'time1',
      listData: [],
      current: 1,
      pageSize: 30,
      pageSizes: [10, 20, 30, 50],
      totalLength: 0, // 数量
      isSelected: 0, // 默认当前月份
      searchKey: "", // 关键字
      selectedCategory: "", // 选择的分类
      categoryOptions: [], // 分类选项
      selectedWarehouse: "", // 选择的仓库
      warehouseOptions: [], // 仓库选项
      multipleSelection: [],
      dateFilter: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      tableHeader: [
        {
          prop: "workshop",
          label: "工作场所",
          width: "200",
        },
        {
          prop: "categoryName",
          label: "防护用品分类",
          width: "150",
        },
        {
          prop: "warehouseName",
          label: "仓库",
          width: "120",
        },
        {
          prop: "products",
          label: "报废用品",
        },
        {
          prop: "employeeInfo",
          label: "申请人",
          width: "120"
        },
        {
          prop: "status",
          label: "状态",
          width: "100"
        },
        {
          prop: "scrapTime",
          label: "报废时间",
          width: "170"
        },
        {
          prop: "operator",
          label: "操作人",
          width: "120"
        },
      ],
      productInfoShow: false,
      productInfo: {},
    };
  },

  mounted() {
    const now = new Date();
    this.isSelected = now.getMonth();
    this.year = now.getFullYear() + "";
    this.getList();
    this.getWarehouseOptions();
  },

  computed: {
    ...mapGetters(["defendProductList","branch"]),
  },

  methods: {
    // 获取数据
    async getList() {
      const params = {
        current: this.current,
        pageSize: this.pageSize,
        isSelected: this.isSelected * 1 + 1, // 当前月份
        year: this.year * 1, // 当前年
        query: {
          searchKey: this.searchKey, // 关键字
          categoryName: this.selectedCategory, // 分类筛选（使用分类名称）
          warehouseId: this.selectedWarehouse, // 仓库筛选（使用仓库ID）
        }
      }
      const res = await getScrapProducts(params);
      console.log(res, 'scrapRes');
      if (res.status === 200) {
        this.listData = res.data.data;
        this.totalLength = res.data.totalLength;

        // 提取分类选项（去重）
        this.updateCategoryOptions();
      }
    },

    // 更新分类选项
    updateCategoryOptions() {
      const categories = new Set();
      this.listData.forEach(item => {
        if (item.categoryName) {
          categories.add(item.categoryName);
        }
        // 也从产品中提取分类信息
        if (item.products && item.products.length > 0) {
          item.products.forEach(product => {
            if (product.categoryName) {
              categories.add(product.categoryName);
            }
          });
        }
      });

      this.categoryOptions = Array.from(categories).map(name => ({
        label: name,
        value: name
      }));
    },

    // 获取仓库选项
    async getWarehouseOptions() {
      try {
        console.log('开始获取仓库列表...');
        const response = await getWarehouseList(); // 不需要传递参数

        console.log('仓库列表API响应:', response);

        // 兼容不同的响应格式
        if (response.status === 200 || response.code === 200) {
          // 适配新的数据格式：list接口直接返回数组
          const warehouseList = response.data || [];
          console.log('仓库数据:', warehouseList);

          this.warehouseOptions = warehouseList.map(warehouse => ({
            label: warehouse.name || warehouse.warehouseName || warehouse._id,
            value: warehouse._id
          }));
          console.log('处理后的仓库选项:', this.warehouseOptions);
        } else {
          console.warn('仓库数据格式不正确:', response);
          this.$message.error(response.message || '获取仓库列表失败');
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
        this.$message.error('获取仓库列表失败');
      }
    },

    // 改变页面显示数量
    handleSizeChange(e) {
      this.pageSize = e;
      this.getList();
    },

    // 改变当前页
    handleCurrentChange(e) {
      this.current = e;
      this.getList();
    },
    
    // 选择年份
    handleCommand(command) {
      console.log(command, '选择年份');
      this.year = moment(command).format("YYYY");
      // this.year = command;
      this.getList();
    },

    // 选择月份
    changeMouth(tab) {
      this.isSelected = tab.name * 1;
      this.getList()
    },

    // 确认报废方法
    async confirm(row) {
      this.$confirm('是否确认该用品报废了？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await confirmScrap({id: row._id});
        if (res.status === 200) {
        this.$message({
          message: res.data,
          type: "success",
        });
        this.getList();
      }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });          
      });
    },

    handleCloseDialog() {
      this.productInfoShow = false;
    },

    // 删除报废记录
    async deleteRecord(row, type) {
      const arr = [];
      if (type === 'all') {
        row.forEach(item => {
          arr.push(item._id);
        })
      } else {
        arr.push(row);
      }
      this.$confirm('是否确认删除该记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteScrapRecord({ids: arr});
        if (res.status === 200) {
        this.$message({
          message: '删除成功！',
          type: "success",
        });
        this.getList();
      }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });          
      });
    },

    //
    handleSelectionChange(val) {
      console.log(val, '111111');
      console.log(this.multipleSelection, '22222');
      this.multipleSelection = val;
    },

    // 搜索方法
    search() {
      this.getList();
    },

    // 导出月度记录
    async exportMouth() {
      this.$confirm(`是否导出${this.year}年${this.isSelected * 1 + 1}月的报废记录？`, '导出记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        scrapOutputFn({
          mouthNumber: this.isSelected * 1 + 1,
          yearNumber: this.year,
        }, this) // 传递Vue实例以便显示提示
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },

    // 批量通过
    async batchPass() {
      this.$confirm(`是否批量通过${this.year}年${this.isSelected * 1 + 1}月的未审核报废记录？`, '批量审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        const res = await confirmScrap({
          mouthNumber: this.isSelected * 1 + 1,
          yearNumber: this.year,
        })
        if (res.status === 200) {
          this.$message({
            message: res.data,
            type: "success",
          });
          this.getList();
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });          
      });
    },
    
    // 打开详细信息
    openInfo(row) {
      this.productInfoShow = true;
      console.log(row, 'row');
      this.defendProductList.forEach(product => {
        if (product._id === row.productIds[0]) {
          product.data.forEach(item => {
            if (item._id === row.productIds[1]) {
              this.productInfo = item;
            }
          })
        }
      })
    },
  },
};
</script>

<style scoped>
.topBar {
  display: flex;
  justify-content: space-between;
}

element.style {
  z-index: 2007;
}
.el-date-editor.el-input {
  width: 135px;
}
.pagebreak {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-table colgroup.gutter {
  display: table-cell !important;
}
.el-range-editor.el-input__inner {
  position: absolute;
  right: 90px;
}

.el-input__inner {
  width: 83%;
}

.box-card {
  width: 98%;
  height: 100%;
  margin: 0 auto;
}

.font-small {
  font-size: 14px;
  color: rgb(200, 208, 214);
  margin: 0 auto;
  text-align: center;
}

.corss-line {
  margin-top: 30px;
  border: 1px solid rgb(200, 208, 214);
}

.buttons {
  margin-top: 30px;
  text-align: center;
}

.uploadImage {
  width: 90%;
}

.template {
  position: relative;
  display: flex;
  /* margin-top: 20px; */
  margin-bottom: 20px;
}

.undoBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: rgba(0,0,0,0.10);
  border-radius: 4px;
  text-align: center;
  color: #aaaaaa;
  line-height: 24px;
}

.doBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: #ddf3d8;
  border-radius: 4px;
  font-weight: 400;
  text-align: center;
  color: #00cd18;
  line-height: 24px;
}

.reBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: #ffe4cb;
  border-radius: 4px;
  font-weight: 400;
  text-align: center;
  color: #f97d0b;
  line-height: 24px;
}

.undo {
  color: #6dd400;
  cursor: pointer;
}

.done {
  color: #aaaaaa;
}

.delete {
  color: #F46C6C;
  cursor: pointer;
  margin-left: 16px;
}

.btn-box {
  display: flex;
  justify-content: space-between;
}

.btn {
  width: 50%;
}

.tableContainer {
  cursor: pointer;
}

.tableContainer:hover {
  color: #409EFF;
}

</style>
