<template>
  <div>
    <el-dialog
      title="新建"
      :visible="dialogForm"
      class="dialog"
      width="700px"
      top="5vh"
      :show-close="false"
      :modal-append-to-body="false"
    >
      <el-form
        v-if="dialogForm"
        :model="addPage"
        label-position="right"
        label-width="85px"
      >
        <el-form-item label="发放区域">
          <el-cascader
            ref="myCascader"
            v-model="selectMillConstructions"
            :options="millConstructions"
            @change="handleChange"
            :props="cascaderProps"
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="危害因素">
          <div class="harmFactorInfo" v-for="item in harmFactorInfo">
            <span>{{ item.label }}</span>
            ：
            <span>{{ item.harmFactors.join("、") }}</span>

            <span v-if="!item.harmFactors || item.harmFactors.length === 0">
              暂无接触危害因素
            </span>
          </div>
        </el-form-item>

        <el-form-item label="子区域">
          <el-cascader
            ref="myCascaderChild"
            v-model="selectChildMills"
            :options="childMillConstructions"
            @change="handleChildChange"
            :props="childCascaderProps"
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        
        <el-form-item v-if="branch === 'wh'" label="标签">
          <el-select v-model="formData.category" placeholder="请选择标签" style="width: 100%">
            <el-option v-for="option in categoryOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
          </el-select>
        </el-form-item>

        <el-divider content-position="center"> 防护用品 </el-divider>

        <div class="tabs">
          <el-button class="addBtn" size="mini" @click="add">新增</el-button>
          <el-tabs
            v-model="editableTabsValue"
            type="card"
            closable
            @tab-remove="removeTab"
          >
            <el-tab-pane
              v-for="(item, index) in formData.products"
              :key="index"
              :label="item.product"
              :name="index"
            >
              <span slot="label">
                {{ item.product ? item.product : '请选择' }}
              </span>
              <el-form-item label="防护用品">
                <el-cascader
                  ref="protectionCascader"
                  v-model="item.productType"
                  :options="productList"
                  @change="(e) => handleChangepProtection(e, item, index)"
                  :props="cascaderProps_protection"
                  style="width: 100%"
                >
                  <template slot-scope="{ node, data }">
                    <span v-if="data.type === 'category'">{{ data.product }}</span>
                    <span v-else>
                      {{ data.product }}
                    </span>
                  </template>
                </el-cascader>
              </el-form-item>
              <el-form-item label="数量">
                <el-input v-model="item.number" />
              </el-form-item>
              <el-form-item v-if="branch === 'wh'" label="首次发放最大允许量">
                <el-input v-model="item.maxNumber" />
              </el-form-item>

              <el-form-item label="领用周期">
                <el-input v-model="item.time">
                  <el-select
                    style="width: 100px"
                    v-model="item.timeUnit"
                    slot="append"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in timeUnit"
                      :label="item.label"
                      :value="item.value"
                      :key="item.value"
                    ></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForm = false">取 消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addProtectionPlan } from "../api/index.js";
import _ from "loadsh";
import { mapState } from "vuex";
export default {
  data() {
    return {
      dialogForm: false,
      formData: {},
      editableTabsValue: 0,
      millConstructions: [],
      childMillConstructions: [],
      selectMillConstructions: [],
      selectChildMills: [],
      harmFactorInfo: [],
      cascaderProps: {
        label: "name",
        value: "_id",
        checkStrictly: true,
        multiple: false,
      },
      childCascaderProps: {
        label: "name",
        value: "_id",
        checkStrictly: true,
        multiple: true,
      },
      cascaderProps_protection: {
        label: "product",
        value: "product",
        children: "data",
      },
      productList: [],
      timeUnit: [
        { label: "天", value: "d" },
        { label: "周", value: "w" },
        { label: "月", value: "M" },
        { label: "季", value: "Q" },
        { label: "年", value: "y" },
      ],
      categoryOptions: [
        { label: "生产岗位", value: "1" },
        { label: "生产类职能岗", value: "2" },
        { label: "非生产类职能岗", value: "3" },
        { label: "工程管理岗", value: "4" },
        { label: "其他", value: "5" },
      ],
      grantType: '',
    };
  },
  computed: {
    ...mapState('protectionCloudManagement', ['branch'])
  },
  methods: {
    // 将分类树转换为productList格式，支持多级级联选择器（自适应层级），参照ConfigDialog.vue
    transformCategoryTreeToProductList(categoryTree) {
      const productList = [];

      // 递归处理分类树，构建多级级联结构
      const processCategory = (category, parentPath = '') => {
        const currentPath = parentPath ? `${parentPath}/${category.name}` : category.name;

        // 如果有子分类，创建中间节点
        if (category.children && category.children.length > 0) {
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          // 递归处理所有子分类
          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          // 只有当有子数据时才添加到结果中
          if (categoryNode.data.length > 0) {
            return categoryNode;
          }
        } else {
          // 叶子节点，直接返回产品信息
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name
          };
        }
      };

      // 处理子分类的辅助方法
      this.processChildCategory = (category, parentPath) => {
        const currentPath = `${parentPath}/${category.name}`;

        if (category.children && category.children.length > 0) {
          // 有子分类，创建中间节点
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          return categoryNode.data.length > 0 ? categoryNode : null;
        } else {
          // 叶子节点
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name
          };
        }
      };

      // 处理顶级分类
      categoryTree.forEach(category => {
        const result = processCategory(category);
        if (result) {
          productList.push(result);
        }
      });

      return productList;
    },

    getTooltipContent(data) {
      return data.tooltipContent || ''; // 如果有 tooltipContent 属性就显示，否则为空字符串
    },

    async showModal(obj) {
      if (obj.success) {
        this.success = obj.success;
      } else {
        this.success = () => {};
      }
      if (obj.grantType) {
        this.grantType = obj.grantType
      }

      if (obj.productList) {
        // 参照ConfigDialog.vue的多级自适应结构处理逻辑
        this.productList = this.transformCategoryTreeToProductList(_.cloneDeep(obj.productList));
      }
      await this.initForm();
      this.editableTabsValue = 0;

      if (obj.millConstructions) {
        let newMill = _.cloneDeep(obj.millConstructions);

        // 筛选出当前企业
        if (obj.EnterpriseID) {
          newMill = newMill.filter(e => e.EnterpriseID === obj.EnterpriseID)
        }
        this.handleEmployees(newMill);

        this.millConstructions = newMill;
      } else {
        this.millConstructions = [];
      }

      this.selectChildMills = []
      this.childMillConstructions = []

      this.dialogForm = true;
    },
    initForm() {
      this.formData = {
        products: [
          {
            product: "",
          },
        ],
        category: "",
      };
      this.selectMillConstructions = [];
      this.harmFactorInfo = [];
    },
    handleChange(e) {
      this.$nextTick(() => {
        const checkNodes = this.$refs.myCascader.getCheckedNodes();
        this.$refs.myCascaderChild.handleClear()
        this.$refs.myCascaderChild.panel.activePath = []

        this.childMillConstructions = []

        const selectChildMills = []
        this.childMillConstructions = _.cloneDeep(checkNodes[0].data)
        this.hanleSelectAllChildMill(_.cloneDeep(checkNodes[0].data), selectChildMills, [])

        this.selectChildMills = selectChildMills

        const harmFactorInfo = [];
        // 根据组合
        for (let i = 0; i < checkNodes.length; i++) {
          const checkNode = checkNodes[i];
          let harmFactors = [];
          this.findHarmFactors(harmFactors, checkNode);
          harmFactors = Array.from(new Set(harmFactors));
          const data = {
            label: checkNode.label,
            harmFactors,
          };
          harmFactorInfo.push(data);
        }
        this.harmFactorInfo = harmFactorInfo;
      });
    },
    hanleSelectAllChildMill(mill, data, parentIds = []) {
      data.push(parentIds.concat([ mill._id ]))
      
      if (mill.children) {
        const newParentIds = parentIds.concat([ mill._id ])
        for (let i = 0; i < mill.children.length; i++) {
          const child = mill.children[i];

          this.hanleSelectAllChildMill(child, data, newParentIds)
        }
      }
    },
    handleChildChange(e) {
      console.log(23333333, e)
    },
    handleChangepProtection(e, item, index) {
      const checkNodes = this.$refs.protectionCascader[index].getCheckedNodes()[0];

      // 检查是否选择了分类数据（新的分类系统）
      if (checkNodes.data && checkNodes.data.categoryId) {
        // 新的分类系统数据
        item.categoryId = checkNodes.data.categoryId;
        item.categoryPath = checkNodes.data.categoryPath;
        item.categoryName = checkNodes.data.categoryName;
        item.product = checkNodes.data.categoryName;
      } else {
        // 兼容原有的防护用品清单数据
        item.product = checkNodes.label;
      }
    },
    findHarmFactors(harmFactors, item) {
      // 如果不存在category 就是人员
      if (!item.data.category) {
        if (item.parent.data.harmFactors) {
          harmFactors.push(...item.parent.data.harmFactors.map((e) => e[1]));
        }
      }

      if (item.data.harmFactors) {
        harmFactors.push(...item.data.harmFactors.map((e) => e[1]));
      }
      if (item.children) {
        for (let i = 0; i < item.children.length; i++) {
          this.findHarmFactors(harmFactors, item.children[i]);
        }
      }
    },
    async submit() {
      // 校验参数
      if (!this.selectMillConstructions || this.selectMillConstructions.length === 0) {
        this.$message({
          message: "请先选择发放区域",
          type: "warning",
        });
        return;
      }

      let validMessage = "";
      this.formData.products.forEach((e, index) => {
        if (
          !e.product ||
          !e.number ||
          !e.time ||
          !e.timeUnit ||
          !e.productType ||
          e.productType.length === 0
        ) {
          validMessage = `请完善第${index + 1}个防护用品的内容`;
        }
      });

      if (validMessage) {
        this.$message({
          message: validMessage,
          type: "warning",
        });
        return;
      }
      const formData = JSON.parse(JSON.stringify(this.formData));
      const formatMill = _.cloneDeep(this.selectMillConstructions).map((item, index) => {
        const target = this.findTargetMill(item, this.millConstructions);
        let type = "";

        if (this.grantType === 'mill') {
          type = target.category;
        } else if (this.grantType === 'depart') {
          if (!target.unitCode) {
            type = 'org';
          } else {
            type = 'depart'
          }
        }

        return {
          id: target._id,
          type,
          name: target.name
        };
      });

      const data = [];
      const mill = formatMill;
      const item = {
        formData: this.formData,
        grantType: this.grantType,
        selectChildMills: this.selectChildMills,
      };

      if (this.grantType === 'mill') {
        for (let j = 0; j < mill.length; j++) {
          const e = mill[j];
          let key = "";
          if (e.type === "mill") {
            key = "workshop";
          } else if (e.type === "workspaces") {
            key = "workspaces";
          } else if (e.type === "stations") {
            key = "workstation";
          } else if (e.type === "employee") {
            key = "employee";
          } else if (e.type === "enterprises") {
            key = "enterprises";
          }
          item[key] = e.id;
          item[key+'Name'] = e.name;
        }
      } else if (this.grantType === 'depart') {
        item.departId = mill[mill.length - 1].id
        item.departName = (formatMill && formatMill[formatMill.length - 1]) ? formatMill[formatMill.length - 1].name : ''
      }
      data.push(item);

      const res = await addProtectionPlan({
        data: item,
      });

      if (res && res.status === 200) {
        this.$message({
          type: "success",
          message: res.message,
        });
        this.dialogForm = false;
        this.$emit("submitSuccess");
      }
      console.log(231213, res);
    },
    findTargetMill(targetId, mills) {
      for (let i = 0; i < mills.length; i++) {
        const item = mills[i];
        if (item._id === targetId) {
          return item;
        }
        if (item.children) {
          const res = this.findTargetMill(targetId, item.children);
          if (res) {
            return res;
          }
        }
      }
    },
    handleEmployees(arr) {
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (item.children && item.children.length > 0) {
          if (item.category === "stations") {
            delete item.children;
          } else {
            this.handleEmployees(item.children);
          }
        } else {
          delete item.children;
        }
        if (item.employees) {
          item.name = item.employees.name;
          item._id = item.employees._id;
          delete item.children;
        }
      }
    },
    add() {
      this.formData.products.push({
        product: "",
      });
      this.editableTabsValue = this.formData.products.length - 1;
    },
    removeTab(targetName) {
      if (this.formData.products.length === 1) {
        this.$message({
          type: "warning",
          message: "至少保留一个防护用品",
        });
        return;
      }
      this.formData.products.splice(targetName, 1);
    },
  },
};
</script>

<style lang="scss" scoped>
.harmFactorInfo {
  text-align: left;
  color: #409eff;
}

.tabs {
  position: relative;
}
.addBtn {
  position: absolute;
  right: 0;
  z-index: 99999;
}

// Add styles for form label spacing
:deep(.el-form-item) {
  .el-form-item__label {
    line-height: 1.2;
    padding-bottom: 8px;
    white-space: normal;
    height: auto;
    word-break: break-all;
  }
  
  // Adjust vertical alignment for the form item content
  .el-form-item__content {
    line-height: 32px;
    display: flex;
    align-items: center;
  }
}

// Ensure consistent form item spacing
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 18px;
  }
}
</style>
