<template>
  <div>
    <el-dialog
      title="新增配发标准"
      :visible="dialogForm"
      class="dialog"
      width="700px"
      top="5vh"
      :show-close="false"
      :modal-append-to-body="false"
    >
      <!-- 骨架屏 loading -->
      <div v-if="loading" style="padding: 20px;">
        <el-skeleton :rows="8" animated />
      </div>

      <el-form
        v-else-if="dialogForm"
        :model="addPage"
        label-position="right"
        label-width="85px"
      >
        <el-form-item label="发放区域">
          <el-cascader
            ref="myCascader"
            v-model="selectMillConstructions"
            :options="millConstructions"
            @change="handleChange"
            :props="cascaderProps"
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="危害因素">
          <div class="harmFactorInfo" v-for="item in harmFactorInfo" :key="item.label">
            <span>{{ item.label }}</span>：
            <span>{{ item.harmFactors.join("、") }}</span>
            <span v-if="!item.harmFactors || item.harmFactors.length === 0">
              暂无接触危害因素
            </span>
          </div>
        </el-form-item>
        <el-form-item label="子区域">
          <el-cascader
            ref="myCascaderChild"
            v-model="selectChildMills"
            :options="childMillConstructions"
            @change="handleChildChange"
            :props="childCascaderProps"
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        
        <el-form-item v-if="branch === 'wh'" label="标签">
          <el-select v-model="formData.category" placeholder="请选择标签" style="width: 100%">
            <el-option v-for="option in categoryOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
          </el-select>
        </el-form-item>

        <el-divider content-position="center"> 防护用品 </el-divider>

        <div class="tabs">
          <el-button class="addBtn" size="mini" @click="add">新增</el-button>
          <el-tabs
            v-model="editableTabsValue"
            type="card"
            closable
            @tab-remove="removeTab"
          >
            <el-tab-pane
              v-for="(item, index) in formData.products"
              :key="index"
              :label="item.product"
              :name="index"
            >
              <span slot="label">
                {{ item.product ? item.product : '请选择' }}
              </span>
              <el-form-item label="防护用品">
                <el-cascader
                  ref="protectionCascader"
                  v-model="item.productType"
                  :options="productList"
                  @change="(e) => handleChangepProtection(e, item, index)"
                  :props="cascaderProps_protection"
                  style="width: 100%"
                >
                  <template slot-scope="{ node, data }">
                    <span>{{ data.product || data.name || node.label || '未知' }}</span>
                  </template>
                </el-cascader>
              </el-form-item>
              <el-form-item label="数量">
                <el-input v-model="item.number" />
              </el-form-item>
              <el-form-item v-if="branch === 'wh'" label="首次发放最大允许量">
                <el-input v-model="item.maxNumber" />
              </el-form-item>

              <el-form-item label="领用周期">
                <el-input v-model="item.time">
                  <el-select
                    style="width: 100px"
                    v-model="item.timeUnit"
                    slot="append"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in timeUnit"
                      :label="item.label"
                      :value="item.value"
                      :key="item.value"
                    ></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForm = false">取 消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addProtectionPlan, findHarmFactors, findMillConstruction } from "../api/index.js";
import { getProtectionCategoryTree } from "../api/protectionCategory.js";
import _ from "lodash";
import { mapState } from "vuex";

export default {
  name: 'NewStandardDialog',
  
  computed: {
    ...mapState(['branch'])
  },
  
  data() {
    return {
      dialogForm: false,
      loading: false, // 添加loading状态
      formData: {},
      editableTabsValue: 0,
      millConstructions: [],
      childMillConstructions: [],
      selectMillConstructions: [],
      selectChildMills: [],
      harmFactorInfo: [],
      cascaderProps: {
        label: "name",
        value: "_id",
        checkStrictly: true,
        multiple: false,
      },
      childCascaderProps: {
        label: "name",
        value: "_id",
        checkStrictly: true,
        multiple: true,
      },
      cascaderProps_protection: {
        label: "product",
        value: "product",
        children: "data",
      },
      productList: [],
      timeUnit: [
        { label: "天", value: "d" },
        { label: "周", value: "w" },
        { label: "月", value: "M" },
        { label: "季", value: "Q" },
        { label: "年", value: "y" },
      ],
      categoryOptions: [
        { label: "普通", value: "normal" },
        { label: "特殊", value: "special" },
      ],
      success: () => {},
      grantType: '',
    };
  },
  
  methods: {
    // 将分类树转换为productList格式，支持多级级联选择器（自适应层级），参照ConfigDialog.vue
    transformCategoryTreeToProductList(categoryTree) {
      const productList = [];

      // 递归处理分类树，构建多级级联结构
      const processCategory = (category, parentPath = '') => {
        const currentPath = parentPath ? `${parentPath}/${category.name}` : category.name;

        // 如果有子分类，创建中间节点
        if (category.children && category.children.length > 0) {
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          // 递归处理所有子分类
          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          // 只有当有子数据时才添加到结果中
          if (categoryNode.data.length > 0) {
            return categoryNode;
          }
        } else {
          // 叶子节点，直接返回产品信息
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name
          };
        }
      };

      // 处理子分类的辅助方法
      this.processChildCategory = (category, parentPath) => {
        const currentPath = `${parentPath}/${category.name}`;

        if (category.children && category.children.length > 0) {
          // 有子分类，创建中间节点
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          return categoryNode.data.length > 0 ? categoryNode : null;
        } else {
          // 叶子节点
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name
          };
        }
      };

      // 处理顶级分类
      categoryTree.forEach(category => {
        const result = processCategory(category);
        if (result) {
          productList.push(result);
        }
      });

      return productList;
    },

    // 加载防护用品分类数据，参照ConfigDialog.vue
    async loadProductList() {
      try {
        const categoryRes = await getProtectionCategoryTree({
          includeSystem: true,
          activeOnly: true
        });
        if (categoryRes.status === 200) {
          this.productList = this.transformCategoryTreeToProductList(categoryRes.data || []);
          console.log('NewStandardDialog - 加载的productList数据:', this.productList);
        }
      } catch (error) {
        console.error('加载防护用品分类数据失败:', error);
      }
    },

    // 加载发放区域数据
    async loadMillConstructions() {
      try {
        const millRes = await findMillConstruction();
        if (millRes.status === 200) {
          this.millConstructions = millRes.data || [];
          console.log('NewStandardDialog - 加载的millConstructions数据:', this.millConstructions);
        }
      } catch (error) {
        console.error('加载发放区域数据失败:', error);
      }
    },

    // 加载所有初始数据
    async loadInitialData() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadProductList(),
          this.loadMillConstructions()
        ]);
      } catch (error) {
        console.error('加载初始数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 打开弹窗，参照NewForm.vue的showModal方法
    async showModal(obj) {
      if (obj.success) {
        this.success = obj.success;
      } else {
        this.success = () => {};
      }
      if (obj.grantType) {
        this.grantType = obj.grantType
      }

      // 显示弹窗
      this.dialogForm = true;

      // 加载初始数据（包含loading状态）
      await this.loadInitialData();
      await this.initForm();
      this.editableTabsValue = 0;

      // 处理传入的企业ID筛选（如果有的话）
      if (obj.EnterpriseID && this.millConstructions.length > 0) {
        this.millConstructions = this.millConstructions.filter(e => e.EnterpriseID === obj.EnterpriseID);
      }

      // 处理员工数据
      this.handleEmployees(this.millConstructions);

      this.selectChildMills = []
      this.childMillConstructions = []
    },

    // 兼容open方法
    async open(obj) {
      await this.showModal(obj);
    },
    
    // 初始化表单
    initForm() {
      this.formData = {
        products: [
          {
            product: "",
            productType: [],
            number: 1,
            time: "",
            timeUnit: "M",
            maxNumber: ""
          },
        ],
        category: "",
      };
      this.selectMillConstructions = [];
      this.harmFactorInfo = [];
    },
    
    // 发放区域变化处理，参照NewForm.vue
    handleChange(e) {
      this.$nextTick(() => {
        const checkNodes = this.$refs.myCascader.getCheckedNodes();
        this.$refs.myCascaderChild.handleClear()
        this.$refs.myCascaderChild.panel.activePath = []

        this.childMillConstructions = []

        const selectChildMills = []
        // 设置子区域选项为选中节点的children
        if (checkNodes[0] && checkNodes[0].data && checkNodes[0].data.children) {
          this.childMillConstructions = _.cloneDeep(checkNodes[0].data.children)
        } else {
          this.childMillConstructions = []
        }
        this.hanleSelectAllChildMill(_.cloneDeep(checkNodes[0].data), selectChildMills, [])

        this.selectChildMills = selectChildMills

        const harmFactorInfo = [];
        // 根据组合
        for (let i = 0; i < checkNodes.length; i++) {
          const checkNode = checkNodes[i];
          let harmFactors = [];
          this.findHarmFactors(harmFactors, checkNode);
          harmFactors = Array.from(new Set(harmFactors));
          const data = {
            label: checkNode.label,
            harmFactors,
          };
          harmFactorInfo.push(data);
        }
        this.harmFactorInfo = harmFactorInfo;
      });
    },

    // 处理子区域选择，参照NewForm.vue
    hanleSelectAllChildMill(mill, data, parentIds = []) {
      data.push(parentIds.concat([ mill._id ]))

      if (mill.children) {
        const newParentIds = parentIds.concat([ mill._id ])
        for (let i = 0; i < mill.children.length; i++) {
          const child = mill.children[i];
          this.hanleSelectAllChildMill(child, data, newParentIds)
        }
      }
    },
    
    // 子区域变化处理，参照NewForm.vue
    handleChildChange(e) {
      console.log(23333333, e)
    },

    // 防护用品选择变化处理，参照NewForm.vue
    handleChangepProtection(e, item, index) {
      const checkNodes = this.$refs.protectionCascader[index].getCheckedNodes()[0];

      // 检查是否选择了分类数据（新的分类系统）
      if (checkNodes.data && checkNodes.data.categoryId) {
        // 新的分类系统数据
        item.categoryId = checkNodes.data.categoryId;
        item.categoryPath = checkNodes.data.categoryPath;
        item.categoryName = checkNodes.data.categoryName;
        item.product = checkNodes.data.categoryName;
      } else {
        // 兼容原有的防护用品清单数据
        item.product = checkNodes.label;
      }
    },

    // 查找危害因素，参照NewForm.vue
    findHarmFactors(harmFactors, item) {
      // 如果不存在category 就是人员
      if (!item.data.category) {
        if (item.parent.data.harmFactors) {
          harmFactors.push(...item.parent.data.harmFactors.map((e) => e[1]));
        }
      }

      if (item.data.harmFactors) {
        harmFactors.push(...item.data.harmFactors.map((e) => e[1]));
      }
      if (item.children) {
        for (let i = 0; i < item.children.length; i++) {
          this.findHarmFactors(harmFactors, item.children[i]);
        }
      }
    },
    
    // 新增防护用品
    add() {
      this.formData.products.push({
        product: "",
        productType: [],
        number: 1,
        time: "",
        timeUnit: "M",
        maxNumber: ""
      });
      this.editableTabsValue = this.formData.products.length - 1;
    },
    
    // 删除标签页
    removeTab(targetName) {
      const tabs = this.formData.products;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (index === parseInt(targetName)) {
            const nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = tabs.indexOf(nextTab);
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.formData.products.splice(parseInt(targetName), 1);
    },
    
    // 提交保存
    async submit() {
      // 校验参数
      if (!this.selectMillConstructions || this.selectMillConstructions.length === 0) {
        this.$message({
          message: "请先选择发放区域",
          type: "warning",
        });
        return;
      }

      let validMessage = "";
      this.formData.products.forEach((e, index) => {
        if (
          !e.product ||
          !e.number ||
          !e.time ||
          !e.timeUnit ||
          !e.productType ||
          e.productType.length === 0
        ) {
          validMessage = `请完善第${index + 1}个防护用品的内容`;
        }
      });

      if (validMessage) {
        this.$message({
          message: validMessage,
          type: "warning",
        });
        return;
      }

      try {
        // 参照NewForm.vue的提交逻辑
        const formData = JSON.parse(JSON.stringify(this.formData));
        const formatMill = _.cloneDeep(this.selectMillConstructions).map((item, index) => {
          const target = this.findTargetMill(item, this.millConstructions);
          let type = "";

          if (this.grantType === 'mill') {
            type = target.category;
          } else if (this.grantType === 'depart') {
            if (!target.unitCode) {
              type = 'org';
            } else {
              type = 'depart'
            }
          }

          return {
            id: target._id,
            type,
            name: target.name
          };
        });

        // 新增配发标准的逻辑：给选中的父级区域下的所有岗位配备相同的标准
        const data = {
          formData: this.formData,
          grantType: this.grantType,
          parentMills: formatMill, // 选中的父级区域
          childMills: this.selectChildMills, // 选中的子区域
          // 后端会根据parentMills和childMills，给对应区域下的所有岗位配备相同的防护用品标准
        };

        // 调用API保存数据
        const res = await addProtectionPlan({
          data: data,
          grantType: this.grantType
        });

        if (res.status === 200) {
          this.$message.success('新增标准成功');
        } else {
          throw new Error(res.message || '保存失败');
        }
        this.$emit('save', {
          data: data,
          millConstructions: this.selectMillConstructions,
          childMills: this.selectChildMills,
          products: this.formData.products
        });
        this.dialogForm = false;
      } catch (error) {
        this.$message.error('新增标准失败：' + error.message);
      }
    },

    // 处理员工数据，参照NewForm.vue
    handleEmployees(arr) {
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (item.children && item.children.length > 0) {
          if (item.category === "stations") {
            delete item.children;
          } else {
            this.handleEmployees(item.children);
          }
        } else {
          delete item.children;
        }
        if (item.employees) {
          item.name = item.employees.name;
          item._id = item.employees._id;
          delete item.children;
        }
      }
    },

    // 查找目标mill，参照NewForm.vue
    findTargetMill(id, mills) {
      for (let i = 0; i < mills.length; i++) {
        const mill = mills[i];
        if (mill._id === id) {
          return mill;
        }
        if (mill.children && mill.children.length > 0) {
          const target = this.findTargetMill(id, mill.children);
          if (target) {
            return target;
          }
        }
      }
      return null;
    }
  }
};
</script>

<style scoped>
.harmFactorInfo {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
}

.harmFactorInfo span:first-child {
  font-weight: bold;
  color: #606266;
}

.tabs .addBtn {
  margin-bottom: 10px;
}

.tabs .el-tabs .el-tab-pane {
  padding: 20px 0;
}
</style>
