<template>
  <el-dialog
    title="智能导入防护用品"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" style="margin-bottom: 30px;">
      <el-step title="下载模板" />
      <el-step title="上传文件" />
      <el-step title="预览确认" />
      <el-step title="导入完成" />
    </el-steps>

    <!-- 步骤1: 下载模板 -->
    <div v-if="currentStep === 0" class="step-content">
      <div class="step-title">选择导入仓库并下载模板</div>

      <!-- 仓库选择 -->
      <div class="warehouse-selection">
        <div class="selection-label">选择导入仓库：</div>
        <el-select
          v-model="selectedWarehouseId"
          placeholder="请选择仓库"
          style="width: 200px;"
          @change="handleWarehouseChange"
        >
          <el-option
            v-for="warehouse in warehouseList"
            :key="warehouse._id"
            :label="warehouse.name"
            :value="warehouse._id"
          />
        </el-select>
        <div class="warehouse-tip">
          <i class="el-icon-info"></i>
          产品将导入到选择的仓库中
        </div>
      </div>

      <div class="template-info">
        <p><strong>智能通用模板</strong> - 支持所有分类的防护用品导入</p>
        <p>模板特点：</p>
        <ul>
          <li><strong>分类名称列</strong> - 支持自动分类识别（必填）</li>
          <li><strong>通用字段</strong> - 产品名称、物料编码、型号等基础字段</li>
          <li><strong>专用字段</strong> - 各分类的专用字段（按需填写）</li>
          <li><strong>批量导入</strong> - 可同时导入多个分类的产品</li>
        </ul>
        <div class="template-tips">
          <el-alert
            title="使用提示"
            type="info"
            :closable="false"
            show-icon>
            <div slot="description">
              <p>1. 分类名称支持路径格式，如："头部防护/安全帽"</p>
              <p>2. 只需填写相关字段，其他字段可留空</p>
              <p>3. 系统会自动识别分类并匹配对应字段</p>
            </div>
          </el-alert>
        </div>
      </div>
      <div class="step-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="downloadUniversalTemplate"
          :loading="downloading"
          :disabled="!selectedWarehouseId"
        >
          <i class="el-icon-download"></i> 下载通用模板
        </el-button>
        <el-button
          @click="nextStep"
          :disabled="!selectedWarehouseId"
        >
          跳过下载，直接上传
        </el-button>
      </div>
    </div>

    <!-- 步骤2: 上传文件 -->
    <div v-if="currentStep === 1" class="step-content">
      <div class="step-title">上传填写好的Excel文件</div>
      <div class="upload-area">
        <input
          ref="fileInput"
          type="file"
          accept=".xlsx,.xls"
          style="display: none"
          @change="handleFileSelect"
        />
        
        <div v-if="!selectedFile" class="upload-placeholder" @click="selectFile">
          <i class="el-icon-upload"></i>
          <div>点击选择文件或拖拽文件到此处</div>
          <div class="upload-tip">支持 .xlsx, .xls 格式</div>
        </div>

        <div v-else class="file-info">
          <i class="el-icon-document"></i>
          <span>{{ selectedFile.name }}</span>
          <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
          <el-button size="mini" type="text" @click="removeFile">移除</el-button>
        </div>
      </div>
      
      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" :disabled="!selectedFile" @click="parseFile" :loading="parsing">
          智能解析文件
        </el-button>
      </div>
    </div>

    <!-- 步骤3: 预览确认 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="step-title">预览导入数据</div>
      
      <!-- 解析结果统计 -->
      <div class="parse-summary">
        <el-alert
          :title="`解析完成：共 ${parseResult.statistics.total} 条数据，有效 ${parseResult.statistics.valid} 条，无效 ${parseResult.statistics.invalid} 条`"
          :type="parseResult.statistics.invalid > 0 ? 'warning' : 'success'"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 数据预览表格 -->
      <div class="preview-table">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="有效数据" name="valid" :disabled="parseResult.validData.length === 0">
            <el-table :data="parseResult.validData.slice(0, 10)" border size="small">
              <el-table-column prop="product" label="产品名称" width="150" />
              <el-table-column prop="categoryName" label="分类" width="120" />
              <el-table-column prop="materialCode" label="物料编码" width="120" />
              <el-table-column prop="modelNumber" label="型号" width="120" />
              <el-table-column prop="surplus" label="库存" width="80" />
              <!-- 有效期相关列 -->
              <el-table-column prop="hasExpiry" label="有有效期" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.hasExpiry !== undefined">
                    {{ scope.row.hasExpiry ? '是' : '否' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="有效期" width="120">
                <template slot-scope="scope">
                  <span v-if="scope.row.hasExpiry && scope.row.expiryPeriod">
                    {{ scope.row.expiryPeriod }}{{ getExpiryUnitText(scope.row.expiryUnit) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="needProductionDate" label="需生产日期" width="100">
                <template slot-scope="scope">
                  <span v-if="scope.row.needProductionDate !== undefined">
                    {{ scope.row.needProductionDate ? '是' : '否' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="自定义属性" min-width="200">
                <template slot-scope="scope">
                  <span v-for="attr in scope.row.customAttributes" :key="attr.key" class="custom-attr">
                    {{ attr.label }}: {{ attr.value }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="parseResult.validData.length > 10" class="table-tip">
              仅显示前10条数据，实际将导入 {{ parseResult.validData.length }} 条
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="无效数据" name="invalid" :disabled="parseResult.invalidData.length === 0">
            <el-table :data="parseResult.invalidData" border size="small">
              <el-table-column prop="rowIndex" label="行号" width="80" />
              <el-table-column prop="error" label="错误信息" min-width="200" />
              <el-table-column label="原始数据" min-width="300">
                <template slot-scope="scope">
                  {{ JSON.stringify(scope.row.originalData) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 导入选项 -->
      <div class="import-options">
        <el-checkbox v-model="importOptions.skipDuplicates">跳过重复数据</el-checkbox>
        <el-checkbox v-model="importOptions.updateExisting">更新已存在的产品</el-checkbox>
      </div>

      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button 
          type="primary" 
          :disabled="parseResult.validData.length === 0" 
          @click="startImport"
          :loading="importing"
        >
          开始导入 ({{ parseResult.validData.length }} 条)
        </el-button>
      </div>
    </div>

    <!-- 步骤4: 导入完成 -->
    <div v-if="currentStep === 3" class="step-content">
      <div class="step-title">导入完成</div>
      <div class="import-result">
        <el-result
          :icon="importResult.failed > 0 ? 'warning' : 'success'"
          :title="importResult.failed > 0 ? '导入完成（部分失败）' : '导入成功'"
        >
          <template slot="sub-title">
            <p>成功导入: {{ importResult.success }} 条</p>
            <p v-if="importResult.skipped > 0">跳过重复: {{ importResult.skipped }} 条</p>
            <p v-if="importResult.failed > 0">导入失败: {{ importResult.failed }} 条</p>
          </template>
        </el-result>

        <!-- 错误详情 -->
        <div v-if="importResult.errors && importResult.errors.length > 0" class="error-details">
          <el-collapse>
            <el-collapse-item title="查看错误详情">
              <el-table :data="importResult.errors" border size="small">
                <el-table-column prop="row" label="行号" width="80" />
                <el-table-column prop="error" label="错误信息" min-width="200" />
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="resetDialog">再次导入</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  parseImportFile,
  importProducts,
  getWarehouseList
} from '@/api/index';

export default {
  name: 'SmartImportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    categoryTree: {
      type: Array,
      default: () => []
    },
    defaultWarehouseId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentStep: 0,

      // 模板相关
      downloading: false,

      // 文件上传
      selectedFile: null,
      parsing: false,

      // 解析结果
      parseResult: {
        validData: [],
        invalidData: [],
        statistics: {
          total: 0,
          valid: 0,
          invalid: 0
        }
      },
      activeTab: 'valid',

      // 导入选项
      importOptions: {
        skipDuplicates: true,
        updateExisting: false
      },

      // 导入结果
      importing: false,
      importResult: {
        success: 0,
        failed: 0,
        skipped: 0,
        errors: []
      },

      // 仓库相关
      warehouseList: [],
      publicWarehouseId: null,
      selectedWarehouseId: ''
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },

  watch: {
    // 监听默认仓库ID变化
    defaultWarehouseId(newVal) {
      if (newVal && this.warehouseList.find(w => w._id === newVal)) {
        this.selectedWarehouseId = newVal;
      }
    },

    // 监听对话框显示状态
    visible(newVal) {
      if (newVal) {
        // 对话框打开时重新设置默认仓库
        if (this.defaultWarehouseId && this.warehouseList.find(w => w._id === this.defaultWarehouseId)) {
          this.selectedWarehouseId = this.defaultWarehouseId;
        }
      }
    }
  },

  created() {
    this.loadWarehouseList();
  },
  methods: {
    // 加载仓库列表
    async loadWarehouseList() {
      try {
        const response = await getWarehouseList();
        if (response.status === 200) {
          // 适配新的数据格式：list接口直接返回数组
          this.warehouseList = response.data || [];
          // 找到公共仓库
          const publicWarehouse = this.warehouseList.find(w => w.isPublic);
          if (publicWarehouse) {
            this.publicWarehouseId = publicWarehouse._id;
          } else {
            console.warn('未找到公共仓库');
          }

          // 设置默认选择的仓库
          if (this.defaultWarehouseId && this.warehouseList.find(w => w._id === this.defaultWarehouseId)) {
            this.selectedWarehouseId = this.defaultWarehouseId;
          } else if (publicWarehouse) {
            this.selectedWarehouseId = publicWarehouse._id;
          }
        }
      } catch (error) {
        console.error('加载仓库列表失败:', error);
        this.$message.error('加载仓库列表失败: ' + error.message);
      }
    },

    // 仓库选择变化
    handleWarehouseChange(warehouseId) {
      console.log('选择的仓库ID:', warehouseId);
    },

    // 下一步
    nextStep() {
      this.currentStep++;
    },

    // 上一步
    prevStep() {
      this.currentStep--;
    },

    // 下载通用模板
    async downloadUniversalTemplate() {
      this.downloading = true;
      try {
        // 确保有选择的仓库ID
        if (!this.selectedWarehouseId) {
          this.$message.error('请先选择仓库');
          return;
        }

        // 直接使用浏览器下载
        const url = `/api/defendproducts/downloadTemplate?templateType=universal&warehouseId=${this.selectedWarehouseId}`;
        const link = document.createElement('a');
        link.href = url;
        link.download = '防护用品通用导入模板.xlsx';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success('通用模板下载成功');
      } catch (error) {
        this.$message.error('模板下载失败: ' + error.message);
      } finally {
        this.downloading = false;
      }
    },

    // 选择文件
    selectFile() {
      this.$refs.fileInput.click();
    },

    // 文件选择处理
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.selectedFile = file;
      }
    },

    // 移除文件
    removeFile() {
      this.selectedFile = null;
      this.$refs.fileInput.value = '';
    },

    // 智能解析文件
    async parseFile() {
      this.parsing = true;
      try {
        // 确保有选择的仓库ID
        if (!this.selectedWarehouseId) {
          this.$message.error('请先选择仓库');
          return;
        }

        const response = await parseImportFile(
          this.selectedFile,
          '', // 空字符串而不是null
          this.selectedWarehouseId,
          { intelligentParsing: true } // 启用智能解析模式
        );

        if (response.status === 200) {
          this.parseResult = response.data;
          this.activeTab = this.parseResult.validData.length > 0 ? 'valid' : 'invalid';
          this.nextStep();
        }
      } catch (error) {
        this.$message.error('文件解析失败: ' + error.message);
      } finally {
        this.parsing = false;
      }
    },

    // 开始导入
    async startImport() {
      this.importing = true;
      try {
        // 确保有选择的仓库ID
        if (!this.selectedWarehouseId) {
          this.$message.error('请先选择仓库');
          return;
        }

        const response = await importProducts({
          data: this.parseResult.validData,
          warehouseId: this.selectedWarehouseId,
          options: this.importOptions,
          intelligentImport: true // 启用智能导入模式
        });

        if (response.status === 200) {
          this.importResult = response.data;
          this.nextStep();
          this.$emit('import-success');
        }
      } catch (error) {
        this.$message.error('导入失败: ' + error.message);
      } finally {
        this.importing = false;
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 转换有效期单位显示文本
    getExpiryUnitText(unit) {
      const unitMap = {
        'days': '天',
        'months': '月',
        'years': '年'
      };
      return unitMap[unit] || unit || '';
    },

    // 重置对话框
    resetDialog() {
      this.currentStep = 0;
      this.selectedFile = null;
      this.parseResult = {
        validData: [],
        invalidData: [],
        statistics: { total: 0, valid: 0, invalid: 0 }
      };
      this.importResult = {
        success: 0,
        failed: 0,
        skipped: 0,
        errors: []
      };

      // 重置仓库选择为默认值
      if (this.defaultWarehouseId && this.warehouseList.find(w => w._id === this.defaultWarehouseId)) {
        this.selectedWarehouseId = this.defaultWarehouseId;
      } else if (this.publicWarehouseId) {
        this.selectedWarehouseId = this.publicWarehouseId;
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
      this.resetDialog();
    }
  }
};
</script>

<style scoped>
.step-content {
  min-height: 300px;
}

.step-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

.step-actions {
  margin-top: 30px;
  text-align: right;
}

.warehouse-selection {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
}

.selection-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.warehouse-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
}

.warehouse-tip i {
  margin-right: 4px;
}

.template-info {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.template-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px;
  text-align: center;
  margin-bottom: 20px;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-placeholder {
  cursor: pointer;
}

.upload-placeholder i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.file-size {
  color: #999;
  font-size: 12px;
}

.parse-summary {
  margin-bottom: 20px;
}

.preview-table {
  margin-bottom: 20px;
}

.table-tip {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 10px;
}

.custom-attr {
  display: inline-block;
  margin-right: 10px;
  font-size: 12px;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 2px;
}

.import-options {
  margin-bottom: 20px;
}

.import-result {
  text-align: center;
}

.error-details {
  margin-top: 20px;
}

.template-tips {
  margin-top: 15px;
}

.template-tips .el-alert {
  text-align: left;
}

.template-tips p {
  margin: 5px 0;
}
</style>
