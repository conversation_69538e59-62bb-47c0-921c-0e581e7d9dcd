<template>
  <el-dialog
    :title="isEdit ? '编辑产品' : '新增产品'"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="productForm"
      :model="formData"
      :rules="dynamicFormRules"
      label-width="120px"
      size="small"
    >
      <!-- 基础信息 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>基础信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="product">
              <el-input v-model="formData.product" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="formData.materialCode" placeholder="请输入物料编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="型号" prop="modelNumber">
              <el-input v-model="formData.modelNumber" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品规格" prop="productSpec">
              <el-input v-model="formData.productSpec" placeholder="请输入产品规格" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-cascader
                v-model="selectedCategoryPath"
                :options="categoryTree"
                :props="categoryProps"
                placeholder="请选择分类"
                style="width: 100%;"
                clearable
                @change="handleCategoryChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库" prop="warehouseId">
              <el-select v-model="formData.warehouseId" placeholder="请选择仓库" style="width: 100%;">
                <el-option
                  v-for="warehouse in warehouseList"
                  :key="warehouse._id"
                  :label="warehouse.name"
                  :value="warehouse._id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="库存数量" prop="surplus">
              <el-input-number
                v-model="formData.surplus"
                :min="0"
                placeholder="请输入库存数量"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂家" prop="vender">
              <el-input v-model="formData.vender" placeholder="请输入厂家" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 有效期管理 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>有效期管理</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="过期判断方式">
              <el-radio-group v-model="formData.expiryMode" @change="handleExpiryModeChange">
                <el-radio label="standard">按配发标准周期</el-radio>
                <el-radio label="production">按生产日期+产品有效期</el-radio>
              </el-radio-group>
              <div class="form-tip">
                选择该产品的过期判断方式
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <template v-if="formData.expiryMode === 'production'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="产品有效期长度" prop="expiryPeriod">
                <el-input-number
                  v-model="formData.expiryPeriod"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入有效期长度"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效期单位" prop="expiryUnit">
                <el-select v-model="formData.expiryUnit" style="width: 100%">
                  <el-option label="天" value="days" />
                  <el-option label="月" value="months" />
                  <el-option label="年" value="years" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="form-tip">
                <strong>过期判断逻辑：</strong>生产日期 + 产品有效期长度 = 到期时间<br>
                发放时需要记录产品的生产日期
              </div>
            </el-col>
          </el-row>
        </template>


        <template v-if="formData.expiryMode === 'standard'">
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="form-tip">
                <strong>过期判断逻辑：</strong>领用日期 + 配发标准周期 = 到期时间<br>
                按照配发标准中设置的领用周期来判断过期
              </div>
            </el-col>
          </el-row>
        </template>
      </el-card>

      <!-- 防护属性 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>防护属性</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="防护类型" prop="protectionType">
              <el-input v-model="formData.protectionType" placeholder="请输入防护类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="防护用途" prop="function">
              <el-input v-model="formData.function" placeholder="请输入防护用途" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="危害因素" prop="harmFactors">
          <el-select
            v-model="formData.harmFactors"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入危害因素"
            style="width: 100%;"
          >
            <el-option
              v-for="factor in commonHarmFactors"
              :key="factor"
              :label="factor"
              :value="factor"
            />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 自定义属性 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>自定义属性</span>
          <el-button size="mini" type="text" @click="addCustomAttribute">
            <i class="el-icon-plus"></i> 添加属性
          </el-button>
        </div>
        
        <div v-if="formData.customAttributes.length === 0" class="empty-custom-attrs">
          暂无自定义属性，点击上方"添加属性"按钮添加
        </div>

        <div v-for="(attr, index) in formData.customAttributes" :key="index" class="custom-attr-item">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input v-model="attr.key" placeholder="属性键" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="attr.label" placeholder="显示标签" />
            </el-col>
            <el-col :span="4">
              <el-select v-model="attr.dataType" placeholder="类型">
                <el-option label="文本" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔" value="boolean" />
                <el-option label="数组" value="array" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="attr.value" placeholder="属性值" />
            </el-col>
            <el-col :span="2">
              <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeCustomAttribute(index)" />
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>其他信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="特点" prop="characteristic">
              <el-input
                v-model="formData.characteristic"
                type="textarea"
                :rows="3"
                placeholder="请输入产品特点"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用环境" prop="industryEnvironment">
              <el-input
                v-model="formData.industryEnvironment"
                type="textarea"
                :rows="3"
                placeholder="请输入使用行业或环境"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="formData.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProductEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    productData: {
      type: Object,
      default: null
    },
    categoryTree: {
      type: Array,
      default: () => []
    },
    warehouseList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      saving: false,
      selectedCategoryPath: [],
      
      // 分类属性
      categoryProps: {
        value: '_id',
        label: 'name',
        children: 'children',
        checkStrictly: true
      },

      // 常见危害因素
      commonHarmFactors: [
        '粉尘', '有毒气体', '缺氧', '噪声', '高温', '低温',
        '化学腐蚀', '机械伤害', '电击', '辐射', '生物危害'
      ],

      // 表单数据
      formData: {
        product: '',
        materialCode: '',
        modelNumber: '',
        productSpec: '',
        categoryId: '',
        warehouseId: '',
        surplus: 0,
        vender: '',
        protectionType: '',
        function: '',
        harmFactors: [],
        characteristic: '',
        industryEnvironment: '',
        remark: '',
        isActive: true,
        // 有效期相关字段
        expiryMode: 'standard', // 'standard' | 'production'
        expiryPeriod: null,
        expiryUnit: 'days',
        customAttributes: []
      },

      // 表单验证规则
      formRules: {
        product: [
          { required: true, message: '请输入产品名称', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        warehouseId: [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    isEdit() {
      return this.productData && this.productData._id;
    },

    // 动态表单验证规则
    dynamicFormRules() {
      const rules = { ...this.formRules };

      // 只有在选择"生产日期+产品有效期"模式时才验证有效期长度
      if (this.formData.expiryMode === 'production') {
        rules.expiryPeriod = [
          { required: true, message: '请输入有效期长度', trigger: 'blur' },
          { type: 'number', min: 1, message: '有效期长度必须大于0', trigger: 'blur' }
        ];
      }

      return rules;
    }
  },
  watch: {
    productData: {
      handler(newVal) {
        if (newVal) {
          this.initFormData(newVal);
        } else {
          this.resetFormData();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData(data) {
      // 深拷贝自定义属性，确保数据独立性
      let customAttributes = [];
      try {
        customAttributes = data.customAttributes && Array.isArray(data.customAttributes) ?
          JSON.parse(JSON.stringify(data.customAttributes)) : [];
      } catch (error) {
        console.warn('ProductEditDialog - 自定义属性数据解析失败:', error);
        customAttributes = [];
      }

      this.formData = {
        ...this.formData,
        ...data,
        customAttributes
      };

      // 设置分类路径
      if (data.categoryId) {
        this.selectedCategoryPath = this.buildCategoryPath(data.categoryId);
      }

      // 设置有效期模式
      if (data.hasExpiry) {
        this.formData.expiryMode = 'production';
      } else {
        this.formData.expiryMode = 'standard';
      }
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        product: '',
        materialCode: '',
        modelNumber: '',
        productSpec: '',
        categoryId: '',
        warehouseId: '',
        surplus: 0,
        vender: '',
        protectionType: '',
        function: '',
        harmFactors: [],
        characteristic: '',
        industryEnvironment: '',
        remark: '',
        isActive: true,
        // 有效期相关字段
        expiryMode: 'standard', // 默认按配发标准周期
        expiryPeriod: null,
        expiryUnit: 'days',
        customAttributes: []
      };
      this.selectedCategoryPath = [];
    },

    // 构建分类路径
    buildCategoryPath(categoryId) {
      const path = [];
      const findPath = (tree, targetId, currentPath = []) => {
        for (const node of tree) {
          const newPath = [...currentPath, node._id];
          if (node._id === targetId) {
            path.push(...newPath);
            return true;
          }
          if (node.children && node.children.length > 0) {
            if (findPath(node.children, targetId, newPath)) {
              return true;
            }
          }
        }
        return false;
      };
      findPath(this.categoryTree, categoryId);
      return path;
    },

    // 分类选择变化
    handleCategoryChange(value) {
      if (value && value.length > 0) {
        this.formData.categoryId = value[value.length - 1];
        // 设置分类名称和路径
        const categoryInfo = this.findCategoryInfo(this.categoryTree, this.formData.categoryId);
        if (categoryInfo) {
          this.formData.categoryName = categoryInfo.name;
          this.formData.categoryPath = categoryInfo.path;
        }
      } else {
        this.formData.categoryId = '';
        this.formData.categoryName = '';
        this.formData.categoryPath = '';
      }
    },

    // 查找分类信息
    findCategoryInfo(tree, id) {
      for (const node of tree) {
        if (node._id === id) {
          return { name: node.name, path: node.path };
        }
        if (node.children && node.children.length > 0) {
          const found = this.findCategoryInfo(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    // 添加自定义属性
    addCustomAttribute() {
      this.formData.customAttributes.push({
        key: '',
        label: '',
        value: '',
        dataType: 'string',
        required: false
      });
    },

    // 移除自定义属性
    removeCustomAttribute(index) {
      this.formData.customAttributes.splice(index, 1);
    },

    // 有效期模式切换
    handleExpiryModeChange(value) {
      if (value === 'standard') {
        // 切换到配发标准模式时清空产品有效期字段
        this.formData.expiryPeriod = null;
        this.formData.expiryUnit = 'days';
      } else if (value === 'production') {
        // 切换到生产日期模式时设置默认有效期
        if (!this.formData.expiryPeriod) {
          this.formData.expiryPeriod = 365; // 默认365天
        }
        if (!this.formData.expiryUnit) {
          this.formData.expiryUnit = 'days';
        }
      }
    },

    // 保存
    handleSave() {
      this.$refs.productForm.validate((valid) => {
        if (valid) {
          // 验证有效期配置
          if (this.formData.expiryMode === 'production' && !this.formData.expiryPeriod) {
            this.$message.error('选择按生产日期判断时必须设置产品有效期长度');
            return;
          }

          this.saving = true;

          // 清理空的自定义属性（只要有key或label其中之一就保留）
          this.formData.customAttributes = this.formData.customAttributes.filter(
            attr => (attr.key && attr.key.trim()) || (attr.label && attr.label.trim())
          );

          // 转换数据格式以兼容后端
          const saveData = { ...this.formData };
          saveData.hasExpiry = this.formData.expiryMode === 'production';
          saveData.needProductionDate = this.formData.expiryMode === 'production';

          // 如果是新建产品（_id为空），则删除_id字段
          if (!saveData._id || saveData._id === '') {
            delete saveData._id;
          }

          this.$emit('save', saveData);
        }
      });
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
      this.saving = false;
      this.$refs.productForm.resetFields();
    }
  }
};
</script>

<style scoped>
.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.custom-attr-item {
  margin-bottom: 10px;
}

.empty-custom-attrs {
  text-align: center;
  color: #999;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}
</style>
