(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_train-pages-training-publicCourseDetail"],{"0001":function(t,e,n){"use strict";n.r(e);var a=n("3f2f"),i=n("d596");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("7a29");var s=n("f0c5"),u=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"7f3e3064",null,!1,a["a"],void 0);e["default"]=u.exports},"0f32":function(t,e,n){"use strict";(function(t){n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("14d9");var i=a(n("c7eb")),r=a(n("2909")),s=a(n("1da1")),u=a(n("5530")),o=n("26cb"),l=a(n("8bcb")),c={data:function(){return{adminTrainingId:"",personalTrainingId:"",personalTrainingStatus:0,detail:null,coursesList:[],timedOut:!1,trainingType:2,EnterpriseID:""}},onLoad:function(e){t("log","上个页面传递的参数: ",e," at pages_train/pages/training/publicCourseDetail.vue:52"),this.adminTrainingId=e.adminTrainingId||"",this.personalTrainingId=e.personalTrainingId||"",this.personalTrainingStatus=e.personalTrainingStatus||0},mounted:function(){t("log","用户信息：",this.hasLogin,this.userInfo," at pages_train/pages/training/publicCourseDetail.vue:61"),this.adminTrainingId&&this.hasLogin&&this.getDetail(),this.EnterpriseID=this.userInfo.companyId?this.userInfo.companyId[this.userInfo.companyId.length-1]:""},computed:(0,u.default)({},(0,o.mapGetters)({userInfo:"userInfo",hasLogin:"hasLogin"})),methods:{previewCourse:function(t){this.$store.commit("setCourseID",t),uni.navigateTo({url:"/pages_train/pages/training/courses/courseWithoutPersonal"})},getDetail:function(){var e=this;l.default.getAdminTrainingDetail({_id:this.adminTrainingId}).then((function(n){if(t("log","公开课培训详情：",n.data," at pages_train/pages/training/publicCourseDetail.vue:83"),n.data){if(e.detail=n.data.detail||{},e.trainingType=e.detail.trainingType,e.coursesList=n.data.coursesList||[],!e.detail.completeState){var a=e.detail.completeTime;e.timedOut=!!((new Date).getTime()-new Date(a).getTime()>0)}t("log","培训超时了吗？",e.timedOut," at pages_train/pages/training/publicCourseDetail.vue:93"),uni.stopPullDownRefresh()}}))},createPersonalTraining:function(){var e=this;return(0,s.default)((0,i.default)().mark((function n(){var a,s;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.hasLogin){n.next=3;break}return uni.showToast({title:"请先登录",icon:"none"}),n.abrupt("return");case 3:return a=e.detail.coursesID.map((function(t){return{coursesId:t,courseType:1}})),e.detail.electives&&e.detail.electives.length&&a.push.apply(a,(0,r.default)(e.detail.electives.map((function(t){return{coursesId:t,courseType:2}})))),n.next=7,l.default.createPersonalTraining({adminTrainingId:e.adminTrainingId,trainingType:e.trainingType,courses:a,adminUserId:e.userInfo.adminUserId||""});case 7:s=n.sent,t("log","创建PersonalTraining：",s," at pages_train/pages/training/publicCourseDetail.vue:127"),s.data&&s.data._id?(uni.showToast({title:"操作成功",icon:"success"}),e.personalTrainingStatus=1,e.personalTrainingId=s.data._id):uni.showToast({title:"操作失败",icon:"none"});case 10:case"end":return n.stop()}}),n)})))()},delPersonalTraining:function(){var t=this;l.default.delPersonalTraining({_id:this.personalTrainingId}).then((function(e){200==e.status&&(uni.showToast({title:"操作成功",icon:"success"}),t.personalTrainingStatus=0,t.personalTrainingId="")}))},gotoCourse:function(t){this.timedOut?uni.showModal({content:"本次培训已终止",showCancel:!1,success:function(){}}):this.$store.commit("setCourseID",t)}},onPullDownRefresh:function(){t("log","refresh"," at pages_train/pages/training/publicCourseDetail.vue:168"),this.adminTrainingId&&(this.getDetail(),uni.showToast({title:"数据已刷新",icon:"success"}))}};e.default=c}).call(this,n("0de9")["log"])},"229c":function(t,e,n){var a=n("c93a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("71674226",a,!0,{sourceMap:!1,shadowMode:!1})},2750:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,".grace-empty[data-v-7f5b55b2]{display:flex;flex-direction:column;justify-content:center;align-items:center}",""]),t.exports=e},"32b1":function(t,e,n){"use strict";n.r(e);var a=n("ba39"),i=n("372f");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("653d");var s=n("f0c5"),u=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"7f5b55b2",null,!1,a["a"],void 0);e["default"]=u.exports},"372f":function(t,e,n){"use strict";n.r(e);var a=n("fa9e"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"3f2f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={gracePage:n("c14d").default,graceEmptyNew:n("32b1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[t.detail?n("my-header",{attrs:{slot:"gHeader",title:t.detail.name},slot:"gHeader"}):t._e(),n("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody",id:"gBody"},slot:"gBody"},[n("v-uni-view",{staticClass:"grace-margin-top"},[t.detail?n("v-uni-scroll-view",{staticClass:"grace-list grace-margin-top"},[t._l(t.coursesList,(function(e){return n("v-uni-view",{key:e._id,staticClass:"grace-list-items grace-body grace-border-b",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.previewCourse(e._id)}}},[n("v-uni-view",{staticClass:"grace-list-body"},[n("v-uni-view",{staticClass:"grace-list-title"},[n("v-uni-text",{staticClass:"grace-list-title-text"},[t._v(t._s(e.name))]),n("v-uni-text",{staticClass:"grace-list-title-desc"},[t._v("学时："+t._s(e.classHours)+"h")])],1),n("v-uni-view",{staticClass:"grace-list-title"},[n("v-uni-text",{staticClass:"grace-list-body-desc Introduction"},[t._v(t._s(e.explain||""))])],1)],1)],1)})),0==t.coursesList.length?n("graceEmptyNew",[n("v-uni-view",{staticClass:"empty-view",attrs:{slot:"img"},slot:"img"},[n("v-uni-image",{staticClass:"empty-img",attrs:{mode:"widthFix",src:"https://zyws.cn/static/images/noData.png"}})],1),n("v-uni-text",{staticClass:"grace-text grace-gray",attrs:{slot:"text"},slot:"text"},[t._v("暂无相关课程")])],1):t._e(),t.coursesList.length?n("v-uni-view",[0==t.personalTrainingStatus?n("v-uni-button",{staticClass:"grace-button grace-margin-top",staticStyle:{"line-height":"80rpx"},attrs:{type:"success"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.createPersonalTraining()}}},[t._v("加入我的课程")]):1==t.personalTrainingStatus&&t.personalTrainingId?n("v-uni-button",{staticClass:"grace-button grace-margin-top",staticStyle:{"line-height":"80rpx"},attrs:{type:"warn",plain:!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.delPersonalTraining()}}},[t._v("取消课程")]):t._e()],1):t._e()],2):t._e()],1)],1)],1)},r=[]},"53bd":function(t,e,n){var a=n("2750");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("54804df4",a,!0,{sourceMap:!1,shadowMode:!1})},"653d":function(t,e,n){"use strict";var a=n("53bd"),i=n.n(a);i.a},"7a29":function(t,e,n){"use strict";var a=n("229c"),i=n.n(a);i.a},"8bcb":function(t,e,n){"use strict";(function(t){n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("1067")),r={getHotCourses:function(t){return(0,i.default)({url:"manage/training/courses/getHotCourses",method:"get",param:t})},getClassification:function(t){return(0,i.default)({url:"manage/training/courses/getClassification",method:"get",data:t})},getCourseOne:function(t){return(0,i.default)({url:"manage/training/courses/getCourseOne",method:"get",data:t})},getVideoUrl:function(e){return t("log",e," at api/training.js:34"),(0,i.default)({url:"manage/training/courses/getVideoUrl",method:"get",data:e})},updateCourseProgress:function(t){return(0,i.default)({url:"manage/training/courses/updateCourseProgress",method:"post",data:t})},likeCourse:function(t){return(0,i.default)({url:"manage/training/courses/likeCourse",method:"post",data:t})},searchCourse:function(t){return(0,i.default)({url:"manage/training/courses/searchCourse",method:"get",data:t})},getCourseByClass:function(t){return(0,i.default)({url:"manage/training/courses/getCourseByClass",method:"get",data:t})},creatComment:function(t){return(0,i.default)({url:"manage/training/courses/creatComment",method:"post",data:t})},getComments:function(t){return(0,i.default)({url:"manage/training/courses/getComments",method:"get",data:t})},createCourseReply:function(t){return(0,i.default)({url:"manage/training/courses/createReply",method:"post",data:t})},likeCourseComment:function(t){return(0,i.default)({url:"manage/training/courses/likeComment",method:"post",data:t})},getMycourses:function(t){return(0,i.default)({url:"manage/training/courses/getMyclourses",method:"post",data:t})},createBulletScreenComment:function(t){return(0,i.default)({url:"manage/training/courses/createBulletScreenComment",method:"post",data:t})},getBulletScreenComment:function(t){return(0,i.default)({url:"manage/training/courses/getBulletScreenComment",method:"get",data:t})},adminTrainingList:function(t){return(0,i.default)({url:"manage/adminTraining/list",method:"post",data:t})},personalTrainingList:function(t){return(0,i.default)({url:"manage/adminTraining/personalTrainingList",method:"post",data:t})},createPersonalTraining:function(t){return(0,i.default)({url:"manage/adminTraining/createPersonalTraining",method:"post",data:t})},updatePersonalTraining:function(t){return(0,i.default)({url:"manage/adminTraining/updatePersonalTraining",method:"post",data:t})},delPersonalTraining:function(t){return(0,i.default)({url:"manage/adminTraining/delPersonalTraining",method:"post",data:t})},getPersonalTraining:function(t){return(0,i.default)({url:"manage/adminTraining/getPersonalTraining",method:"post",data:t})},getAdminTrainingDetail:function(t){return(0,i.default)({url:"manage/adminTraining/getDetail",method:"post",data:t})},updateCompleteState:function(t){return(0,i.default)({url:"manage/training/courses/updateCompleteState",method:"get",data:t})},getPauseTime:function(t){return(0,i.default)({url:"manage/training/courses/getPauseTime",method:"get",data:t})},employeesTrainingList:function(t){return(0,i.default)({url:"manage/employeeTraining/list",method:"post",data:t})},getSign:function(t){return(0,i.default)({url:"app/user/getSign",method:"post",data:t})},getFacePicture:function(t){return(0,i.default)({url:"app/user/getFacePicture",method:"post",data:t})}},s=r;e.default=s}).call(this,n("0de9")["log"])},ba39:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"grace-empty"},[this._t("img"),this._t("text"),this._t("other")],2)},i=[]},c93a:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"uni-button[size=mini][data-v-7f3e3064]{line-height:1.8;margin-top:%?9?%}.grace-list-body-desc[data-v-7f3e3064]{margin-top:10px}.grace-body[data-v-7f3e3064]{width:auto}.Introduction[data-v-7f3e3064]{display:inline-block;width:%?500?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}",""]),t.exports=e},d596:function(t,e,n){"use strict";n.r(e);var a=n("0f32"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},fa9e:function(t,e){}}]);