(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_learning-pages-learning-artInfo~pages_train-pages-training-courses-document"],{"0614c":function(t,e,n){"use strict";(function(t){n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"wxParseVideo",props:{node:{}},data:function(){return{playState:!0,videoStyle:"width: 100%;"}},methods:{play:function(){t("log","点击了video 播放"," at components/feng-parse/components/wxParseVideo.vue:36"),this.playState=!this.playState}},mounted:function(){var e=this;uni.$on("slideMenuShow",(function(n){t("log","捕获事件："+n," at components/feng-parse/components/wxParseVideo.vue:44"),"show"==n&&e.playState&&(e.playState=!1)}))}};e.default=a}).call(this,n("0de9")["log"])},"087b":function(t,e,n){"use strict";(function(t){n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7"),n("159b"),n("14d9"),n("ac1f"),n("a434"),n("c975");var r=a(n("ee9d")),s=a(n("d87b")),i={name:"wxParse",props:{userSelect:{type:String,default:"text"},imgOptions:{type:[Object,Boolean],default:function(){return{loop:!1,indicator:"number",longPressActions:!1}}},loading:{type:Boolean,default:!1},className:{type:String,default:""},content:{type:String,default:""},noData:{type:String,default:""},startHandler:{type:Function,default:function(){return function(t){t.attr.class=null,t.attr.style=null}}},endHandler:{type:Function,default:function(){return function(t){t=t}}},charsHandler:{type:Function,default:function(){return function(t){t=t}}},imageProp:{type:Object,default:function(){return{mode:"aspectFit",padding:0,lazyLoad:!1,domain:""}}}},components:{wxParseTemplate:s.default},data:function(){return{nodes:{},imageUrls:[],wxParseWidth:{value:0}}},computed:{},mounted:function(){this.setHtml()},methods:{setHtml:function(){var t=this;this.getWidth().then((function(e){t.wxParseWidth.value=e}));var e=this.content,n=this.noData,a=this.imageProp,s=this.startHandler,i=this.endHandler,o=this.charsHandler,l=e||n,c={start:s,end:i,chars:o},d=(0,r.default)(l,c,a,this);this.imageUrls=d.imageUrls,this.nodes=[],d.nodes.forEach((function(e){setTimeout((function(){e.node&&t.nodes.push(e)}),0)}))},getWidth:function(){var t=this;return new Promise((function(e,n){uni.createSelectorQuery().in(t).select(".wxParse").fields({size:!0,scrollOffset:!0},(function(t){e(t.width)})).exec()}))},navigate:function(e,n,a){t("log",e,a," at components/feng-parse/parse.vue:191"),this.$emit("navigate",e,n)},preview:function(t,e){this.imageUrls.length&&"boolean"!==typeof this.imgOptions&&uni.previewImage({current:t,urls:this.imageUrls,loop:this.imgOptions.loop,indicator:this.imgOptions.indicator,longPressActions:this.imgOptions.longPressActions}),this.$emit("preview",t,e)},removeImageUrl:function(t){var e=this.imageUrls;e.splice(e.indexOf(t),1)}},provide:function(){return{parseWidth:this.wxParseWidth,parseSelect:this.userSelect}},watch:{content:function(t){this.setHtml()}}};e.default=i}).call(this,n("0de9")["log"])},"09e6":function(t,e,n){"use strict";e["a"]=function(t){(t.options.wxs||(t.options.wxs={}))["handler"]=function(t){var e={abbr:!0,b:!0,big:!0,code:!0,del:!0,em:!0,i:!0,ins:!0,label:!0,q:!0,small:!0,span:!0,strong:!0,sub:!0,sup:!0};return t.exports={use:function(t){return!e[t.name]&&-1==(t.attrs.style||"").indexOf("display:inline")&&!t.c}},t.exports}({exports:{}})}},"09f2":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{class:"_"+t.name+" "+t.attrs.class,style:t.attrs.style,attrs:{id:t.attrs.id}},[t._l(t.childs,(function(e,a){return["img"==e.name&&(t.opts[1]&&!t.ctrl[a]||t.ctrl[a]<0)?n("v-uni-image",{key:a+"_0",staticClass:"_img",style:e.attrs.style,attrs:{src:t.ctrl[a]<0?t.opts[2]:t.opts[1],mode:"widthFix"}}):t._e(),"img"==e.name?n("img",{key:a+"_1",class:"_img "+e.attrs.class,style:(-1==t.ctrl[a]?"display:none;":"")+e.attrs.style,attrs:{id:e.attrs.id,src:e.attrs.src||(t.ctrl.load?e.attrs["data-src"]:""),"data-i":a},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imgLoad.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaError.apply(void 0,arguments)},longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.imgLongTap.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.imgTap.apply(void 0,arguments)}}}):"text"==e.type?n("v-uni-text",{attrs:{decode:!0}},[t._v(t._s(e.text))]):"br"==e.name?n("v-uni-text",[t._v("\\n")]):"a"==e.name?n("v-uni-view",{class:(e.attrs.href?"_a ":"")+e.attrs.class,style:"display:inline;"+e.attrs.style,attrs:{id:e.attrs.id,"hover-class":"_hover","data-i":a},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.linkTap.apply(void 0,arguments)}}},[n("node",{staticStyle:{display:"inherit"},attrs:{name:"span",childs:e.children,opts:t.opts}})],1):"video"==e.name?n("v-uni-video",{class:e.attrs.class,style:e.attrs.style,attrs:{id:e.attrs.id,autoplay:e.attrs.autoplay,controls:e.attrs.controls,loop:e.attrs.loop,muted:e.attrs.muted,poster:e.attrs.poster,src:e.src[t.ctrl[a]||0],"data-i":a},on:{play:function(e){arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaError.apply(void 0,arguments)}}}):"iframe"==e.name?n("iframe",{style:e.attrs.style,attrs:{allowfullscreen:e.attrs.allowfullscreen,frameborder:e.attrs.frameborder,src:e.attrs.src}}):"embed"==e.name?n("embed",{style:e.attrs.style,attrs:{src:e.attrs.src}}):"audio"==e.name?n("v-uni-audio",{class:e.attrs.class,style:e.attrs.style,attrs:{id:e.attrs.id,author:e.attrs.author,controls:e.attrs.controls,loop:e.attrs.loop,name:e.attrs.name,poster:e.attrs.poster,src:e.src[t.ctrl[a]||0],"data-i":a},on:{play:function(e){arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaError.apply(void 0,arguments)}}}):"table"==e.name&&e.c||"li"==e.name?n("v-uni-view",{class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style,attrs:{id:e.attrs.id}},["li"==e.name?n("node",{attrs:{childs:e.children,opts:t.opts}}):t._l(e.children,(function(e,a){return n("v-uni-view",{key:a,class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},["td"==e.name||"th"==e.name?n("node",{attrs:{childs:e.children,opts:t.opts}}):t._l(e.children,(function(e,a){return["td"==e.name||"th"==e.name?n("v-uni-view",{key:a+"_0",class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},[n("node",{attrs:{childs:e.children,opts:t.opts}})],1):n("v-uni-view",{class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},t._l(e.children,(function(e,a){return n("v-uni-view",{key:a,class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},[n("node",{attrs:{childs:e.children,opts:t.opts}})],1)})),1)]}))],2)}))],2):t.handler.use(e)?n("v-uni-rich-text",{style:e.f,attrs:{id:e.attrs.id,nodes:[e]}}):2==e.c?n("v-uni-view",{class:"_"+e.name+" "+e.attrs.class,style:e.f+";"+e.attrs.style,attrs:{id:e.attrs.id}},t._l(e.children,(function(e,a){return n("node",{key:a,style:e.f,attrs:{name:e.name,attrs:e.attrs,childs:e.children,opts:t.opts}})})),1):n("node",{style:e.f,attrs:{name:e.name,attrs:e.attrs,childs:e.children,opts:t.opts}})]}))],2)},r=[]},"0c05":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"wxParse",class:t.className,style:"user-select:"+t.userSelect},[t._l(t.nodes,(function(e,a){return t.loading?t._e():[n("wxParseTemplate",{key:a+"_0",attrs:{node:e}})]}))],2)},r=[]},"20cb":function(t,e,n){"use strict";n.r(e);var a=n("6823"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},"246a":function(t,e,n){"use strict";n.r(e);var a=n("09f2"),r=n("d25d");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);n("2ac7");var i=n("f0c5"),o=n("09e6"),l=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,"c6b99c78",null,!1,a["a"],void 0);"function"===typeof o["a"]&&Object(o["a"])(l),e["default"]=l.exports},2563:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 根节点样式 */._root[data-v-4a9db9b8]{overflow:auto;-webkit-overflow-scrolling:touch}\n/* 长按复制 */._select[data-v-4a9db9b8]{-webkit-user-select:text;user-select:text}\n",""]),t.exports=e},"27d9":function(t,e,n){"use strict";n.r(e);var a=n("7734"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},"2ac7":function(t,e,n){"use strict";var a=n("42f4"),r=n.n(a);r.a},3642:function(t,e,n){"use strict";n.r(e);var a=n("780f"),r=n("27d9");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);n("c3a9");var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,"31d3609f",null,!1,a["a"],void 0);e["default"]=o.exports},"38f2":function(t,e,n){"use strict";n.r(e);var a=n("c88e"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},"42f4":function(t,e,n){var a=n("b747");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=n("4f06").default;r("3dbf5a2f",a,!0,{sourceMap:!1,shadowMode:!1})},"49d1":function(t,e,n){"use strict";n.r(e);var a=n("f84a"),r=n("c80a");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);n("833b");var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,"66d46ff4",null,!1,a["a"],void 0);e["default"]=o.exports},"51fd":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,".video-video[data-v-66d46ff4]{background:#000}",""]),t.exports=e},5961:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("ac1f"),n("5319"),n("00b4"),n("99af");var a={strDiscode:function(t){return t=function(t){return t=t.replace(/&forall;|&#8704;|&#x2200;/g,"∀"),t=t.replace(/&part;|&#8706;|&#x2202;/g,"∂"),t=t.replace(/&exist;|&#8707;|&#x2203;/g,"∃"),t=t.replace(/&empty;|&#8709;|&#x2205;/g,"∅"),t=t.replace(/&nabla;|&#8711;|&#x2207;/g,"∇"),t=t.replace(/&isin;|&#8712;|&#x2208;/g,"∈"),t=t.replace(/&notin;|&#8713;|&#x2209;/g,"∉"),t=t.replace(/&ni;|&#8715;|&#x220b;/g,"∋"),t=t.replace(/&prod;|&#8719;|&#x220f;/g,"∏"),t=t.replace(/&sum;|&#8721;|&#x2211;/g,"∑"),t=t.replace(/&minus;|&#8722;|&#x2212;/g,"−"),t=t.replace(/&lowast;|&#8727;|&#x2217;/g,"∗"),t=t.replace(/&radic;|&#8730;|&#x221a;/g,"√"),t=t.replace(/&prop;|&#8733;|&#x221d;/g,"∝"),t=t.replace(/&infin;|&#8734;|&#x221e;/g,"∞"),t=t.replace(/&ang;|&#8736;|&#x2220;/g,"∠"),t=t.replace(/&and;|&#8743;|&#x2227;/g,"∧"),t=t.replace(/&or;|&#8744;|&#x2228;/g,"∨"),t=t.replace(/&cap;|&#8745;|&#x2229;/g,"∩"),t=t.replace(/&cup;|&#8746;|&#x222a;/g,"∪"),t=t.replace(/&int;|&#8747;|&#x222b;/g,"∫"),t=t.replace(/&there4;|&#8756;|&#x2234;/g,"∴"),t=t.replace(/&sim;|&#8764;|&#x223c;/g,"∼"),t=t.replace(/&cong;|&#8773;|&#x2245;/g,"≅"),t=t.replace(/&asymp;|&#8776;|&#x2248;/g,"≈"),t=t.replace(/&ne;|&#8800;|&#x2260;/g,"≠"),t=t.replace(/&le;|&#8804;|&#x2264;/g,"≤"),t=t.replace(/&ge;|&#8805;|&#x2265;/g,"≥"),t=t.replace(/&sub;|&#8834;|&#x2282;/g,"⊂"),t=t.replace(/&sup;|&#8835;|&#x2283;/g,"⊃"),t=t.replace(/&nsub;|&#8836;|&#x2284;/g,"⊄"),t=t.replace(/&sube;|&#8838;|&#x2286;/g,"⊆"),t=t.replace(/&supe;|&#8839;|&#x2287;/g,"⊇"),t=t.replace(/&oplus;|&#8853;|&#x2295;/g,"⊕"),t=t.replace(/&otimes;|&#8855;|&#x2297;/g,"⊗"),t=t.replace(/&perp;|&#8869;|&#x22a5;/g,"⊥"),t=t.replace(/&sdot;|&#8901;|&#x22c5;/g,"⋅"),t}(t),t=function(t){return t=t.replace(/&Alpha;|&#913;|&#x391;/g,"Α"),t=t.replace(/&Beta;|&#914;|&#x392;/g,"Β"),t=t.replace(/&Gamma;|&#915;|&#x393;/g,"Γ"),t=t.replace(/&Delta;|&#916;|&#x394;/g,"Δ"),t=t.replace(/&Epsilon;|&#917;|&#x395;/g,"Ε"),t=t.replace(/&Zeta;|&#918;|&#x396;/g,"Ζ"),t=t.replace(/&Eta;|&#919;|&#x397;/g,"Η"),t=t.replace(/&Theta;|&#920;|&#x398;/g,"Θ"),t=t.replace(/&Iota;|&#921;|&#x399;/g,"Ι"),t=t.replace(/&Kappa;|&#922;|&#x39a;/g,"Κ"),t=t.replace(/&Lambda;|&#923;|&#x39b;/g,"Λ"),t=t.replace(/&Mu;|&#924;|&#x39c;/g,"Μ"),t=t.replace(/&Nu;|&#925;|&#x39d;/g,"Ν"),t=t.replace(/&Xi;|&#925;|&#x39d;/g,"Ν"),t=t.replace(/&Omicron;|&#927;|&#x39f;/g,"Ο"),t=t.replace(/&Pi;|&#928;|&#x3a0;/g,"Π"),t=t.replace(/&Rho;|&#929;|&#x3a1;/g,"Ρ"),t=t.replace(/&Sigma;|&#931;|&#x3a3;/g,"Σ"),t=t.replace(/&Tau;|&#932;|&#x3a4;/g,"Τ"),t=t.replace(/&Upsilon;|&#933;|&#x3a5;/g,"Υ"),t=t.replace(/&Phi;|&#934;|&#x3a6;/g,"Φ"),t=t.replace(/&Chi;|&#935;|&#x3a7;/g,"Χ"),t=t.replace(/&Psi;|&#936;|&#x3a8;/g,"Ψ"),t=t.replace(/&Omega;|&#937;|&#x3a9;/g,"Ω"),t=t.replace(/&alpha;|&#945;|&#x3b1;/g,"α"),t=t.replace(/&beta;|&#946;|&#x3b2;/g,"β"),t=t.replace(/&gamma;|&#947;|&#x3b3;/g,"γ"),t=t.replace(/&delta;|&#948;|&#x3b4;/g,"δ"),t=t.replace(/&epsilon;|&#949;|&#x3b5;/g,"ε"),t=t.replace(/&zeta;|&#950;|&#x3b6;/g,"ζ"),t=t.replace(/&eta;|&#951;|&#x3b7;/g,"η"),t=t.replace(/&theta;|&#952;|&#x3b8;/g,"θ"),t=t.replace(/&iota;|&#953;|&#x3b9;/g,"ι"),t=t.replace(/&kappa;|&#954;|&#x3ba;/g,"κ"),t=t.replace(/&lambda;|&#955;|&#x3bb;/g,"λ"),t=t.replace(/&mu;|&#956;|&#x3bc;/g,"μ"),t=t.replace(/&nu;|&#957;|&#x3bd;/g,"ν"),t=t.replace(/&xi;|&#958;|&#x3be;/g,"ξ"),t=t.replace(/&omicron;|&#959;|&#x3bf;/g,"ο"),t=t.replace(/&pi;|&#960;|&#x3c0;/g,"π"),t=t.replace(/&rho;|&#961;|&#x3c1;/g,"ρ"),t=t.replace(/&sigmaf;|&#962;|&#x3c2;/g,"ς"),t=t.replace(/&sigma;|&#963;|&#x3c3;/g,"σ"),t=t.replace(/&tau;|&#964;|&#x3c4;/g,"τ"),t=t.replace(/&upsilon;|&#965;|&#x3c5;/g,"υ"),t=t.replace(/&phi;|&#966;|&#x3c6;/g,"φ"),t=t.replace(/&chi;|&#967;|&#x3c7;/g,"χ"),t=t.replace(/&psi;|&#968;|&#x3c8;/g,"ψ"),t=t.replace(/&omega;|&#969;|&#x3c9;/g,"ω"),t=t.replace(/&thetasym;|&#977;|&#x3d1;/g,"ϑ"),t=t.replace(/&upsih;|&#978;|&#x3d2;/g,"ϒ"),t=t.replace(/&piv;|&#982;|&#x3d6;/g,"ϖ"),t=t.replace(/&middot;|&#183;|&#xb7;/g,"·"),t}(t),t=function(t){return t=t.replace(/&nbsp;|&#32;|&#x20;/g,"&nbsp;"),t=t.replace(/&ensp;|&#8194;|&#x2002;/g,"&ensp;"),t=t.replace(/&#12288;|&#x3000;/g,"<span class='spaceshow'>　</span>"),t=t.replace(/&emsp;|&#8195;|&#x2003;/g,"&emsp;"),t=t.replace(/&quot;|&#34;|&#x22;/g,'"'),t=t.replace(/&apos;|&#39;|&#x27;/g,"&apos;"),t=t.replace(/&acute;|&#180;|&#xB4;/g,"´"),t=t.replace(/&times;|&#215;|&#xD7;/g,"×"),t=t.replace(/&divide;|&#247;|&#xF7;/g,"÷"),t=t.replace(/&amp;|&#38;|&#x26;/g,"&amp;"),t=t.replace(/&lt;|&#60;|&#x3c;/g,"&lt;"),t=t.replace(/&gt;|&#62;|&#x3e;/g,"&gt;"),t=t.replace(/&nbsp;|&#32;|&#x20;/g,"<span class='spaceshow'> </span>"),t=t.replace(/&ensp;|&#8194;|&#x2002;/g,"<span class='spaceshow'> </span>"),t=t.replace(/&#12288;|&#x3000;/g,"<span class='spaceshow'>　</span>"),t=t.replace(/&emsp;|&#8195;|&#x2003;/g,"<span class='spaceshow'> </span>"),t=t.replace(/&quot;|&#34;|&#x22;/g,'"'),t=t.replace(/&quot;|&#39;|&#x27;/g,"'"),t=t.replace(/&acute;|&#180;|&#xB4;/g,"´"),t=t.replace(/&times;|&#215;|&#xD7;/g,"×"),t=t.replace(/&divide;|&#247;|&#xF7;/g,"÷"),t=t.replace(/&amp;|&#38;|&#x26;/g,"&"),t=t.replace(/&lt;|&#60;|&#x3c;/g,"<"),t=t.replace(/&gt;|&#62;|&#x3e;/g,">"),t}(t),t=function(t){return t=t.replace(/&OElig;|&#338;|&#x152;/g,"Œ"),t=t.replace(/&oelig;|&#339;|&#x153;/g,"œ"),t=t.replace(/&Scaron;|&#352;|&#x160;/g,"Š"),t=t.replace(/&scaron;|&#353;|&#x161;/g,"š"),t=t.replace(/&Yuml;|&#376;|&#x178;/g,"Ÿ"),t=t.replace(/&fnof;|&#402;|&#x192;/g,"ƒ"),t=t.replace(/&circ;|&#710;|&#x2c6;/g,"ˆ"),t=t.replace(/&tilde;|&#732;|&#x2dc;/g,"˜"),t=t.replace(/&thinsp;|$#8201;|&#x2009;/g,"<span class='spaceshow'> </span>"),t=t.replace(/&zwnj;|&#8204;|&#x200C;/g,"<span class='spaceshow'>‌</span>"),t=t.replace(/&zwj;|$#8205;|&#x200D;/g,"<span class='spaceshow'>‍</span>"),t=t.replace(/&lrm;|$#8206;|&#x200E;/g,"<span class='spaceshow'>‎</span>"),t=t.replace(/&rlm;|&#8207;|&#x200F;/g,"<span class='spaceshow'>‏</span>"),t=t.replace(/&ndash;|&#8211;|&#x2013;/g,"–"),t=t.replace(/&mdash;|&#8212;|&#x2014;/g,"—"),t=t.replace(/&lsquo;|&#8216;|&#x2018;/g,"‘"),t=t.replace(/&rsquo;|&#8217;|&#x2019;/g,"’"),t=t.replace(/&sbquo;|&#8218;|&#x201a;/g,"‚"),t=t.replace(/&ldquo;|&#8220;|&#x201c;/g,"“"),t=t.replace(/&rdquo;|&#8221;|&#x201d;/g,"”"),t=t.replace(/&bdquo;|&#8222;|&#x201e;/g,"„"),t=t.replace(/&dagger;|&#8224;|&#x2020;/g,"†"),t=t.replace(/&Dagger;|&#8225;|&#x2021;/g,"‡"),t=t.replace(/&bull;|&#8226;|&#x2022;/g,"•"),t=t.replace(/&hellip;|&#8230;|&#x2026;/g,"…"),t=t.replace(/&permil;|&#8240;|&#x2030;/g,"‰"),t=t.replace(/&prime;|&#8242;|&#x2032;/g,"′"),t=t.replace(/&Prime;|&#8243;|&#x2033;/g,"″"),t=t.replace(/&lsaquo;|&#8249;|&#x2039;/g,"‹"),t=t.replace(/&rsaquo;|&#8250;|&#x203a;/g,"›"),t=t.replace(/&oline;|&#8254;|&#x203e;/g,"‾"),t=t.replace(/&euro;|&#8364;|&#x20ac;/g,"€"),t=t.replace(/&trade;|&#8482;|&#x2122;/g,"™"),t=t.replace(/&larr;|&#8592;|&#x2190;/g,"←"),t=t.replace(/&uarr;|&#8593;|&#x2191;/g,"↑"),t=t.replace(/&rarr;|&#8594;|&#x2192;/g,"→"),t=t.replace(/&darr;|&#8595;|&#x2193;/g,"↓"),t=t.replace(/&harr;|&#8596;|&#x2194;/g,"↔"),t=t.replace(/&crarr;|&#8629;|&#x21b5;/g,"↵"),t=t.replace(/&lceil;|&#8968;|&#x2308;/g,"⌈"),t=t.replace(/&rceil;|&#8969;|&#x2309;/g,"⌉"),t=t.replace(/&lfloor;|&#8970;|&#x230a;/g,"⌊"),t=t.replace(/&rfloor;|&#8971;|&#x230b;/g,"⌋"),t=t.replace(/&loz;|&#9674;|&#x25ca;/g,"◊"),t=t.replace(/&spades;|&#9824;|&#x2660;/g,"♠"),t=t.replace(/&clubs;|&#9827;|&#x2663;/g,"♣"),t=t.replace(/&hearts;|&#9829;|&#x2665;/g,"♥"),t=t.replace(/&diams;|&#9830;|&#x2666;/g,"♦"),t}(t),t},urlToHttpUrl:function(t,e){return/^\/\//.test(t)?"https:".concat(t):/^\//.test(t)?"https://".concat(e).concat(t):t}};e.default=a},"597a":function(t,e,n){var a=n("51fd");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=n("4f06").default;r("4f3458f8",a,!0,{sourceMap:!1,shadowMode:!1})},"5dec":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"/**\n * author: Di (微信小程序开发工程师)\n * organization: WeAppDev(微信小程序开发论坛)(http://weappdev.com)\n *         垂直微信小程序开发交流社区\n *\n * github地址: https://github.com/icindy/wxParse\n *\n * for: 微信小程序富文本解析\n * detail : http://weappdev.com/t/wxparse-alpha0-1-html-markdown/184\n */\n/**\n * 请在全局下引入该文件，@import '/static/wxParse.css';\n */.wxParse[data-v-31d3609f]{-webkit-user-select:none;user-select:none;width:100%;font-family:Helvetica,PingFangSC,Microsoft Yahei,微软雅黑,Arial,sans-serif;color:#333;line-height:1.5;font-size:1em;text-align:justify/* //左右两端对齐 */}.wxParse uni-view[data-v-31d3609f],.wxParse uni-view[data-v-31d3609f]{word-break:break-word}.wxParse .p[data-v-31d3609f]{padding-bottom:.5em;clear:both\n\t/* letter-spacing: 0;//字间距 */}.wxParse .inline[data-v-31d3609f]{display:inline;margin:0;padding:0}.wxParse .div[data-v-31d3609f]{margin:0;padding:0;display:block}.wxParse .h1[data-v-31d3609f]{font-size:2em;line-height:1.2em;margin:.67em 0}.wxParse .h2[data-v-31d3609f]{font-size:1.5em;margin:.83em 0}.wxParse .h3[data-v-31d3609f]{font-size:1.17em;margin:1em 0}.wxParse .h4[data-v-31d3609f]{margin:1.33em 0}.wxParse .h5[data-v-31d3609f]{font-size:.83em;margin:1.67em 0}.wxParse .h6[data-v-31d3609f]{font-size:.83em;margin:1.67em 0}.wxParse .h1[data-v-31d3609f],\n.wxParse .h2[data-v-31d3609f],\n.wxParse .h3[data-v-31d3609f],\n.wxParse .h4[data-v-31d3609f],\n.wxParse .h5[data-v-31d3609f],\n.wxParse .h6[data-v-31d3609f],\n.wxParse .b[data-v-31d3609f],\n.wxParse .strong[data-v-31d3609f]{font-weight:bolder}.wxParse .i[data-v-31d3609f],\n.wxParse .cite[data-v-31d3609f],\n.wxParse .em[data-v-31d3609f],\n.wxParse .var[data-v-31d3609f],\n.wxParse .address[data-v-31d3609f]{font-style:italic}.wxParse .spaceshow[data-v-31d3609f]{white-space:pre}.wxParse .pre[data-v-31d3609f],\n.wxParse .tt[data-v-31d3609f],\n.wxParse .code[data-v-31d3609f],\n.wxParse .kbd[data-v-31d3609f],\n.wxParse .samp[data-v-31d3609f]{font-family:monospace}.wxParse .pre[data-v-31d3609f]{overflow:auto;background:#f5f5f5;padding:%?16?%;white-space:pre;margin:1em %?0?%;font-size:%?24?%}.wxParse .code[data-v-31d3609f]{overflow:auto;padding:%?16?%;white-space:pre;margin:1em %?0?%;background:#f5f5f5;font-size:%?24?%}.wxParse .big[data-v-31d3609f]{font-size:1.17em}.wxParse .small[data-v-31d3609f],\n.wxParse .sub[data-v-31d3609f],\n.wxParse .sup[data-v-31d3609f]{font-size:.83em}.wxParse .sub[data-v-31d3609f]{vertical-align:sub}.wxParse .sup[data-v-31d3609f]{vertical-align:super}.wxParse .s[data-v-31d3609f],\n.wxParse .strike[data-v-31d3609f],\n.wxParse .del[data-v-31d3609f]{text-decoration:line-through}.wxParse .strong[data-v-31d3609f],\n.wxParse .text[data-v-31d3609f],\n.wxParse .span[data-v-31d3609f],\n.wxParse .s[data-v-31d3609f]{display:inline}.wxParse .a[data-v-31d3609f]{color:#00bfff}.wxParse .video[data-v-31d3609f]{text-align:center;margin:%?22?% 0}.wxParse .video-video[data-v-31d3609f]{width:100%}.wxParse .uni-image[data-v-31d3609f]{max-width:100%}.wxParse .img[data-v-31d3609f]{display:block;max-width:100%;margin-bottom:0;/* //与p标签底部padding同时修改 */overflow:hidden}.wxParse .blockquote[data-v-31d3609f]{margin:%?10?% 0;padding:%?22?% 0 %?22?% %?22?%;font-family:Courier,Calibri,宋体;background:#f5f5f5;border-left:%?6?% solid #dbdbdb}.wxParse .blockquote .p[data-v-31d3609f]{margin:0}.wxParse .ul[data-v-31d3609f], .wxParse .ol[data-v-31d3609f]{display:block;margin:1em 0;padding-left:2em}.wxParse .ol[data-v-31d3609f]{list-style-type:disc}.wxParse .ol[data-v-31d3609f]{list-style-type:decimal}.wxParse .ol>weixin-parse-template[data-v-31d3609f],.wxParse .ul>weixin-parse-template[data-v-31d3609f]{display:list-item;align-items:baseline;text-align:match-parent}.wxParse .ol>.li[data-v-31d3609f],.wxParse .ul>.li[data-v-31d3609f]{display:list-item;align-items:baseline;text-align:match-parent}.wxParse .ul .ul[data-v-31d3609f], .wxParse .ol .ul[data-v-31d3609f]{list-style-type:circle}.wxParse .ol .ol .ul[data-v-31d3609f], .wxParse .ol .ul .ul[data-v-31d3609f], .wxParse .ul .ol .ul[data-v-31d3609f], .wxParse .ul .ul .ul[data-v-31d3609f]{list-style-type:square}.wxParse .u[data-v-31d3609f]{text-decoration:underline}.wxParse .hide[data-v-31d3609f]{display:none}.wxParse .del[data-v-31d3609f]{display:inline}.wxParse .figure[data-v-31d3609f]{overflow:hidden}.wxParse .tablebox[data-v-31d3609f]{overflow:auto;background-color:#f5f5f5;background:#f5f5f5;font-size:13px;padding:8px}.wxParse .table .table[data-v-31d3609f],.wxParse .table[data-v-31d3609f]{border-collapse:collapse;box-sizing:border-box;\n\t/* 内边框 */\n\t/* width: 100%; */overflow:auto;white-space:pre}.wxParse .tbody[data-v-31d3609f]{border-collapse:collapse;box-sizing:border-box;\n\t/* 内边框 */border:1px solid #dadada}.wxParse .table  .thead[data-v-31d3609f], .wxParse  .table .tfoot[data-v-31d3609f], .wxParse  .table .th[data-v-31d3609f]{border-collapse:collapse;box-sizing:border-box;background:#ececec;font-weight:40}.wxParse  .table .tr[data-v-31d3609f]{border-collapse:collapse;box-sizing:border-box;\n\t/* border: 2px solid #F0AD4E; */overflow:auto}.wxParse  .table .th[data-v-31d3609f],\n.wxParse  .table .td[data-v-31d3609f]{border-collapse:collapse;box-sizing:border-box;border:%?2?% solid #dadada;overflow:auto}.wxParse .audio[data-v-31d3609f], .wxParse .uni-audio-default[data-v-31d3609f]{display:block}",""]),t.exports=e},"60a8":function(t,e,n){"use strict";n.r(e);var a=n("087b"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},6413:function(t,e,n){"use strict";var a=n("ded3").default;n("c975"),n("e25e"),n("99af"),n("caad"),n("2532"),n("acd8"),n("498a"),n("baa5"),n("ac1f"),n("5319"),n("14d9"),n("d401"),n("d3b7"),n("25f0"),n("a434");var r={trustTags:c("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,ruby,rt,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),blockTags:c("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:c("area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,rp,script,source,style,textarea,title,track,wbr"),voidTags:c("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),entities:{lt:"<",gt:">",quot:'"',apos:"'",ensp:" ",emsp:" ",nbsp:" ",semi:";",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…"},tagStyle:{address:"font-style:italic",big:"display:inline;font-size:1.2em",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}},s=uni.getSystemInfoSync(),i=s.windowWidth,o=c(" ,\r,\n,\t,\f"),l=0;function c(t){for(var e=Object.create(null),n=t.split(","),a=n.length;a--;)e[n[a]]=!0;return e}function d(t,e){var n=t.indexOf("&");while(-1!=n){var a=t.indexOf(";",n+3),s=void 0;if(-1==a)break;"#"==t[n+1]?(s=parseInt(("x"==t[n+2]?"0":"")+t.substring(n+2,a)),isNaN(s)||(t=t.substr(0,n)+String.fromCharCode(s)+t.substr(a+1))):(s=t.substring(n+1,a),(r.entities[s]||"amp"==s&&e)&&(t=t.substr(0,n)+(r.entities[s]||"&")+t.substr(a+1))),n=t.indexOf("&",n+1)}return t}function u(t){this.options=t||{},this.tagStyle=Object.assign(r.tagStyle,this.options.tagStyle),this.imgList=t.imgList||[],this.plugins=t.plugins||[],this.attrs=Object.create(null),this.stack=[],this.nodes=[]}function p(t){this.handler=t}r.ignoreTags.iframe=void 0,r.trustTags.iframe=!0,r.ignoreTags.embed=void 0,r.trustTags.embed=!0,u.prototype.parse=function(t){for(var e=this.plugins.length;e--;)this.plugins[e].onUpdate&&(t=this.plugins[e].onUpdate(t,r)||t);new p(this).parse(t);while(this.stack.length)this.popNode();return this.nodes},u.prototype.expose=function(){for(var t=this.stack.length;t--;){var e=this.stack[t];if("a"==e.name||e.c)return;e.c=1}},u.prototype.hook=function(t){for(var e=this.plugins.length;e--;)if(this.plugins[e].onParse&&0==this.plugins[e].onParse(t,this))return!1;return!0},u.prototype.getUrl=function(t){var e=this.options.domain;return"/"==t[0]?"/"==t[1]?t="".concat(e?e.split("://")[0]:"http",":").concat(t):e&&(t=e+t):!e||t.includes("data:")||t.includes("://")||(t="".concat(e,"/").concat(t)),t},u.prototype.parseStyle=function(t){var e=t.attrs,n=(this.tagStyle[t.name]||"").split(";").concat((e.style||"").split(";")),a={},r="";e.id&&(this.options.useAnchor?this.expose():"img"!=t.name&&"a"!=t.name&&"video"!=t.name&&"audio"!=t.name&&(e.id=void 0)),e.width&&(a.width=parseFloat(e.width)+(e.width.includes("%")?"%":"px"),e.width=void 0),e.height&&(a.height=parseFloat(e.height)+(e.height.includes("%")?"%":"px"),e.height=void 0);for(var s=0,l=n.length;s<l;s++){var c=n[s].split(":");if(!(c.length<2)){var d=c.shift().trim().toLowerCase(),u=c.join(":").trim();if("-"==u[0]&&u.lastIndexOf("-")>0||u.includes("safe"))r+=";".concat(d,":").concat(u);else if(!a[d]||u.includes("import")||!a[d].includes("import")){if(u.includes("url")){var p=u.indexOf("(")+1;if(p){while('"'==u[p]||"'"==u[p]||o[u[p]])p++;u=u.substr(0,p)+this.getUrl(u.substr(p))}}else u.includes("rpx")&&(u=u.replace(/[0-9.]+\s*rpx/g,(function(t){return"".concat(parseFloat(t)*i/750,"px")})));a[d]=u}}}return t.attrs.style=r,a},u.prototype.onTagName=function(t){this.tagName=this.xml?t:t.toLowerCase(),"svg"==this.tagName&&(this.xml=!0)},u.prototype.onAttrName=function(t){t=this.xml?t:t.toLowerCase(),"data-"==t.substr(0,5)?"data-src"!=t||this.attrs.src?"img"==this.tagName||"a"==this.tagName?this.attrName=t:this.attrName=void 0:this.attrName="src":(this.attrName=t,this.attrs[t]="T")},u.prototype.onAttrVal=function(t){var e=this.attrName||"";"style"==e||"href"==e?this.attrs[e]=d(t,!0):e.includes("src")?this.attrs[e]=this.getUrl(d(t,!0)):e&&(this.attrs[e]=t)},u.prototype.onOpenTag=function(t){var e=Object.create(null);e.name=this.tagName,e.attrs=this.attrs,this.attrs=Object.create(null);var n=e.attrs,a=this.stack[this.stack.length-1],s=a?a.children:this.nodes,o=this.xml?t:r.voidTags[e.name];if("embed"==e.name&&this.expose(),"video"!=e.name&&"audio"!=e.name||("video"!=e.name||n.id||(n.id="v".concat(l++)),n.controls||n.autoplay||(n.controls="T"),e.src=[],n.src&&(e.src.push(n.src),n.src=void 0),this.expose()),o){if(!this.hook(e)||r.ignoreTags[e.name])return void("base"!=e.name||this.options.domain?"source"==e.name&&a&&("video"==a.name||"audio"==a.name)&&n.src&&a.src.push(n.src):this.options.domain=n.href);var c=this.parseStyle(e);if("img"==e.name){if(n.src&&(n.src.includes("webp")&&(e.webp="T"),n.src.includes("data:")&&!n["original-src"]&&(n.ignore="T"),!n.ignore||e.webp||n.src.includes("cloud://"))){for(var d=this.stack.length;d--;){var u=this.stack[d];if("a"==u.name){e.a=u.attrs;break}u.c=1}n.i=this.imgList.length.toString();var p=n["original-src"]||n.src;this.imgList.push(p),this.options.lazyLoad&&(n["data-src"]=n.src,n.src=void 0)}"inline"==c.display&&(c.display=""),n.ignore&&(c["max-width"]=c["max-width"]||"100%",n.style+=";-webkit-touch-callout:none"),parseInt(c.width)>i&&(c.height=void 0),c.width&&(c.width.includes("auto")?c.width="":(e.w="T",c.height&&!c.height.includes("auto")&&(e.h="T")))}else if("svg"==e.name)return s.push(e),this.stack.push(e),void this.popNode();for(var f in c)c[f]&&(n.style+=";".concat(f,":").concat(c[f].replace(" !important","")));n.style=n.style.substr(1)||void 0}else("pre"==e.name||(n.style||"").includes("white-space")&&n.style.includes("pre"))&&(this.pre=e.pre=!0),e.children=[],this.stack.push(e);s.push(e)},u.prototype.onCloseTag=function(t){var e;for(t=this.xml?t:t.toLowerCase(),e=this.stack.length;e--;)if(this.stack[e].name==t)break;if(-1!=e)while(this.stack.length>e)this.popNode();else if("p"==t||"br"==t){var n=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;n.push({name:t,attrs:{}})}},u.prototype.popNode=function(){var t=this.stack.pop(),e=t.attrs,n=t.children,s=this.stack[this.stack.length-1],o=s?s.children:this.nodes;if(!this.hook(t)||r.ignoreTags[t.name])return"title"==t.name&&n.length&&"text"==n[0].type&&this.options.setTitle&&uni.setNavigationBarTitle({title:n[0].text}),void o.pop();if(t.pre){t.pre=this.pre=void 0;for(var l=this.stack.length;l--;)this.stack[l].pre&&(this.pre=!0)}var c={};if("svg"==t.name){var d="",u=e,p=u.style;return e.style="",e.xmlns="http://www.w3.org/2000/svg",function t(e){for(var n in d+="<".concat(e.name),e.attrs){var a=e.attrs[n];a&&("viewbox"==n&&(n="viewBox"),d+=" ".concat(n,'="').concat(a,'"'))}if(e.children){d+=">";for(var r=0;r<e.children.length;r++)t(e.children[r]);d+="</".concat(e.name,">")}else d+="/>"}(t),t.name="img",t.attrs={src:"data:image/svg+xml;utf8,".concat(d.replace(/#/g,"%23")),style:p,ignore:"T"},t.children=void 0,void(this.xml=!1)}if(e.align&&("table"==t.name?"center"==e.align?c["margin-inline-start"]=c["margin-inline-end"]="auto":c.float=e.align:c["text-align"]=e.align,e.align=void 0),"font"==t.name&&(e.color&&(c.color=e.color,e.color=void 0),e.face&&(c["font-family"]=e.face,e.face=void 0),e.size)){var f=parseInt(e.size);isNaN(f)||(f<1?f=1:f>7&&(f=7),c["font-size"]=["xx-small","x-small","small","medium","large","x-large","xx-large"][f-1]),e.size=void 0}if((e.class||"").includes("align-center")&&(c["text-align"]="center"),Object.assign(c,this.parseStyle(t)),parseInt(c.width)>i&&(c["max-width"]="100%",c["box-sizing"]="border-box"),r.blockTags[t.name]?t.name="div":r.trustTags[t.name]||this.xml||(t.name="span"),"a"==t.name||"ad"==t.name||"iframe"==t.name)this.expose();else if("ul"!=t.name&&"ol"!=t.name||!t.c){if("table"==t.name){var h=parseFloat(e.cellpadding),g=parseFloat(e.cellspacing),v=parseFloat(e.border);if(t.c&&(isNaN(h)&&(h=2),isNaN(g)&&(g=2)),v&&(e.style+=";border:".concat(v,"px solid gray")),t.flag&&t.c){c.display="grid",g?(c["grid-gap"]="".concat(g,"px"),c.padding="".concat(g,"px")):v&&(e.style+=";border-left:0;border-top:0");var m=[],x=[],b=[],y={};(function t(e){for(var n=0;n<e.length;n++)"tr"==e[n].name?x.push(e[n]):t(e[n].children||[])})(n);for(var w=1;w<=x.length;w++){for(var _=1,P=0;P<x[w-1].children.length;P++,_++){var k=x[w-1].children[P];if("td"==k.name||"th"==k.name){while(y["".concat(w,".").concat(_)])_++;var S=k.attrs.style||"",O=S.indexOf("width")?S.indexOf(";width"):0;if(-1!=O){var T=S.indexOf(";",O+6);-1==T&&(T=S.length),k.attrs.colspan||(m[_]=S.substring(O?O+7:6,T)),S=S.substr(0,O)+S.substr(T)}if(S+=(v?";border:".concat(v,"px solid gray")+(g?"":";border-right:0;border-bottom:0"):"")+(h?";padding:".concat(h,"px"):""),k.attrs.colspan&&(S+=";grid-column-start:".concat(_,";grid-column-end:").concat(_+parseInt(k.attrs.colspan)),k.attrs.rowspan||(S+=";grid-row-start:".concat(w,";grid-row-end:").concat(w+1)),_+=parseInt(k.attrs.colspan)-1),k.attrs.rowspan){S+=";grid-row-start:".concat(w,";grid-row-end:").concat(w+parseInt(k.attrs.rowspan)),k.attrs.colspan||(S+=";grid-column-start:".concat(_,";grid-column-end:").concat(_+1));for(var j=1;j<k.attrs.rowspan;j++)y["".concat(w+j,".").concat(_)]=1}S&&(k.attrs.style=S),b.push(k)}}if(1==w){for(var $="",z=1;z<_;z++)$+="".concat(m[z]?m[z]:"auto"," ");c["grid-template-columns"]=$}}t.children=b}else t.c&&(c.display="table"),isNaN(g)||(c["border-spacing"]="".concat(g,"px")),(v||h)&&function t(e){for(var n=0;n<e.length;n++){var a=e[n];"th"==a.name||"td"==a.name?(v&&(a.attrs.style="border:".concat(v,"px solid gray;").concat(a.attrs.style||"")),h&&(a.attrs.style="padding:".concat(h,"px;").concat(a.attrs.style||""))):a.children&&t(a.children)}}(n);if(this.options.scrollTable&&!(e.style||"").includes("inline")){var I=a({},t);t.name="div",t.attrs={style:"overflow:auto"},t.children=[I],e=I.attrs}}else if("td"!=t.name&&"th"!=t.name||!e.colspan&&!e.rowspan){if("ruby"==t.name){t.name="span";for(var N=0;N<n.length-1;N++)"text"==n[N].type&&"rt"==n[N+1].name&&(n[N]={name:"div",attrs:{style:"display:inline-block"},children:[{name:"div",attrs:{style:"font-size:50%;text-align:start"},children:n[N+1].children},n[N]]},n.splice(N+1,1))}else if(t.c){t.c=2;for(var A=t.children.length;A--;)t.children[A].c&&"table"!=t.children[A].name||(t.c=1)}}else for(var E=this.stack.length;E--;)if("table"==this.stack[E].name){this.stack[E].flag=1;break}}else{var C={a:"lower-alpha",A:"upper-alpha",i:"lower-roman",I:"upper-roman"};C[e.type]&&(e.style+=";list-style-type:".concat(C[e.type]),e.type=void 0);for(var M=n.length;M--;)"li"==n[M].name&&(n[M].c=1)}if((c.display||"").includes("flex")&&!t.c)for(var L=n.length;L--;){var q=n[L];q.f&&(q.attrs.style=(q.attrs.style||"")+q.f,q.f=void 0)}var D=s&&(s.attrs.style||"").includes("flex")&&!t.c;for(var F in D&&(t.f=";max-width:100%"),c)if(c[F]){var U=";".concat(F,":").concat(c[F].replace(" !important",""));D&&(F.includes("flex")&&"flex-direction"!=F||"align-self"==F||"-"==c[F][0]||"width"==F&&U.includes("%"))?(t.f+=U,"width"==F&&(e.style+=";width:100%")):e.style+=U}e.style=e.style.substr(1)||void 0},u.prototype.onText=function(t){if(!this.pre){for(var e,n="",a=0,r=t.length;a<r;a++)o[t[a]]?(" "!=n[n.length-1]&&(n+=" "),"\n"!=t[a]||e||(e=!0)):n+=t[a];if(" "==n&&e)return;t=n}var s=Object.create(null);if(s.type="text",s.text=d(t),this.hook(s)){var i=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;i.push(s)}},p.prototype.parse=function(t){this.content=t||"",this.i=0,this.start=0,this.state=this.text;for(var e=this.content.length;-1!=this.i&&this.i<e;)this.state()},p.prototype.checkClose=function(t){var e="/"==this.content[this.i];return!!(">"==this.content[this.i]||e&&">"==this.content[this.i+1])&&(t&&this.handler[t](this.content.substring(this.start,this.i)),this.i+=e?2:1,this.start=this.i,this.handler.onOpenTag(e),"script"==this.handler.tagName?(this.i=this.content.indexOf("</",this.i),-1!=this.i&&(this.i+=2,this.start=this.i),this.state=this.endTag):this.state=this.text,!0)},p.prototype.text=function(){if(this.i=this.content.indexOf("<",this.i),-1!=this.i){var t=this.content[this.i+1];if(t>="a"&&t<="z"||t>="A"&&t<="Z")this.start!=this.i&&this.handler.onText(this.content.substring(this.start,this.i)),this.start=++this.i,this.state=this.tagName;else if("/"==t||"!"==t||"?"==t){this.start!=this.i&&this.handler.onText(this.content.substring(this.start,this.i));var e=this.content[this.i+2];if("/"==t&&(e>="a"&&e<="z"||e>="A"&&e<="Z"))return this.i+=2,this.start=this.i,this.state=this.endTag;var n="--\x3e";"!"==t&&"-"==this.content[this.i+2]&&"-"==this.content[this.i+3]||(n=">"),this.i=this.content.indexOf(n,this.i),-1!=this.i&&(this.i+=n.length,this.start=this.i)}else this.i++}else this.start<this.content.length&&this.handler.onText(this.content.substring(this.start,this.content.length))},p.prototype.tagName=function(){if(o[this.content[this.i]]){this.handler.onTagName(this.content.substring(this.start,this.i));while(o[this.content[++this.i]]);this.i<this.content.length&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)}else this.checkClose("onTagName")||this.i++},p.prototype.attrName=function(){var t=this.content[this.i];if(o[t]||"="==t){this.handler.onAttrName(this.content.substring(this.start,this.i));var e="="==t,n=this.content.length;while(++this.i<n)if(t=this.content[this.i],!o[t]){if(this.checkClose())return;if(e)return this.start=this.i,this.state=this.attrVal;if("="!=this.content[this.i])return this.start=this.i,this.state=this.attrName;e=!0}}else this.checkClose("onAttrName")||this.i++},p.prototype.attrVal=function(){var t=this.content[this.i],e=this.content.length;if('"'==t||"'"==t){if(this.start=++this.i,this.i=this.content.indexOf(t,this.i),-1==this.i)return;this.handler.onAttrVal(this.content.substring(this.start,this.i))}else for(;this.i<e;this.i++){if(o[this.content[this.i]]){this.handler.onAttrVal(this.content.substring(this.start,this.i));break}if(this.checkClose("onAttrVal"))return}while(o[this.content[++this.i]]);this.i<e&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)},p.prototype.endTag=function(){var t=this.content[this.i];if(o[t]||">"==t||"/"==t){if(this.handler.onCloseTag(this.content.substring(this.start,this.i)),">"!=t&&(this.i=this.content.indexOf(">",this.i),-1==this.i))return;this.start=++this.i,this.state=this.text}else this.i++},t.exports=u},"646d":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{class:(t.selectable?"_select ":"")+"_root",attrs:{id:"_root"}},[t.nodes[0]?n("node",{attrs:{childs:t.nodes,opts:[t.lazyLoad,t.loadingImg,t.errorImg,t.showImgMenu]}}):t._t("default")],2)},r=[]},6781:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return"element"==t.node.node?["button"==t.node.tag?n("v-uni-button",{class:t.node.classStr,style:t.node.styleStr,attrs:{type:"default",size:"mini"}},[n("wx-parse-template",{attrs:{node:t.node}})],1):"a"==t.node.tag?n("v-uni-view",{class:t.node.classStr,staticStyle:{display:"inline","border-bottom":"1px solid #555555"},style:t.node.styleStr,attrs:{"data-href":t.node.attr.href},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.wxParseATap(t.node.attr,e)}}},[t._l(t.node.nodes,(function(t,e){return[n("wx-parse-template",{key:e+"_0",attrs:{node:t}})]}))],2):"li"==t.node.tag?n("v-uni-view",{class:t.node.classStr,style:t.node.styleStr},[t._l(t.node.nodes,(function(t,e){return[n("wx-parse-template",{key:e+"_0",attrs:{node:t}})]}))],2):"table"==t.node.tag?n("wx-parse-table",{class:t.node.classStr,style:t.node.styleStr,attrs:{node:t.node}}):"br"==t.node.tag?n("br"):"video"==t.node.tag?n("wx-parse-video",{attrs:{node:t.node}}):"audio"==t.node.tag?n("wx-parse-audio",{attrs:{node:t.node}}):"img"==t.node.tag?n("wx-parse-img",{style:t.node.styleStr,attrs:{node:t.node}}):"strong"==t.node.tag?n("v-uni-view",{class:t.node.classStr,staticStyle:{"font-weight":"700",display:"inline"},style:t.node.styleStr},[t._l(t.node.nodes,(function(t,e){return[n("wx-parse-template",{key:e+"_0",attrs:{node:t}})]}))],2):"span"==t.node.tag?n("v-uni-view",{class:t.node.classStr,staticStyle:{display:"inline"},style:t.node.styleStr},[t._l(t.node.nodes,(function(t,e){return[n("wx-parse-template",{key:e+"_0",attrs:{node:t}})]}))],2):"em"==t.node.tag?n("v-uni-view",{class:t.node.classStr,staticStyle:{display:"inline","font-style":"italic"},style:t.node.styleStr},[t._l(t.node.nodes,(function(t,e){return[n("wx-parse-template",{key:e+"_0",attrs:{node:t}})]}))],2):n("v-uni-view",{class:t.node.classStr,style:t.node.styleStr},[t._l(t.node.nodes,(function(t,e){return[n("wx-parse-template",{key:e+"_0",attrs:{node:t}})]}))],2)]:"text"==t.node.node?[t._v(t._s(t.node.text))]:t._e()},r=[]},6823:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"wxParseAudio",props:{node:{type:Object,default:function(){return{}}}}};e.default=a},"6f5d":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{content:String,copyLink:{type:Boolean,default:uni.$u.props.parse.copyLink},domain:String,errorImg:{type:String,default:uni.$u.props.parse.errorImg},lazyLoad:{type:Boolean,default:uni.$u.props.parse.lazyLoad},loadingImg:{type:String,default:uni.$u.props.parse.loadingImg},pauseVideo:{type:Boolean,default:uni.$u.props.parse.pauseVideo},previewImg:{type:Boolean,default:uni.$u.props.parse.previewImg},scrollTable:Boolean,selectable:Boolean,setTitle:{type:Boolean,default:uni.$u.props.parse.setTitle},showImgMenu:{type:Boolean,default:uni.$u.props.parse.showImgMenu},tagStyle:Object,useAnchor:null}};e.default=a},7734:function(t,e,n){"use strict";(function(t){n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("14d9");var r=a(n("b85c")),s={name:"wxParseTable",props:{node:{type:Object,default:function(){return{}}}},inject:["parseSelect"],data:function(){return{nodes:[]}},mounted:function(){this.nodes=this.loadNode([this.node])},methods:{loadNode:function(e){t("log",e," at components/feng-parse/components/wxParseTable.vue:28");var n,a=[],s=(0,r.default)(e);try{for(s.s();!(n=s.n()).done;){var i=n.value;if("element"==i.node){var o={name:i.tag,attrs:{class:i.classStr,style:i.styleStr},children:i.nodes?this.loadNode(i.nodes):[]};a.push(o)}else"text"==i.node&&a.push({type:"text",text:i.text})}}catch(l){s.e(l)}finally{s.f()}return a}}};e.default=s}).call(this,n("0de9")["log"])},"780f":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tablebox"},[e("v-uni-rich-text",{class:this.node.classStr,style:"user-select:"+this.parseSelect,attrs:{nodes:this.nodes}})],1)},r=[]},"7b02":function(t,e,n){"use strict";n.r(e);var a=n("646d"),r=n("38f2");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);n("ce43");var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,"4a9db9b8",null,!1,a["a"],void 0);e["default"]=o.exports},"7d1c":function(t,e,n){var a=n("2563");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=n("4f06").default;r("7db4d5b2",a,!0,{sourceMap:!1,shadowMode:!1})},"7db9":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-audio",{class:t.node.classStr,style:t.node.styleStr,attrs:{id:t.node.attr.id,src:t.node.attr.src,loop:t.node.attr.loop,poster:t.node.attr.poster,name:t.node.attr.name,author:t.node.attr.author,controls:!0}})},r=[]},8147:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var a={name:"wxParseImg",data:function(){return{newStyleStr:"",preview:!0}},inject:["parseWidth"],mounted:function(){},props:{node:{type:Object,default:function(){return{}}}},methods:{wxParseImgTap:function(t){if(this.preview){var e=t.currentTarget.dataset.src;if(e){var n=this.$parent;while(!n.preview||"function"!==typeof n.preview)n=n.$parent;n.preview(e,t)}}},wxParseImgLoad:function(t){var e=t.currentTarget.dataset.src;if(e){var n=t.mp.detail,a=n.width,r=n.height,s=this.wxAutoImageCal(a,r),i=s.imageheight,o=s.imageWidth,l=this.node.attr,c=l.padding,d=l.mode,u=this.node.styleStr,p="widthFix"===d?"":"height: ".concat(i,"px;");u||(this.newStyleStr="".concat(u,"; ").concat(p,"; width: ").concat(o,"px; padding: 0 ").concat(+c,"px;"))}},wxAutoImageCal:function(t,e){var n=this.parseWidth.value,a={};if(t<60||e<60){var r=this.node.attr.src,s=this.$parent;while(!s.preview||"function"!==typeof s.preview)s=s.$parent;s.removeImageUrl(r),this.preview=!1}return t>n?(a.imageWidth=n,a.imageheight=n*(e/t)):(a.imageWidth=t,a.imageheight=e),a}}};e.default=a},"833b":function(t,e,n){"use strict";var a=n("597a"),r=n.n(a);r.a},"86b7":function(t,e,n){"use strict";n.r(e);var a=n("0c05"),r=n("60a8");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=o.exports},9523:function(t,e,n){n("7a82");var a=n("a395");t.exports=function(t,e,n){return e=a(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"9d94":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-image",{class:t.node.classStr,style:t.newStyleStr||t.node.styleStr,attrs:{mode:"widthFix","lazy-load":t.node.attr.lazyLoad,"data-src":t.node.attr.src,src:t.node.attr.src},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.wxParseImgLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.wxParseImgTap.apply(void 0,arguments)}}})},r=[]},a395:function(t,e,n){var a=n("7037")["default"],r=n("e50d");t.exports=function(t){var e=r(t,"string");return"symbol"===a(e)?e:String(e)},t.exports.__esModule=!0,t.exports["default"]=t.exports},ae35:function(t,e,n){"use strict";n.r(e);var a=n("e048"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},b747:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* a 标签默认效果 */._a[data-v-c6b99c78]{padding:1.5px 0 1.5px 0;color:#366092;word-break:break-all}\n/* a 标签点击态效果 */._hover[data-v-c6b99c78]{text-decoration:underline;opacity:.7}\n/* 图片默认效果 */._img[data-v-c6b99c78]{max-width:100%;-webkit-touch-callout:none}\n/* 内部样式 */._b[data-v-c6b99c78],\n._strong[data-v-c6b99c78]{font-weight:700}._code[data-v-c6b99c78]{font-family:monospace}._del[data-v-c6b99c78]{text-decoration:line-through}._em[data-v-c6b99c78],\n._i[data-v-c6b99c78]{font-style:italic}._h1[data-v-c6b99c78]{font-size:2em}._h2[data-v-c6b99c78]{font-size:1.5em}._h3[data-v-c6b99c78]{font-size:1.17em}._h5[data-v-c6b99c78]{font-size:.83em}._h6[data-v-c6b99c78]{font-size:.67em}._h1[data-v-c6b99c78],\n._h2[data-v-c6b99c78],\n._h3[data-v-c6b99c78],\n._h4[data-v-c6b99c78],\n._h5[data-v-c6b99c78],\n._h6[data-v-c6b99c78]{display:block;font-weight:700}._image[data-v-c6b99c78]{height:1px}._ins[data-v-c6b99c78]{text-decoration:underline}._li[data-v-c6b99c78]{display:list-item}._ol[data-v-c6b99c78]{list-style-type:decimal}._ol[data-v-c6b99c78],\n._ul[data-v-c6b99c78]{display:block;padding-left:40px;margin:1em 0}._q[data-v-c6b99c78]::before{content:'\"'}._q[data-v-c6b99c78]::after{content:'\"'}._sub[data-v-c6b99c78]{font-size:smaller;vertical-align:sub}._sup[data-v-c6b99c78]{font-size:smaller;vertical-align:super}._thead[data-v-c6b99c78],\n._tbody[data-v-c6b99c78],\n._tfoot[data-v-c6b99c78]{display:table-row-group}._tr[data-v-c6b99c78]{display:table-row}._td[data-v-c6b99c78],\n._th[data-v-c6b99c78]{display:table-cell;vertical-align:middle}._th[data-v-c6b99c78]{font-weight:700;text-align:center}._ul[data-v-c6b99c78]{list-style-type:disc}._ul ._ul[data-v-c6b99c78]{margin:0;list-style-type:circle}._ul ._ul ._ul[data-v-c6b99c78]{list-style-type:square}._abbr[data-v-c6b99c78],\n._b[data-v-c6b99c78],\n._code[data-v-c6b99c78],\n._del[data-v-c6b99c78],\n._em[data-v-c6b99c78],\n._i[data-v-c6b99c78],\n._ins[data-v-c6b99c78],\n._label[data-v-c6b99c78],\n._q[data-v-c6b99c78],\n._span[data-v-c6b99c78],\n._strong[data-v-c6b99c78],\n._sub[data-v-c6b99c78],\n._sup[data-v-c6b99c78]{display:inline}\n\n",""]),t.exports=e},baa5:function(t,e,n){var a=n("23e7"),r=n("e58c");a({target:"Array",proto:!0,forced:r!==[].lastIndexOf},{lastIndexOf:r})},bee3:function(t,e,n){var a=n("5dec");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=n("4f06").default;r("46d9383e",a,!0,{sourceMap:!1,shadowMode:!1})},c0f8:function(t,e,n){"use strict";n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("14d9"),n("e25e"),n("caad"),n("2532");var r=a(n("246a")),s={name:"node",data:function(){return{ctrl:{}}},props:{name:String,attrs:{type:Object,default:function(){return{}}},childs:Array,opts:Array},components:{node:r.default},mounted:function(){var t=this;for(this.root=this.$parent;"mp-html"!=this.root.$options.name;this.root=this.root.$parent);if(this.opts[0]){for(var e=this.childs.length;e--;)if("img"==this.childs[e].name)break;-1!=e&&(this.observer=uni.createIntersectionObserver(this).relativeToViewport({top:500,bottom:500}),this.observer.observe("._img",(function(e){e.intersectionRatio&&(t.$set(t.ctrl,"load",1),t.observer.disconnect())})))}},beforeDestroy:function(){this.observer&&this.observer.disconnect()},methods:{play:function(t){if(this.root.pauseVideo){for(var e=!1,n=t.target.id,a=this.root._videos.length;a--;)this.root._videos[a].id==n?e=!0:this.root._videos[a].pause();if(!e){var r=uni.createVideoContext(n,this);r.id=n,this.root._videos.push(r)}}},imgTap:function(t){var e=this.childs[t.currentTarget.dataset.i];if(e.a)return this.linkTap(e.a);e.attrs.ignore||(e.attrs.src=e.attrs.src||e.attrs["data-src"],this.root.$emit("imgTap",e.attrs),this.root.previewImg&&uni.previewImage({current:parseInt(e.attrs.i),urls:this.root.imgList}))},imgLongTap:function(t){},imgLoad:function(t){var e=t.currentTarget.dataset.i;(this.opts[1]&&!this.ctrl[e]||-1==this.ctrl[e])&&this.$set(this.ctrl,e,1)},linkTap:function(t){var e=t.currentTarget?this.childs[t.currentTarget.dataset.i].attrs:t,n=e.href;this.root.$emit("linkTap",e),n&&("#"==n[0]?this.root.navigateTo(n.substring(1)).catch((function(){})):n.includes("://")?this.root.copyLink&&window.open(n):uni.navigateTo({url:n,fail:function(){uni.switchTab({url:n,fail:function(){}})}}))},mediaError:function(t){var e=t.currentTarget.dataset.i,n=this.childs[e];if("video"==n.name||"audio"==n.name){var a=(this.ctrl[e]||0)+1;if(a>n.src.length&&(a=0),a<n.src.length)return this.$set(this.ctrl,e,a)}else"img"==n.name&&this.opts[2]&&this.$set(this.ctrl,e,-1);this.root&&this.root.$emit("error",{source:n.name,attrs:n.attrs,errMsg:t.detail.errMsg})}}};e.default=s},c3a9:function(t,e,n){"use strict";var a=n("bee3"),r=n.n(a);r.a},c80a:function(t,e,n){"use strict";n.r(e);var a=n("0614c"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},c88e:function(t,e,n){"use strict";n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("14d9"),n("d3b7"),n("e25e"),n("ac1f"),n("5319"),n("99af");var r=a(n("6f5d")),s=a(n("246a")),i=[],o=n("6413"),l={name:"mp-html",data:function(){return{nodes:[]}},mixins:[r.default],components:{node:s.default},watch:{content:function(t){this.setContent(t)}},created:function(){this.plugins=[];for(var t=i.length;t--;)this.plugins.push(new i[t](this))},mounted:function(){this.content&&!this.nodes.length&&this.setContent(this.content)},beforeDestroy:function(){this._hook("onDetached"),clearInterval(this._timer)},methods:{in:function(t,e,n){t&&e&&n&&(this._in={page:t,selector:e,scrollTop:n})},navigateTo:function(t,e){var n=this;return new Promise((function(a,r){if(!n.useAnchor)return r("Anchor is disabled");e=e||parseInt(n.useAnchor)||0;var s=uni.createSelectorQuery().in(n._in?n._in.page:n).select((n._in?n._in.selector:"._root")+(t?"".concat(" ","#").concat(t):"")).boundingClientRect();n._in?s.select(n._in.selector).scrollOffset().select(n._in.selector).boundingClientRect():s.selectViewport().scrollOffset(),s.exec((function(t){if(!t[0])return r("Label not found");var s=t[1].scrollTop+t[0].top-(t[2]?t[2].top:0)+e;n._in?n._in.page[n._in.scrollTop]=s:uni.pageScrollTo({scrollTop:s,duration:300}),a()}))}))},getText:function(){var t="";return function e(n){for(var a=0;a<n.length;a++){var r=n[a];if("text"==r.type)t+=r.text.replace(/&amp;/g,"&");else if("br"==r.name)t+="\n";else{var s="p"==r.name||"div"==r.name||"tr"==r.name||"li"==r.name||"h"==r.name[0]&&r.name[1]>"0"&&r.name[1]<"7";s&&t&&"\n"!=t[t.length-1]&&(t+="\n"),r.children&&e(r.children),s&&"\n"!=t[t.length-1]?t+="\n":"td"!=r.name&&"th"!=r.name||(t+="\t")}}}(this.nodes),t},getRect:function(){var t=this;return new Promise((function(e,n){uni.createSelectorQuery().in(t).select("#_root").boundingClientRect().exec((function(t){return t[0]?e(t[0]):n("Root label not found")}))}))},setContent:function(t,e){var n=this;e&&this.imgList||(this.imgList=[]);var a,r=new o(this).parse(t);this.$set(this,"nodes",e?(this.nodes||[]).concat(r):r),this._videos=[],this.$nextTick((function(){n._hook("onLoad"),n.$emit("load")})),clearInterval(this._timer),this._timer=setInterval((function(){n.getRect().then((function(t){t.height==a&&(n.$emit("ready",t),clearInterval(n._timer)),a=t.height})).catch((function(){}))}),350)},_hook:function(t){for(var e=i.length;e--;)this.plugins[e][t]&&this.plugins[e][t]()}}};e.default=l},c946:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("14d9"),n("ac1f"),n("5319"),n("c975"),n("466d"),n("d9e2"),n("d401");var a=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z0-9_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,r=/^<\/([-A-Za-z0-9_]+)[^>]*>/,s=/([a-zA-Z0-9_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g;function i(t){for(var e={},n=t.split(","),a=0;a<n.length;a+=1)e[n[a]]=!0;return e}var o=i("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),l=i("address,code,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,ins,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),c=i("a,abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),d=i("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),u=i("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected");var p=function(t,e){var n,i,p,f=t,h=[];function g(t,n){var a;if(n){for(n=n.toLowerCase(),a=h.length-1;a>=0;a-=1)if(h[a]===n)break}else a=0;if(a>=0){for(var r=h.length-1;r>=a;r-=1)e.end&&e.end(h[r]);h.length=a}}function v(t,n,a,r){if(n=n.toLowerCase(),l[n])while(h.last()&&c[h.last()])g(0,h.last());if(d[n]&&h.last()===n&&g(0,n),r=o[n]||!!r,r||h.push(n),e.start){var i=[];a.replace(s,(function(t,e){var n=arguments[2]||arguments[3]||arguments[4]||(u[e]?e:"");i.push({name:e,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(n,i,r)}}h.last=function(){return h[h.length-1]};while(t){if(i=!0,0===t.indexOf("</")?(p=t.match(r),p&&(t=t.substring(p[0].length),p[0].replace(r,g),i=!1)):0===t.indexOf("<")&&(p=t.match(a),p&&(t=t.substring(p[0].length),p[0].replace(a,v),i=!1)),i){n=t.indexOf("<");var m="";while(0===n)m+="<",t=t.substring(1),n=t.indexOf("<");m+=n<0?t:t.substring(0,n),t=n<0?"":t.substring(n),e.chars&&e.chars(m)}if(t===f)throw new Error("Parse Error: ".concat(t));f=t}g()};e.default=p},ce43:function(t,e,n){"use strict";var a=n("7d1c"),r=n.n(a);r.a},d25d:function(t,e,n){"use strict";n.r(e);var a=n("c0f8"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},d87b:function(t,e,n){"use strict";n.r(e);var a=n("6781"),r=n("ae35");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=o.exports},dbeb:function(t,e,n){"use strict";n.r(e);var a=n("7db9"),r=n("20cb");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=o.exports},ded3:function(t,e,n){n("b64b"),n("a4d3"),n("4de4"),n("d3b7"),n("e439"),n("14d9"),n("159b"),n("dbb4"),n("1d1c"),n("7a82");var a=n("9523");function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}t.exports=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){a(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t},t.exports.__esModule=!0,t.exports["default"]=t.exports},e048:function(t,e,n){"use strict";n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(n("d87b")),s=a(n("fc4a")),i=a(n("49d1")),o=a(n("dbeb")),l=a(n("3642")),c={name:"wxParseTemplate",props:{node:{}},components:{wxParseTemplate:r.default,wxParseImg:s.default,wxParseVideo:i.default,wxParseAudio:o.default,wxParseTable:l.default},methods:{wxParseATap:function(t,e){var n=e.currentTarget.dataset.href;if(n){var a=this.$parent;while(!a.preview||"function"!==typeof a.preview)a=a.$parent;a.navigate(n,e,t)}}}};e.default=c},e50d:function(t,e,n){n("8172"),n("efec"),n("a4d3"),n("e01a"),n("d3b7"),n("d9e2"),n("d401"),n("a9e3");var a=n("7037")["default"];t.exports=function(t,e){if("object"!==a(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports},ee9d:function(t,e,n){"use strict";(function(t){n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("ac1f"),n("00b4"),n("4d63"),n("c607"),n("2c3e"),n("25f0"),n("5319"),n("13d5"),n("d3b7"),n("466d"),n("14d9"),n("159b"),n("b64b"),n("99af"),n("3c65"),n("498a");var r=a(n("5961")),s=a(n("c946"));function i(t){for(var e={},n=t.split(","),a=0;a<n.length;a+=1)e[n[a]]=!0;return e}var o=i("br,code,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,ins,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),l=i("a,abbr,acronym,applet,b,basefont,bdo,big,button,cite,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),c=i("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr");var d=function(e,n,a,i){e=function(t){var e=/<body.*>([^]*)<\/body>/.test(t);return e?RegExp.$1:t}(e),e=function(t){return t.replace(/<!--.*?-->/gi,"").replace(/\/\*.*?\*\//gi,"").replace(/<script[^]*<\/script>/gi,"").replace(/<style[^]*<\/style>/gi,"")}(e),e=r.default.strDiscode(e);var d=[],u={nodes:[],imageUrls:[]},p=function(){var t={};return wx.getSystemInfo({success:function(e){t.width=e.windowWidth,t.height=e.windowHeight}}),t}();function f(t){this.node="element",this.tag=t,this.$screen=p}return(0,s.default)(e,{start:function(t,e,s){var i=new f(t);if(0!==d.length){var p=d[0];void 0===p.nodes&&(p.nodes=[])}if(o[t]?i.tagType="block":l[t]?i.tagType="inline":c[t]&&(i.tagType="closeSelf"),i.attr=e.reduce((function(t,e){var n=e.name,a=e.value;return"class"===n&&(i.classStr=a),"style"===n&&(i.styleStr=a),a.match(/ /)&&(a=a.split(" ")),t[n]?Array.isArray(t[n])?t[n].push(a):t[n]=[t[n],a]:t[n]=a,t}),{}),i.classStr?i.classStr+=" ".concat(i.tag):i.classStr=i.tag,"inline"===i.tagType&&(i.classStr+=" inline"),"img"===i.tag){var h=i.attr.src;h=r.default.urlToHttpUrl(h,a.domain),Object.assign(i.attr,a,{src:h||""}),h&&u.imageUrls.push(h)}if("a"===i.tag&&(i.attr.href=i.attr.href||""),"table"!==i.tag&&"tr"!==i.tag&&"td"!==i.tag||(i.styleStr="",i.attr.width&&(i.styleStr+="width:"+i.attr.width+"px;",i.attr.width>i.$screen.width&&i.attr.height&&(i.attr.height=i.$screen.width*i.attr.height/i.attr.width)),i.attr.height&&(i.styleStr+="height:"+i.attr.height+"px;")),"video"===i.tag&&(i.styleStr="",i.attr.width&&(i.styleStr+="width:"+i.attr.width+"px;",i.attr.width>i.$screen.width&&i.attr.height&&(i.attr.height=i.$screen.width*i.attr.height/i.attr.width)),i.attr.height&&(i.styleStr+="height:"+i.attr.height+"px;")),"font"===i.tag){var g=["x-small","small","medium","large","x-large","xx-large","-webkit-xxx-large"],v={color:"color",face:"font-family",size:"font-size"};i.styleStr||(i.styleStr=""),Object.keys(v).forEach((function(t){if(i.attr[t]){var e="size"===t?g[i.attr[t]-1]:i.attr[t];i.styleStr+="".concat(v[t],": ").concat(e,";")}}))}if("source"===i.tag&&(u.source=i.attr.src),n.start&&n.start(i,u),s){var m=d[0]||u;void 0===m.nodes&&(m.nodes=[]),m.nodes.push(i)}else d.unshift(i)},end:function(e){var a=d.shift();if(a.tag!==e&&t("error","invalid state: mismatch end tag"," at components/feng-parse/libs/html2json.js:256"),"video"===a.tag&&u.source&&(a.attr.src=u.source,delete u.source),n&&n.end&&n.end(a,u),0===d.length)u.nodes.push(a);else{var r=d[0];r.nodes||(r.nodes=[]),r.nodes.push(a)}},chars:function(t){if(t.trim()){var e={node:"text",text:t};if(n.chars&&n.chars(e,u),0===d.length)u.nodes.push(e);else{var a=d[0];void 0===a.nodes&&(a.nodes=[]),a.nodes.push(e)}}}}),u};e.default=d}).call(this,n("0de9")["log"])},f84a:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)}}},[t.playState?t._e():n("v-uni-view",{staticClass:"video-video",class:t.node.classStr,staticStyle:{display:"inline-block",margin:"auto","max-width":"100%"},style:t.node.styleStr},[n("v-uni-view",{staticStyle:{display:"flex",width:"100%",height:"100%","flex-direction":"row","justify-content":"center","align-items":"center"}},[n("v-uni-image",{staticStyle:{width:"20%"},attrs:{src:"https://gwbj.tongwenkeji.com/html/static/play.png",mode:"widthFix"}})],1)],1),t.playState?n("v-uni-video",{staticClass:"video-video",class:t.node.classStr,style:t.node.styleStr,attrs:{autoplay:!1,src:t.node.attr.src}}):t._e()],1)},r=[]},fb18:function(t,e,n){"use strict";n.r(e);var a=n("8147"),r=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},fc4a:function(t,e,n){"use strict";n.r(e);var a=n("9d94"),r=n("fb18");for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);var i=n("f0c5"),o=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=o.exports}}]);