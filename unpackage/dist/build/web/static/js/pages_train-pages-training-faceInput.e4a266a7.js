(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_train-pages-training-faceInput"],{1173:function(n,t,e){"use strict";e.r(t);var a=e("64d7"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);t["default"]=i.a},"172c":function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return a}));var a={gracePage:e("c14d").default},i=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[e("my-header",{attrs:{slot:"gHeader",title:"验证"},slot:"gHeader"}),e("v-uni-view",{staticStyle:{height:"200px",width:"auto",margin:"auto"},attrs:{slot:"gBody"},slot:"gBody"},[e("v-uni-button",{on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.faceInput.apply(void 0,arguments)}}},[n._v("录入照片")])],1)],1)},o=[]},6429:function(n,t,e){"use strict";e.r(t);var a=e("172c"),i=e("1173");for(var o in i)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(o);var r=e("f0c5"),u=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=u.exports},"64d7":function(n,t,e){"use strict";(function(n){e("7a82");var a=e("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("99af");var i=a(e("c7eb")),o=a(e("1da1")),r=a(e("5b8c")),u={data:function(){return{option:{}}},onLoad:function(n){this.option=n},methods:{faceInput:function(){var t=this;uni.chooseMedia({count:1,sizeType:["original"],mediaType:["image"],sourceType:["camera"],camera:"front",success:function(e){var a=e.tempFiles[0].tempFilePath;t.option.companyId instanceof Array&&(t.option.companyId=t.option.companyId[0]),uni.uploadFile({url:r.default.apiServer+"app/user/pxVerifyImage",filePath:a,name:"file",formData:{EnterpriseID:t.option.companyId,imageUserId:t.option.id,personalTrainingId:t.option.personalTrainingId},fail:function(t){n("log","上传文件错误",t," at pages_train/pages/training/faceInput.vue:45")},success:function(){var n=(0,o.default)((0,i.default)().mark((function n(e){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:200===e.statusCode&&t.gotoCourse();case 1:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()})},fail:function(n){},complete:function(n){}})},gotoCourse:function(n){uni.navigateTo({url:"/pages_train/pages/training/courses/course?name=".concat(this.option.name,"&companyId=").concat(this.option.companyId,"&id=").concat(this.option._id)})}}};t.default=u}).call(this,e("0de9")["log"])}}]);