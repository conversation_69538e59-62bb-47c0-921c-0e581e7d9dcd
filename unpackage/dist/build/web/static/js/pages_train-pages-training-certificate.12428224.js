(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_train-pages-training-certificate"],{"052b":function(t,e,i){"use strict";var a=i("9602"),c=i.n(a);c.a},"3319c":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";.ycBadge[data-v-39d1d437]{width:140px;height:140px;position:absolute;right:172px;bottom:70px;opacity:.8}.certificateImg2 uni-image[data-v-39d1d437]{width:100%}.grey[data-v-39d1d437]{color:#909399;font-size:14px;font-weight:400;width:80%;text-align:center}.downLoad[data-v-39d1d437]{float:right}.contant[data-v-39d1d437]{width:100%;padding-top:20px;overflow:scroll;position:relative}.certificateImg[data-v-39d1d437]{width:900px;height:630px;background-size:cover;background-position:50%;position:relative;font-family:宋体;font-weight:700;font-size:13px}.certificateImg .title[data-v-39d1d437]{position:absolute;top:129px;left:344px;letter-spacing:1px;font-weight:700;font-size:26px}.certificateImg .avatar[data-v-39d1d437]{position:absolute;top:226px;left:225px;width:84px;height:116px}.certificateImg .name[data-v-39d1d437]{position:absolute;top:366px;left:166px}.certificateImg .IDNum[data-v-39d1d437]{position:absolute;top:390px;left:166px}.certificateImg .companyName[data-v-39d1d437]{position:absolute;top:414px;left:166px}.certificateImg .number[data-v-39d1d437]{position:absolute;top:439px;left:166px}.certificateImg .trainingName[data-v-39d1d437]{position:absolute;top:195px;left:487px}.certificateImg .trainingTime[data-v-39d1d437]{position:absolute;top:195px;left:639px}.certificateImg .grace-table[data-v-39d1d437]{position:absolute;top:220px;left:492px;width:270px;font-weight:400;text-align:left;font-size:12px;font-family:Microsoft YaHei,微软雅黑,Microsoft JhengHei,华文细黑,STHeiti,MingLiu;line-height:26px}.certificateImg .grace-table .grace-theader[data-v-39d1d437]{font-weight:700;text-align:left}.certificateImg .grace-table .grace-theader .grace-td[data-v-39d1d437]{width:auto}.certificateImg .grace-table .grace-tbody .grace-td[data-v-39d1d437]{width:auto}.certificateImg .h3[data-v-39d1d437]{position:absolute;top:460px;right:138px;font-size:%?36?%}.certificateImg .issuanceTime[data-v-39d1d437]{position:absolute;top:486px;right:138px;font-size:14px}.certificateImg .signature[data-v-39d1d437]{position:absolute;top:508px;right:138px;font-size:14px}.certificateImg .type3[data-v-39d1d437]{text-align:center;padding-top:250px}.certificateImg .type3 p[data-v-39d1d437]{font-size:20px;margin-top:20px}.certificateImg .type3 p span[data-v-39d1d437]{text-decoration:underline;padding:0 5px}.certificateImg .type3 .h3[data-v-39d1d437], .certificateImg .type3 .issuanceTime[data-v-39d1d437]{font-size:16px;right:190px;top:450px}.certificateImg .type3 .issuanceTime[data-v-39d1d437]{top:476px}',""]),t.exports=e},5295:function(t,e,i){"use strict";i("7a82");var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var c=a(i("1067")),n={getCount:function(t){return(0,c.default)({url:"manage/adminTraining/getCertificateCount",method:"get",param:t})},add:function(t){return(0,c.default)({url:"manage/adminTraining/addCertificate",method:"post",data:t})},purchase:function(t){return(0,c.default)({url:"manage/adminTraining/purchaseCertificate",method:"post",data:t})},get:function(t){return(0,c.default)({url:"manage/adminTraining/getCertificate",method:"post",data:t})},getMyInfo:function(t){return(0,c.default)({url:"manage/adminTraining/getMyInfo",method:"post",data:t})},wxSpPay:function(t){return(0,c.default)({url:"app/pay/wxSpPay",method:"post",data:t})},trainNativePay:function(t){return(0,c.default)({url:"app/trainPay/trainNativePay",method:"post",data:t})},useCdk:function(t){return(0,c.default)({url:"manage/adminTraining/useCdk",method:"post",data:t})}};e.default=n},"660b":function(t,e,i){"use strict";i.d(e,"b",(function(){return c})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={gracePage:i("c14d").default},c=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[i("my-header",{attrs:{slot:"gHeader",title:"培训证书"},slot:"gHeader"}),i("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody",id:"gBody"},slot:"gBody"},[i("v-uni-view",{staticClass:"header grace-margin-top",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.generate.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("培训通过，恭喜获得证书：")])],1),t.certificateDeatil&&t.certificateDeatil.winner?i("v-uni-view",{staticClass:"contant"},[t.certificateDeatil.img?i("v-uni-view",{staticClass:"certificateImg2"},[i("v-uni-image",{attrs:{src:t._f("imgPath")(t.certificateDeatil.img)},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ViewImage.apply(void 0,arguments)}}})],1):i("v-uni-view",{staticClass:"certificateImg",style:{backgroundImage:"url("+t.bjImgSrc+")"},attrs:{id:"certificateImg"}},[3===t.certificateDeatil.trainingType?i("v-uni-view",{staticClass:"type3"},[i("p",[i("span",[t._v(t._s(" "+t.certificateDeatil.winner.name+" "))]),t._v("，在职业卫生数字化平台完成"),i("span",[t._v(t._s(" "+new Date(t.certificateDeatil.createdAt).getFullYear()+" "))]),t._v("年度")]),i("p",[t._v("职业卫生培训，累计"),i("span",[t._v(t._s(" "+t.totleClassHours+" "))]),t._v("学时，考试成绩"),i("span",[t._v(t._s(" "+t.testRecord+" "))]),t._v("分，")]),i("p",[t._v("完成结业。")]),i("v-uni-view",{staticClass:"h3"},[t._v(t._s(t.certificateDeatil.unit||""))]),i("v-uni-view",{staticClass:"issuanceTime"},[t._v(t._s(t.certificateDeatil.issuanceTime?t.formatTime(t.certificateDeatil.issuanceTime):""))])],1):i("v-uni-view",[t.certificateDeatil.winner.headImg?i("v-uni-image",{staticClass:"avatar",attrs:{src:t._f("imgPath")(t.certificateDeatil.winner.headImg),alt:""}}):t._e(),i("p",{staticClass:"name"},[t._v("姓   名："+t._s(t.certificateDeatil.winner.name))]),t.certificateDeatil.winner&&t.certificateDeatil.winner.IDNum?i("p",{staticClass:"IDNum"},[t._v("身份证号："+t._s(t.certificateDeatil.winner.IDNum))]):t._e(),t.certificateDeatil.winner&&t.certificateDeatil.winner.companyName?i("p",{staticClass:"companyName"},[t._v("工作单位："+t._s(t.certificateDeatil.winner.companyName))]):t._e(),i("p",{staticClass:"number"},[t._v("证书编号："+t._s(t.certificateDeatil.number||""))]),t.certificateDeatil.trainingDetail.coursesList?i("v-uni-view",{staticClass:"grace-table"},[i("v-uni-view",{staticClass:"grace-theader"},[i("v-uni-text",{staticClass:"grace-td "},[t._v("课程")])],1),t.isYc?i("v-uni-view",{staticClass:"grace-tbody"},[i("v-uni-text",{staticClass:"grace-td"},[t._v("职业卫生培训课程")])],1):t._l(t.certificateDeatil.trainingDetail.coursesList,(function(e){return i("v-uni-view",{key:e._id,staticClass:"grace-tbody"},[i("v-uni-text",{staticClass:"grace-td"},[t._v("· "+t._s(e.name)+"（"+t._s(e.classHours||0)+"学时）")])],1)}))],2):t._e(),i("v-uni-view",{staticClass:"h3"},[t._v(t._s(t.certificateDeatil.unit||""))]),i("v-uni-view",{staticClass:"issuanceTime"},[t._v("发证日期："+t._s(t.certificateDeatil.issuanceTime?t.formatTime(t.certificateDeatil.issuanceTime):""))]),t.certificateDeatil.effectiveTime?i("v-uni-view",{staticClass:"signature"},[t._v("有效期至："+t._s(t.certificateDeatil.effectiveTime?t.formatTime(t.certificateDeatil.effectiveTime):""))]):t._e(),t.isYc?i("v-uni-image",{staticClass:"ycBadge",attrs:{src:t.ycSrc,alt:""}}):t._e()],1)],1)],1):t._e()],1)],1)},n=[]},9602:function(t,e,i){var a=i("3319c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var c=i("4f06").default;c("51612a5f",a,!0,{sourceMap:!1,shadowMode:!1})},b654:function(t,e,i){"use strict";(function(t){i("7a82");var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c975"),i("d3b7"),i("159b");var c=a(i("5295")),n=a(i("d8be")),r=i("5b8c"),s="https://zyws.cn/static/reportTemplate/",o={data:function(){return{id:"",certificateDeatil:{},bjImgSrc:"",isYc:!1,ycSrc:s+"yc.png",totleClassHours:0,testRecord:0}},onLoad:function(t){this.id=t.id||""},computed:{formatTime:function(){return function(t){var e=new Date(t);return(0,n.default)(e).format("YYYY-MM-DD")}}},mounted:function(){this.getCertificate()},methods:{getCertificate:function(e){var i=this;this.id&&c.default.get({_id:this.id}).then((function(e){t("log","获取到的证书详情：",e.data," at pages_train/pages/training/certificate.vue:91"),i.certificateDeatil=e.data||{},-1!=i.certificateDeatil.unit.indexOf("越城区")&&(i.isYc=!0),"hf"==i.certificateDeatil.branch?i.bjImgSrc=s+(3===i.certificateDeatil.trainingType?"certificate_bj3.png":"certificate_bj.jpg"):i.bjImgSrc=s+(3===i.certificateDeatil.trainingType?"certificate_bj3.png":"certificate_bj_hf.jpg");var a=0;i.certificateDeatil.trainingDetail.coursesList.forEach((function(t){a+=t.classHours})),i.totleClassHours=a,i.certificateDeatil.testRecord&&i.certificateDeatil.testRecord.resultStatistics&&(i.testRecord=i.certificateDeatil.testRecord.resultStatistics.actualScore)}))},ViewImage:function(t){var e=this.certificateDeatil.img||"";e&&e.length<=60&&(e=(0,r.imgPath)(e)),uni.previewImage({urls:[e],current:e})}},filters:{imgPath:function(t){return(0,r.imgPath)(t)}}};e.default=o}).call(this,i("0de9")["log"])},bf8a:function(t,e,i){"use strict";i.r(e);var a=i("660b"),c=i("c8c8");for(var n in c)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return c[t]}))}(n);i("052b");var r=i("f0c5"),s=Object(r["a"])(c["default"],a["b"],a["c"],!1,null,"39d1d437",null,!1,a["a"],void 0);e["default"]=s.exports},c8c8:function(t,e,i){"use strict";i.r(e);var a=i("b654"),c=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=c.a}}]);