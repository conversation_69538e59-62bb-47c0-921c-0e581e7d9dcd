const _ = require('lodash');
const orgValidator = require('../../validate/adminorg');
const {
  siteFunc,
} = require('../../utils');
const fs = require('fs');
const awaitWriteStream = require('await-stream-ready').write;
const sendToWormhole = require('stream-wormhole');
const path = require('path');

const AdminorgController = {


  async list(ctx) {
    try {
      const query = ctx.query || {};
      const searchKeys = [ 'cname' ];
      const cname = query.cname || '';
      const params = {
        searchKeys,
        files: {
          _id: 1,
          cname: 1,
        },
        sort: {
          createTime: -1,
        },
      };

      const adminorgList = cname === '' ? [] : await ctx.service.adminorg.find({ isPaging: '0', searchkey: cname }, params);
      ctx.helper.renderSuccess(ctx, {
        data: adminorgList,
        message: '返回数据成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取行业分类信息
  async getIndustryCategory(ctx) {
    try {
      const indusutryArray = await ctx.service.industryCategory.find({ isPaging: '0' }, { sort: { sort: 1 } });
      ctx.helper.renderSuccess(ctx, {
        data: indusutryArray,
      });
    } catch (err) {
      ctx.logger.error(new Error(err));
      ctx.helper.renderFail(ctx, {
        message: '哎呀！网络开小差了！要不，稍后再试试？',
      });
    }
  },

  // 新加企业

  async create(ctx) {
    try {
      // 管理员ID字段
      // CMS申请进来的入口
      const fields = ctx.request.body || {};
      const formObj = {
        cname: fields.cname,
        code: fields.code,
        regAdd: fields.regAdd,
        workAdd: fields.workAdd,
        corp: fields.corp,
        industryCategory: fields.industryCategory,

        districtRegAdd: fields.districtRegAdd,
        districtWorkAdd: fields.districtWorkAdd,
        workAddress: fields.workAddress,

        introduce: fields.introduce,
        money: fields.money,
        area: fields.area,
        type: fields.type,
        level: fields.level,
        img: fields.img,
        licensePic: fields.licensePic,
        PID: '0',
        adminUserId: fields.adminUserId, // 必填，要做校验。
        message: fields.message || '',
        createTime: new Date(),
      };
      // 表单校验
      ctx.validate(orgValidator.adminorgRule(ctx), formObj);
      // 查询组织机构代码
      const oldItem = await ctx.service.adminorg.item(ctx, {
        query: {
          code: fields.code,
        },
      });
      if (!_.isEmpty(oldItem)) {
        ctx.helper.renderFail(ctx, {
          status: 200,
          message: '该企业组织机构代码' + oldItem.code + '已存在。',
        });
        // throw new Error(ctx.__('用户' + fields.userName + '已存在，可以直接登录平台。'));
      } else {
        // 添加数据
        await ctx.service.adminorg.create(formObj);

        ctx.helper.renderSuccess(ctx, {
          status: 200,
          message: '企业已添加！',
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },


  async update(ctx) {
    try {
      const fields = ctx.request.body || {};
      console.log('入参', fields);

      const formObj = {

        cname: fields.cname,
        code: fields.code,
        regAdd: fields.regAdd,
        workAdd: fields.workAdd,
        corp: fields.corp,
        industryCategory: fields.industryCategory,

        districtRegAdd: fields.districtRegAdd,
        districtWorkAdd: fields.districtWorkAdd,
        workAddress: fields.workAddress,


        introduce: fields.introduce,
        money: fields.money,
        area: fields.area,
        type: fields.type,
        level: fields.level,
        img: fields.img,


        licensePic: fields.licensePic,
        isactive: fields.isactive || '0',
        PID: fields.PID,
        adminUserId: fields.adminUserId, // 必填，要做校验。
        message: fields.message || '',
        updateTime: new Date(),
      };
      // 表单校验
      ctx.validate(orgValidator.adminorgRule(ctx), formObj);
      await ctx.service.adminorg.update(ctx, fields._id, formObj);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async userInfo(ctx) {
    try {
      const _id = ctx.request.body.id;
      console.log('参数========================');
      console.log(_id);
      const targetItem = await ctx.service.adminorg.item2(ctx, {
        query: {
          _id,
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },


  async uploadLicensePic(ctx, app) {

    try {
      const config = app.config;
      const configFileType = config.imageType || [];
      const publicDir = !_.isEmpty(config.upload_path) ? config.upload_path : '';

      const stream = await ctx.getFileStream();

      const beforeUploadFileInfo = await siteFunc.getFileInfoByStream('{ORGID}/{time}{rand:6}', stream, ctx);
      const {
        uploadForder,
        uploadFileName,
        fileType,
      } = beforeUploadFileInfo;

      if (configFileType.includes(fileType)) {
        const uploadPath = `${publicDir}/${uploadForder}`;
        if (!fs.existsSync(uploadPath)) {
          fs.mkdirSync(uploadPath);
        }
        const target = path.join(uploadPath, `${uploadFileName}${fileType}`);
        console.log(target);
        const writeStream = fs.createWriteStream(target);
        try {
          await awaitWriteStream(stream.pipe(writeStream));
        } catch (err) {
          // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
          await sendToWormhole(stream);
          throw err;
        }
        const returnPath = `${config.static.prefix}${config.upload_http_path}/${uploadForder}/${uploadFileName}${fileType}`;
        // 设置响应内容和响应状态码
        ctx.helper.renderSuccess(ctx, {
          data: {
            path: returnPath,
          },
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '抱歉！上传图片只能是 JPG,PNG,JPEG,BMP,JFIF 格式哦！',
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  },

  // 企业分页查询接口
  async enterpriseList(ctx) {
    try {
      const { query } = ctx;
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const res = await ctx.service.adminorg['queryEnterpriseListV' + version](query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '获取企业列表成功',
      });
    } catch (error) {
      ctx.auditLog('获取企业列表报错', error.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  },

  checkAccept(ctx, accept) {
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      throw new Error('版本号token错误，请检查');
    }
    return version;
  },
};

module.exports = AdminorgController;
