const moment = require('moment');
const orgValidator = require('../../validate/adminorg');
const { buildIndustry } = require('../../utils/siteFunc');
const AdminorgController = {


  async getWorkPlace(ctx) {
    ctx.helper.renderSuccess(ctx, {
      data: ctx.app.config.workplaceName || '',
    });
  },
  async list(ctx) {
    try {
      const payload = ctx.query;
      const query = ctx.query.query ? JSON.parse(ctx.query.query) : {};
      const collections = [
        {
          name: 'adminusers',
          selfKey: 'adminUserId',
          foreignKey: '_id',
          asKey: 'adminUserId',
        },
      ];
      const unwindArray = [ 'adminUserId' ];
      const searchKeys = [
        'cname',
        'regAdd',
        'corp',
        'code',
        'adminUserId.phoneNum',
      ];
      const statisticsFiles = [
        {
          field: {
            isactive: { $eq: '0' },
          },
          keyname: 'unPassCount',
          accumulator: { $sum: 1 },
        },
        {
          field: {
            isactive: { $eq: '1' },
          },
          keyname: 'passCount',
          accumulator: { $sum: 1 },
        },
        {
          field: {
            isactive: { $eq: '2' },
          },
          keyname: 'turnBackCount',
          accumulator: { $sum: 1 },
        },
      ];
      const params = {
        collections,
        unwindArray,
        query,
        searchKeys,
        statisticsFiles,
        files: {
          cname: 1,
          corp: 1,
          adminUserId: 1,
          code: 1,
          districtRegAdd: 1,
          regAdd: 1,
          workAdd: 1,
          industryCategory: 1,
          createTime: 1,
          licensePic: 1,
          isactive: 1,
        },
        sort: {
          createTime: -1,
        },
      };

      // const adminorgList = await ctx.service.adminorg.find(payload, params);
      const adminorgList = await ctx.service.adminorg.unionQuery(
        payload,
        params
      );
      const industryArray = await ctx.service.industryCategory.find(
        { isPaging: '0' },
        { sort: { sort: 1 } }
      );
      adminorgList.docs.map(async val => {
        if (val.industryCategory && val.industryCategory instanceof Array) {
          const buildIndustryArray = await buildIndustry(
            industryArray,
            val.industryCategory
          );
          val.industryCategory = buildIndustryArray.join(' ➤ ');
        }
        return (val.createTime = moment(val.createTime).format(
          'YYYY-MM-DD HH:mm:ss'
        ));
      });
      ctx.helper.renderSuccess(ctx, {
        data: adminorgList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  async listApp(ctx) {
    try {
      const query = ctx.query || {};
      const searchKeys = [ 'cname' ];
      const cname = query.cname || '';
      const params = {
        searchKeys,
        files: {
          _id: 1,
          cname: 1,
        },
        sort: {
          createTime: -1,
        },
      };

      const adminorgList =
        cname === ''
          ? []
          : await ctx.service.adminorg.find(
            { isPaging: '0', searchkey: cname },
            params
          );
      ctx.helper.renderSuccess(ctx, {
        data: adminorgList,
        message: '返回数据成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getOne(ctx) {
    try {
      const _id = ctx.query.id;

      const targetItem = await ctx.service.adminorg.item(ctx, {
        query: {
          _id,
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async update(ctx) {
    try {
      const fields = ctx.request.body || {};

      const formObj = {
        cname: fields.cname,
        code: fields.code,
        regAdd: fields.regAdd,
        workAdd: fields.workAdd,
        corp: fields.corp,
        industryCategory: fields.industryCategory,

        districtRegAdd: fields.districtRegAdd,
        districtWorkAdd: fields.districtWorkAdd,
        workAddress: fields.workAddress,

        introduce: fields.introduce,
        money: fields.money,
        area: fields.area,
        type: fields.type,
        level: fields.level,
        img: fields.img,

        licensePic: fields.licensePic,
        isactive: fields.isactive || '0',
        PID: fields.PID,
        adminUserId: fields.adminUserId, // 必填，要做校验。
        message: fields.message || '',
        updateTime: new Date(),
      };
      // 表单校验
      ctx.validate(orgValidator.adminorgRule(ctx), formObj);

      const beforUpdate = await ctx.service.adminorg.item(ctx, {
        query: {
          _id: fields._id,
        },
        populate: {
          path: 'adminUserId',
          select: 'phoneNum _id userName countryCode',
        },
      });

      await ctx.service.adminorg.update(ctx, fields._id, formObj);
      const afterUpdate = await ctx.service.adminorg.item(ctx, {
        query: {
          _id: fields._id,
        },
      });
      const statisticaltable = await ctx.service.statisticalTable.item(ctx, {
        query: {
          EnterpriseID: fields._id,
        },
      });
      const updateData = {
        EnterpriseID: afterUpdate._id,
        updateTime: formObj.updateTime,
        regTime: afterUpdate.createTime,
        regAdd: afterUpdate.districtRegAdd,
      };
      // 表示审核
      if (formObj.isactive === '1' && !statisticaltable) {
        // 审核通过，在StatisticalTable表创建对应的东西
        await ctx.service.statisticalTable.create(updateData);
      } else if (statisticaltable) {
        // 这里表示只是修改其他信息，有就要修改
        await ctx.service.statisticalTable.update(
          ctx,
          statisticaltable._id,
          updateData
        );
      }
      // 审核操作就发短信
      if (
        formObj.isactive !== beforUpdate.isactive &&
        formObj.isactive !== '0'
      ) {
        ctx.service.adminorg.sendVerifyMassage(ctx, beforUpdate, formObj);
      }
      ctx.auditLog(
        '修改企业',
        `当前用户修改_id为 ${fields._id} 的 ${formObj.cname} 信息成功。`,
        'info'
      );
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async removes(ctx) {
    try {
      const targetIds = ctx.query.ids;
      await ctx.service.adminorg.removes(ctx, targetIds);
      await ctx.service.statisticalTable.removes(
        ctx,
        targetIds,
        'EnterpriseID'
      );
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async upload(ctx) {
    try {
      console.log('上传接口！');
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
};

module.exports = AdminorgController;
