const Service = require('egg').Service;

class ApprovalPpeService extends Service {
  async getDefendproductsAuditList(params) {
    const { ctx } = this;
    const { query = {}, current = 1, pageSize = 10, year = new Date().getFullYear(), isSelected = new Date().getMonth() } = params;
    const regexMatch = {}; // 数据查询条件
    if (query.searchKey) {
      regexMatch.$or = [
        { workshop: { $regex: query.workplace || query.searchKey } },
        { workspaces: { $regex: query.workplace || query.searchKey } },
        { workstation: { $regex: query.workplace || query.searchKey } },
        { modelNumber: { $regex: query.searchKey } },
        { 'productIds.product': { $regex: query.product || query.searchKey } },
      ];
    }
    const pipeline = [
      { $match: regexMatch },
      { $match: { yearNumber: year } },
      { $match: { mouthNumber: isSelected } },
      {
        $match: {
          'auditRecords.needToAuditIds': { $in: [ params.userId ] },
        },
      },
      {
        $sort: {
          auditStatus: 1,
          applicationTime: -1,
        },
      },
      {
        $facet: {
          data: [
            {
              $skip: (current - 1) * Number(pageSize),
            },
            {
              $limit: Number(pageSize),
            },
            {
              $lookup: {
                from: 'employees',
                localField: 'employee',
                foreignField: '_id',
                as: 'employeeInfo',
              },
            },
            {
              $project: {
                auditRecords: 1,
                // workspaces: 1, // 车间
                // workshop: 1, // 厂房
                // workshopId: 1, // 厂房Id
                // workspace: 1, // 车间
                // workspaceId: 1, // 车间id
                workstation: 1, // 岗位
                // workstationId: 1, // 岗位id,
                products: 1, // 产品型号Id
                employeeInfo: 1, // 操作人
                applicationTime: 1, // 申请报废时间
                yearNumber: 1, // 年份
                mouthNumber: 1, // 月份
                updateAt: 1, // 更新申请时间
                auditStatus: 1, // 审核状态 0 未审核 1 已通过 2 被驳回
                sign: 1, // 签名图片
                notes: 1, // 备注
                claimType: 1, // 申请类型
                employee: 1, // 员工id
                auditLevel: 1, // 审核级别 1-一级审核 2-二级审核
              },
            },
          ],
          total: [{ $count: 'total' }],
        },

      },
      {
        $addFields: {
          'pageInfo.total': { $arrayElemAt: [ '$total.total', 0 ] },
          'pageInfo.pageSize': pageSize,
          'pageInfo.pageNum': current,
        },
      },
      {
        $project: {
          data: 1,
          pageInfo: 1,
        },
      },
    ];
    const res = await ctx.model.ApplicationProduct.aggregate(pipeline);
    if (res[0]) {
      // 如果res[0]存在，则返回res[0]
      return res[0];
    }
    return {
      data: [],
      totalLength: 0,
    };

  }
  /**
   * @summary 处理申请审批
   * @description 处理防护用品申请的审批操作，集成审批工作流
   * @param {Object} params - 审批参数
   * @param {string} params._id - 申请ID
   * @param {number} params.status - 审批状态 (1-通过, 2-驳回)
   * @param {number} params.auditLevel - 审批级别
   * @param {Object} params.reason - 审批意见
   * @param {Array} params.auditRecords - 审批记录
   * @return {Promise<string>} 处理结果消息
   */
  async selectApplication(params) {
    const { ctx } = this;
    const operator = ctx.session.user;
    // const EnterpriseID = ctx.session.user ? ctx.session.user.EnterpriseID : '';
    try {
      // 使用审批工作流服务处理状态转换
      const isApproved = params.status === 1; // 1-通过, 2-驳回
      const reason = params.reason ? params.reason.value : (isApproved ? '同意' : '驳回');

      ctx.logger.info(`开始处理审批: 申请ID=${params._id}, 审批级别=${params.auditLevel}, 结果=${isApproved ? '通过' : '驳回'}`);

      const workflowResult = await ctx.service.approvalWorkflow.processApproval(
        params._id,
        params.auditLevel || 1,
        isApproved,
        operator._id,
        operator.name,
        reason
      );

      if (workflowResult && workflowResult.success) {
        ctx.auditLog(
          '防护用品申请审批处理',
          `审批结果: ${isApproved ? '通过' : '驳回'}，申请ID: ${params._id}, 审批人: ${operator.name}`,
          'info'
        );

        // 如果是最终通过，添加领用记录
        if (workflowResult.finalStatus === 'completed') {
          await this.createReceiveRecord(params);
        }

        return workflowResult.message || '审批处理成功！';
      }

      ctx.logger.error('审批工作流返回失败结果', workflowResult);
      return '审批处理失败';

    } catch (workflowError) {
      ctx.logger.error('审批工作流处理失败', workflowError);
      ctx.auditLog(
        '防护用品申请审批处理失败',
        `错误信息: ${workflowError.message}，申请ID: ${params._id}, 审批人: ${operator.name}`,
        'error'
      );
      return '审批处理失败，请稍后重试';
    }
  }
  /**
   * @summary 创建领用记录
   * @description 审批通过后创建防护用品领用记录
   * @param {Object} params - 申请参数
   * @param {string} EnterpriseID - 企业ID
   * @return {Promise<void>}
   */
  async createReceiveRecord(params) {
    const { ctx } = this;

    try {
      // 获取申请详细信息（如果params中信息不完整）
      const applicationRes = await ctx.model.ApplicationProduct.aggregate([
        { $match: { _id: params._id } },
        {
          $lookup: {
            from: 'employees',
            localField: 'employee',
            foreignField: '_id',
            as: 'employeeInfo',
          },
        },
        {
          $unwind: '$employeeInfo',
        },
        {
          $project: {
            employeeInfo: 1,
            products: 1,
            EnterpriseID: 1,
            workspaceId: 1,
            workspace: 1,
            workstationId: 1,
            workstation: 1,
            employee: 1,
            applicationTime: 1,
            yearNumber: 1,
            mouthNumber: 1,
            auditStatus: 1,
            auditLevel: 1,
            auditRecords: 1,
          },
        },
      ]);
      const application = applicationRes[0];
      if (!application) {
        throw new Error('未找到申请记录');
      }

      // 使用申请记录中的信息
      const employeeInfo = application.employeeInfo || params.employeeInfo;
      const products = application.products || params.products;

      if (!employeeInfo || !products || products.length === 0) {
        throw new Error('申请信息不完整，无法创建领用记录');
      }

      const planId = `${application.employee}_${products[0].productIds}_${Date.now()}`;
      const now = new Date();

      // 🔑 从第一个产品中提取分类信息
      const firstProduct = products[0] || {};

      // 🔑 从产品库中获取分类信息（如果申请记录中缺少）
      let categoryInfo = {
        categoryId: firstProduct.categoryId,
        categoryName: firstProduct.categoryName,
        categoryPath: firstProduct.categoryPath,
      };

      // 如果申请记录中缺少分类信息，从产品库中查询
      if (!categoryInfo.categoryId && firstProduct.productIds && firstProduct.productIds[0]) {
        try {
          const productDetail = await ctx.model.ProtectiveProduct.findOne({
            _id: firstProduct.productIds[0],
            EnterpriseID: application.EnterpriseID,
          }).select('categoryId categoryName categoryPath');

          if (productDetail) {
            categoryInfo = {
              categoryId: productDetail.categoryId,
              categoryName: productDetail.categoryName,
              categoryPath: productDetail.categoryPath,
            };
            console.log('从产品库中获取分类信息:', categoryInfo);
          }
        } catch (error) {
          console.warn('获取产品分类信息失败:', error.message);
        }
      }

      const receiveRecord = {
        EnterpriseID: application.EnterpriseID,
        workspaces: application.workspaceId[0] || '', // 车间id
        workspacesName: application.workspace[0] || '', // 车间名称
        workstation: application.workstationId[0] || '', // 岗位id
        workstationName: application.workstation[0] || '', // 岗位名称
        employee: application.employee, // 员工id
        planId, // 申请生成的唯一标识
        products, // 产品列表

        // 🔑 添加分类信息（用于自动报废逻辑）
        categoryId: categoryInfo.categoryId,
        categoryName: categoryInfo.categoryName,
        categoryPath: categoryInfo.categoryPath,

        recordSource: 1, // 记录来源 0-发放 1-主动申请
        receiveStartDate: now,
        applicationId: params._id, // 关联申请ID
        createTime: now,
        updateTime: now,
      };

      // 添加厂房信息（如果存在）
      if (application.workshopId?.[0] && application.workshopId[0] !== 0) {
        receiveRecord.workshop = application.workshopId[0]; // 厂房id
      }
      if (application.workshop?.[0] && application.workshop[0] !== 0) {
        receiveRecord.workshopName = application.workshop[0]; // 厂房名称
      }

      await ctx.model.ReceiveRecord.create(receiveRecord);

      ctx.auditLog(
        '创建领用记录',
        `申请ID: ${params._id}, 员工: ${application.employee}, 产品数量: ${products.length}`,
        'info'
      );

      ctx.logger.info(`成功创建领用记录: planId=${planId}, employee=${application.employee}`);

    } catch (error) {
      ctx.logger.error('创建领用记录失败', error);
      // 记录错误但不阻断审批流程
      ctx.auditLog(
        '创建领用记录失败',
        `申请ID: ${params._id}, 错误: ${error.message}`,
        'error'
      );
    }
  }
}
module.exports = ApprovalPpeService;
