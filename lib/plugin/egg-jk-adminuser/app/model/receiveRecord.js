/*
 * @Author: 嵇汉文
 * @Date: 6 18th, 2020 3:48
 * @LastEditors: 汪周强
 * @LastEditTime: 2023-11-01 14:36:35
 * @Description: 防护用品发放记录
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const receiveRecordSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      EnterpriseID: { type: String },
      workshop: {
        // 厂房id
        type: String,
      },
      workspaces: {
        // 车间id
        type: String,
      },
      workstation: {
        // 岗位id
        type: String,
      },
      workshopName: {
        // 厂房id
        type: String,
      },
      workspacesName: {
        // 车间id
        type: String,
      },
      workstationName: {
        // 岗位id
        type: String,
      },
      departId: String,
      departName: String,
      employee: String, // 员工id

      // 防护用品分类字段
      categoryId: String, // 分类ID
      categoryPath: String, // 分类路径，如"头部防护/安全帽"
      categoryName: String, // 分类名称
      // productIds: [{ // 产品列表id
      //   type: String,
      // }],
      products: [
        {
          // 产品列表id
          _id: {
            type: String,
            default: shortid.generate,
          },
          productIds: [ String ], // 产品id
          productType: [ String ], // 用品分类
          product: String, // 产品名称
          modelNumber: String, // 型号
          productSpec: String, // 产品规格
          number: Number, // 数量
        },
      ],
      planId: String, // 绑定的计划id
      // product: String, // 产品名称
      // modelNumber: String, // 型号
      // number: Number, // 数量
      acknowledge: {
        required: true,
        type: Boolean,
        default: false,
      },
      isRejected: {
        type: Boolean,
        default: false,
      }, // 是否拒绝领取
      claimType: String, // 领取类型
      recordSource: Number, // 记录来源 0 发放 1 主动申请
      receiveStartDate: Date, // 领取开放时间
      // receiveEndDate: Date, // 领取截至时间
      warningDate: Date, // 预警时间
      warningId: String, // 预警id
      receiveDate: Date, // 实际领取时间

      // 生产日期和有效期管理
      productionDate: Date, // 产品生产日期（当产品需要记录生产日期时填写）
      expiryDate: Date, // 计算出的到期日期（生产日期+产品有效期）

      sign: String, // 签名图片
      scrap: {
        // 是否报废
        type: Boolean,
        default: false,
      },
      createAt: {
        // 创建日期
        type: Date,
      },
      updateAt: {
        // 更新时间
        type: Date,
      },
    },
    { timestamps: true }
  );
  return mongoose.model('receiveRecord', receiveRecordSchema);
};
