/*
 * @Author: 系统自动生成
 * @Date: 2024-07-24
 * @Description: 防护用品分类模型
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const protectionCategorySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    }, // 品类名称
    code: {
      type: String,
      trim: true,
    }, // 品类编码
    level: {
      type: Number,
      min: 1,
      default: 1,
    }, // 层级 (1-大类, 2-小类, 3-细分类...)
    parentId: {
      type: String,
      default: null,
    }, // 父级ID
    path: {
      type: String,
      trim: true,
    }, // 完整路径，如 "大类/小类/细分类"

    // 品类属性
    harmFactors: [{
      type: String,
      trim: true,
    }], // 适用危害因素
    defaultCycle: {
      time: {
        type: Number,
        min: 1,
      }, // 默认领用周期时间
      timeUnit: {
        type: String,
        enum: [ 'd', 'w', 'M', 'Q', 'y' ], // day/week/Month/Quarter/year
        default: 'M',
      },
    },

    // 扩展属性
    attributes: [{
      key: {
        type: String,
        required: true,
        trim: true,
      }, // 属性键
      value: {
        type: String,
        trim: true,
      }, // 属性值
      label: {
        type: String,
        required: true,
        trim: true,
      }, // 显示标签
      type: {
        type: String,
        enum: [ 'string', 'number', 'boolean', 'array' ],
        default: 'string',
      }, // 数据类型
      required: {
        type: Boolean,
        default: false,
      }, // 是否必填
    }],

    // 系统字段
    isLeaf: {
      type: Boolean,
      default: true,
    }, // 是否叶子节点
    isActive: {
      type: Boolean,
      default: true,
    }, // 是否启用
    sort: {
      type: Number,
      default: 0,
    }, // 排序
    topEnterpriseId: {
      type: String,
      required: true,
    }, // 顶级企业ID
    isSystemDefault: {
      type: Boolean,
      default: false,
    }, // 是否系统默认

    description: {
      type: String,
      trim: true,
    }, // 分类描述

  }, {
    timestamps: true, // 自动添加 createdAt 和 updatedAt
  });

  // 添加索引
  protectionCategorySchema.index({ topEnterpriseId: 1, parentId: 1, level: 1 });
  protectionCategorySchema.index({ topEnterpriseId: 1, isActive: 1, sort: 1 });
  protectionCategorySchema.index({ path: 1 });
  protectionCategorySchema.index({ harmFactors: 1 });
  protectionCategorySchema.index({ code: 1, topEnterpriseId: 1 }, { unique: true, sparse: true });


  // 添加虚拟字段 - 子分类数量
  protectionCategorySchema.virtual('childrenCount', {
    ref: 'protectionCategory',
    localField: '_id',
    foreignField: 'parentId',
    count: true,
  });

  // 中间件：保存前自动更新路径和叶子节点状态
  protectionCategorySchema.pre('save', async function(next) {
    try {
      // 更新路径
      if (this.parentId) {
        const parent = await this.constructor.findById(this.parentId);
        if (parent) {
          this.path = parent.path ? `${parent.path}/${this.name}` : this.name;
          this.level = parent.level + 1;

          // 更新父节点的叶子节点状态
          await this.constructor.updateOne(
            { _id: this.parentId },
            { $set: { isLeaf: false } }
          );
        }
      } else {
        this.path = this.name;
        this.level = 1;
      }

      // 生成编码（如果没有提供）
      if (!this.code) {
        const count = await this.constructor.countDocuments({
          topEnterpriseId: this.topEnterpriseId,
          level: this.level,
        });
        this.code = `${this.level.toString().padStart(2, '0')}-${(count + 1).toString().padStart(3, '0')}`;
      }

      next();
    } catch (error) {
      next(error);
    }
  });

  // 中间件：删除前检查是否有子分类
  protectionCategorySchema.pre('remove', async function(next) {
    try {
      const childrenCount = await this.constructor.countDocuments({ parentId: this._id });
      if (childrenCount > 0) {
        const error = new Error('无法删除包含子分类的分类');
        error.code = 'CATEGORY_HAS_CHILDREN';
        return next(error);
      }

      // 如果是父节点的最后一个子节点，更新父节点的叶子状态
      if (this.parentId) {
        const siblingCount = await this.constructor.countDocuments({
          parentId: this.parentId,
          _id: { $ne: this._id },
        });

        if (siblingCount === 0) {
          await this.constructor.updateOne(
            { _id: this.parentId },
            { $set: { isLeaf: true } }
          );
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  });

  return mongoose.model('protectionCategory', protectionCategorySchema);
};
