const Service = require('egg').Service;
const moment = require('moment');

class ApplicationProductService extends Service {
  // 获取申请列表
  async getList(params) {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    const enterpriseIds = await this.ctx.service.employee.findSubCompany(EnterpriseID, 'branch');
    const enterpriseData = [ EnterpriseID, ...enterpriseIds ];

    const { query = {}, current, pageSize, year, isSelected } = params;
    const regexMatch = {}; // 数据查询条件
    if (query.searchKey) {
      regexMatch.$or = [
        { workshop: { $regex: query.workplace || query.searchKey } },
        { workspaces: { $regex: query.workplace || query.searchKey } },
        { workstation: { $regex: query.workplace || query.searchKey } },
        { modelNumber: { $regex: query.searchKey } },
        { 'productIds.product': { $regex: query.product || query.searchKey } },
      ];
    }
    const pipeline = [
      { $match: { EnterpriseID: { $in: enterpriseData } } },
      { $match: regexMatch },
      { $match: { yearNumber: year } },
      { $match: { mouthNumber: isSelected } },
      {
        $sort: {
          auditStatus: 1,
          applicationTime: -1,
        },
      },
      {
        $facet: {
          data: [
            {
              $skip: (current - 1) * Number(pageSize),
            },
            {
              $limit: Number(pageSize),
            },
            {
              $lookup: {
                from: 'employees',
                localField: 'employee',
                foreignField: '_id',
                as: 'employeeInfo',
              },
            },
            {
              $project: {
                workspaces: 1, // 车间
                workshop: 1, // 厂房
                workshopId: 1, // 厂房Id
                workspace: 1, // 车间
                workspaceId: 1, // 车间id
                workstation: 1, // 岗位
                workstationId: 1, // 岗位id,
                products: 1, // 产品型号Id
                employeeInfo: 1, // 操作人
                applicationTime: 1, // 申请报废时间
                yearNumber: 1, // 年份
                mouthNumber: 1, // 月份
                updateAt: 1, // 更新申请时间
                auditStatus: 1, // 审核状态 0 未审核 1 已通过 2 被驳回
                sign: 1, // 签名图片
                notes: 1, // 备注
                claimType: 1, // 申请类型
                employee: 1, // 员工id
                auditLevel: 1, // 审核级别 1-一级审核 2-二级审核
                auditRecords: 1, // 审核记录
              },
            },
          ],
          totalLength: [{ $count: 'length' }],
        },
      },
    ];
    let res = await ctx.service.db.aggregate('ApplicationProduct', pipeline);
    let subProducts = [];
    if (app.config.branch === 'wh') {
      subProducts = await this.findProducts();
    }
    if (res[0]) {
      res = res[0];
      if (res.data.length > 0) {
        for (const item of res.data) {
          if (app.config.branch === 'wh') {
            // 根据employee 查询departsId
            const departs = await ctx.service.db.findOne('Employee', { _id: item.employee, EnterpriseID }, 'departs');
            const subProduct = item.products[0].productSpecId && subProducts.find(sub => sub._id === item.products[0].productSpecId);
            if (!subProduct) {
              item.maxNumber = '-';
              continue;
            }

            const maxNumber = await ctx.service.db.findOne('ProtectionPlan', {
              EnterpriseID,
              subRegion: {
                $elemMatch: {
                  $elemMatch: {
                    $eq: departs.departs[0],
                  },
                },
              },
              'products.product': subProduct.product,
            }, {
              'products.$': 1,
            });

            item.maxNumber = maxNumber ? maxNumber.products[0].maxNumber : '-';
          }
          if (item.applicationTime) {
            item.applicationTime = moment(item.applicationTime).format('YYYY-MM-DD HH:mm');
          }
          item.sign = `/static${app.config.upload_http_path}/${item.sign}`;
        }
      }
      res.totalLength = res.totalLength[0] ? res.totalLength[0].length : 0;
    }
    return res;
  }

  // 改变状态
  async selectApplication(params) {
    const { ctx } = this;
    const operator = ctx.session.adminUserInfo;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';

    // WH分支启用工作流审批
    if (this.config.branch === 'wh') {
      // 使用审批工作流服务处理状态转换
      try {
        const isApproved = params.status === 1; // 1-通过, 2-驳回
        const reason = params.reason ? params.reason.value : (isApproved ? '同意' : '驳回');

        const workflowResult = await ctx.service.approvalWorkflow.processApproval(
          params._id,
          params.auditLevel || 1,
          isApproved,
          operator._id,
          operator.name,
          reason
        );

        if (workflowResult && workflowResult.success) {
          ctx.auditLog(
            '防护用品申请审批处理',
            `审批结果: ${isApproved ? '通过' : '驳回'}，申请ID: ${params._id}`,
            'info'
          );

          // 如果是最终通过，添加领用记录
          if (workflowResult.finalStatus === 'completed') {
            await this.createReceiveRecord(params, EnterpriseID);
          }

          return workflowResult.message || '审批处理成功！';
        }
        return '审批处理失败';
      } catch (workflowError) {
        ctx.logger.error('审批工作流处理失败', workflowError);
        ctx.auditLog(
          '防护用品申请审批处理失败',
          `错误信息: ${workflowError.message}，申请ID: ${params._id}`,
          'error'
        );
        return '审批处理失败';
      }
    }

    // 非WH分支保持原有逻辑
    return await this.handleNonWHApproval(params, operator, EnterpriseID);
  }

  /**
   * @summary 创建领用记录
   * @description 审批通过后创建领用记录（支持新分类系统）
   * @param {Object} params - 申请参数
   * @param {string} EnterpriseID - 企业ID
   * @return {Promise<void>}
   */
  async createReceiveRecord(params, EnterpriseID) {
    const { ctx } = this;

    try {
      console.log('createReceiveRecord 被调用，参数:', JSON.stringify(params, null, 2));

      // 检查必要的参数
      if (!params.employeeInfo || !params.employeeInfo[0]) {
        console.error('员工信息检查失败:', {
          hasEmployeeInfo: !!params.employeeInfo,
          employeeInfoLength: params.employeeInfo ? params.employeeInfo.length : 0,
          employeeInfo: params.employeeInfo,
        });
        throw new Error('缺少员工信息');
      }
      if (!params.products || !params.products[0]) {
        console.error('产品信息检查失败:', {
          hasProducts: !!params.products,
          productsLength: params.products ? params.products.length : 0,
          products: params.products,
        });
        throw new Error('缺少产品信息');
      }

      const employee = params.employeeInfo[0];
      const product = params.products[0];

      console.log('员工信息:', employee);
      console.log('产品信息:', product);

      // 🔑 从产品库中获取分类信息（如果申请记录中缺少）
      let categoryInfo = {
        categoryId: product.categoryId,
        categoryName: product.categoryName,
        categoryPath: product.categoryPath,
      };

      // 如果申请记录中缺少分类信息，从产品库中查询
      if (!categoryInfo.categoryId && product.productIds && product.productIds[0]) {
        try {
          const productDetail = await ctx.model.ProtectiveProduct.findOne({
            _id: product.productIds[0],
            EnterpriseID,
          }).select('categoryId categoryName categoryPath');

          if (productDetail) {
            categoryInfo = {
              categoryId: productDetail.categoryId,
              categoryName: productDetail.categoryName,
              categoryPath: productDetail.categoryPath,
            };
            console.log('从产品库中获取分类信息:', categoryInfo);
          }
        } catch (error) {
          console.warn('获取产品分类信息失败:', error.message);
        }
      }

      const now = new Date();

      // 计算预警时间 - 默认7天后预警
      const warningDate = moment(now).add(7, 'days').toDate();

      // 生成唯一的planId
      const productIdForPlan = Array.isArray(product.productIds) ? product.productIds[0] :
        (product.productIds || product.productId || 'manual');
      const planId = `${employee._id}_${productIdForPlan}_${Date.now()}`;

      // 构建领用记录数据（适配新分类系统）
      const receiveRecordData = {
        EnterpriseID,
        // 员工信息
        employee: employee._id,

        // 岗位信息
        workspaces: params.workspaceId ? params.workspaceId[0] : null,
        workspacesName: params.workspace ? params.workspace[0] : null,
        workstation: params.workstationId ? params.workstationId[0] : null,
        workstationName: params.workstation ? params.workstation[0] : null,

        // 产品信息（新分类系统格式）
        products: [{
          _id: ctx.app.mongoose.Types.ObjectId(), // 生成新的领用记录产品ID
          product: product.product,
          productSpec: product.productSpec || '',
          modelNumber: product.modelNumber || '',
          materialCode: product.materialCode || '',
          number: product.number || 1,
          // 🔑 关键修复：将实际产品ID保存到productIds数组中
          productIds: (() => {
            console.log('处理productIds:', {
              原始productIds: product.productIds,
              是否为数组: Array.isArray(product.productIds),
              productId字段: product.productId,
              _id字段: product._id,
            });

            if (Array.isArray(product.productIds)) {
              const filtered = product.productIds.filter(id => id && id.trim());
              console.log('过滤后的productIds:', filtered);
              return filtered;
            } else if (product.productIds) {
              console.log('使用单个productIds:', product.productIds);
              return [ product.productIds ];
            } else if (product.productId) {
              console.log('使用productId字段:', product.productId);
              return [ product.productId ];
            }
            console.log('没有找到有效的产品ID，返回空数组');
            return [];

          })(),
          productType: [],
          // 分类信息
          categoryId: product.categoryId,
          categoryName: product.categoryName,
          categoryPath: product.categoryPath,
        }],

        // 🔑 添加根级别的分类信息（用于自动报废逻辑）
        categoryId: categoryInfo.categoryId,
        categoryName: categoryInfo.categoryName,
        categoryPath: categoryInfo.categoryPath,

        // 记录属性
        planId,
        recordSource: 1, // 1-主动申请
        receiveStartDate: now,
        warningDate,
        claimType: params.claimType || '',
        notes: params.notes || '',

        // 审批信息
        applicationId: params._id,
        auditStatus: 1, // 已审核通过
        isRejected: false,
      };

      // 添加厂房信息（如果存在）
      if (params.workshopId && params.workshopId[0] !== 0) {
        receiveRecordData.workshop = params.workshopId[0];
      }
      if (params.workshop && params.workshop[0] !== 0) {
        receiveRecordData.workshopName = params.workshop[0];
      }

      console.log('创建领用记录的数据:', JSON.stringify(receiveRecordData, null, 2));

      // 创建领用记录
      const receiveRecord = await ctx.service.db.create('ReceiveRecord', receiveRecordData, '审批通过创建领用记录');

      if (!receiveRecord) {
        console.error('创建领用记录失败: 返回结果为空');
        throw new Error('创建领用记录失败: 数据库操作返回空结果');
      }

      ctx.auditLog(
        '创建领用记录',
        `申请ID: ${params._id}, 员工: ${employee._id}, 产品: ${product.product}`,
        'info'
      );

      console.log('领用记录创建成功:', receiveRecord._id);
      return receiveRecord;

    } catch (error) {
      ctx.logger.error('创建领用记录失败', error);
      throw new Error(`创建领用记录失败: ${error.message}`);
    }
  }

  /**
   * @summary 处理非WH分支的审批逻辑
   * @description 保持原有的审批逻辑不变
   * @param {Object} params - 申请参数
   * @param {Object} operator - 操作人信息
   * @param {string} EnterpriseID - 企业ID
   * @return {Promise<string>} 处理结果
   */
  async handleNonWHApproval(params, operator, EnterpriseID) {
    const { ctx } = this;

    const query = params.reason ? params.reason.value : '';
    const auditStatus = params.status;
    const auditLevel = params.auditLevel || 1;

    const updateFields = {
      updateAt: new Date(),
      auditStatus,
      auditRecords: params.auditRecords || [],
    };

    // 确保auditRecords数组有足够的元素
    const targetIndex = auditLevel - 1;
    if (!updateFields.auditRecords[targetIndex]) {
      // 如果对应级别的审批记录不存在，创建一个
      updateFields.auditRecords[targetIndex] = {
        auditLevel,
        auditStatus: 0,
        needToAuditIds: [],
        operator: null,
        auditTime: null,
        reason: '',
      };
    }

    updateFields.auditRecords[targetIndex].auditStatus = 1;
    updateFields.auditRecords[targetIndex].auditTime = new Date();
    updateFields.auditRecords[targetIndex].operator = {
      _id: operator._id,
      name: operator.name,
    };
    updateFields.auditRecords[targetIndex].reason = query;

    const queryCondition = {
      EnterpriseID,
      _id: params._id,
    };

    const res = await ctx.service.db.updateOne(
      'ApplicationProduct',
      queryCondition,
      {
        $set: updateFields,
      },
      {},
      '非WH分支审批处理'
    );

    // 添加领用记录
    console.log('审核状态:', params.status, '是否需要创建领用记录:', params.status === 1);
    if (params.status === 1) {
      console.log('开始创建领用记录...');
      await this.createReceiveRecord(params, EnterpriseID);
      console.log('领用记录创建完成');
    }

    if (res.n !== 0) {
      return '审核通过！';
    }
    return '状态更新失败';
  }

  // 按照月度导出申请列表
  async exportByMonth(params) {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const { mouthNumber, yearNumber } = params;

    // 查询数据库
    const pipeline = [
      {
        $match: {
          EnterpriseID,
          mouthNumber,
          yearNumber,
          status: 1,
        },
      },
      {
        $project: {
          number: 1, // 产品数量
          protectionType: 1, // 防护类型
          scrapReason: 1, // 报废原因
          workspaces: 1, // 车间名称
          sign: 1, // 签字
          scrapTime: 1, // 报废时间
        },
      },
    ];
    const tableData = await ctx.service.db.aggregate('ScrapProduct', pipeline);

    if (tableData.length === 0) {
      return '暂无报废数据或数据未审核通过！';
    }

    for (let i = 0; i < tableData.length; i++) {
      const temp = tableData[i];
      console.log(temp);
      temp.index = i + 1;
      temp.scrapTime = moment(temp.scrapTime).format('YYYY-MM-DD HH:mm');
      if (temp.sign) {
        temp.sign = `${app.config.upload_path}/${temp.sign}`;
      }
    }
    const templateWordName = '特种劳动防护用品报废登记表';
    const word = await ctx.helper.fillWord(ctx, templateWordName, { tableData, projectSN: '111' });
    return word.path;
  }

  async findProducts() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const result = await ctx.model.ProtectiveSuppliesList.aggregate([
      { $match: { EnterpriseID } },
      { $unwind: '$list' },
      { $unwind: '$list.data' },
      {
        $project: {
          _id: '$list.data._id',
          product: '$list.data.product',
        },
      },
    ]);
    return result;
  }

}

module.exports = ApplicationProductService;
