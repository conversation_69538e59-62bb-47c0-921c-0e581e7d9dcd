const path = require('path');
const Cache = require('js-cache');
const { bootstrap } = require('global-agent');

class AppBootHook {

  constructor(app) {
    this.app = app;
  }

  async beforeStart() {
    this.app.runSchedule('onlineMonitoring');
    this.app.runSchedule('onlineMonitoringByDay');
    this.app.runSchedule('expertDataStatistics');
  }

  configWillLoad() {

    this.app.loader.loadFile(path.join(this.app.config.baseDir, 'app/bootstrap/index.js'));
    // const ctx = this.app.createAnonymousContext();
    // 如果出现问题，请用下面的原版
    // this.app.nunjucks.addExtension('remote', new remote(ctx));
    // this.app.nunjucks.addExtension('remote', new global.remote(ctx));
  }

  async didLoad() {
    // 文件加载完成
  }

  async willReady() {
    const _theApp = this.app;
    // 请将你的应用项目中 app.beforeStart 中的代码置于此处。
    console.log('Starting application initialization...');
    const db = this.app.mongoose.connection;
    console.log('Checking if time series collection exists...');
    try {
      const ctx = _theApp.createAnonymousContext();
      const needInitCollections = await db.db
        .listCollections({ name: { $in: [ 'monitorData', 'deviceMonitorFactors', 'deviceTypes' ] } })
        .toArray();
      // 检查monitorData是否存在
      const timeseriesCollections = needInitCollections.filter(
        collection => collection.name === 'monitorData');
      if (
        timeseriesCollections.length > 0
      ) {
        console.log('Time series collection already exists.');
      } else {
        await db.createCollection('monitorData', {
          timeseries: {
            timeField: 'timestamp',
            metaField: 'metadata',
            granularity: 'seconds',
          },
        });
      }
      const NeedInit = [ 'DeviceMonitorFactor', 'DeviceType' ];
      for (const collection of NeedInit) {
        const docLength = await ctx.model[collection].countDocuments();
        if (docLength === 0) {
          // 从dbinit 文件夹中读取数据
          const data = require(path.join(this.app.config.baseDir, '/app/dbInit', `${collection}.json`));
          await ctx.model[collection].insertMany(data);
          console.log('Collection %s created.', collection);
        } else {
          console.log('Collection %s already exists.', collection);
        }
      }
    } catch (error) {
      console.error('Error creating collection:', error);
    }
  }

  async didReady() {
    const _theApp = this.app;
    _theApp.cache = new Cache();

    _theApp.on('error', (err, ctx) => {
      ctx.auditLog('全局捕获的错误', `${err.stack} 。`, 'error');
    });

    _theApp.messenger.on('refreshCache', by => {
      // _theApp.logger.info('start update by %s', by);
      const ctx = _theApp.createAnonymousContext();
      ctx.runInBackground(async () => {
        const {
          key,
          value,
          time,
        } = by;
        _theApp.cache.set(key, value, time);
      });
    });

    const ctx = _theApp.createAnonymousContext();

    // 启动时加载代理设置，后续使用nodejs的http/https模块时会自动使用代理
    const proxy = process.env.GLOBAL_AGENT_HTTP_PROXY || process.env.GLOBAL_AGENT_HTTPS_PROXY;
    if (proxy) {
      bootstrap();
      context.auditLog(`global-agent is configured with proxy ${proxy}`);
    }
    if (!ctx.app.config.whDataOnline.disable) {
      await ctx.helper.delRedisPattern('whdata:device:*');
      ctx.auditLog('万华设备数据同步', '清理redis缓存成功', 'info');
    }
    const queues = ctx.app.config.rabbitMq.queues;
    if (queues && queues.length > 0) {
      for (const queue of queues) {
        try {
          await ctx.service.rabbitmq.consume({
            queueExist: queue.name,
            callback: async message => {
              console.log('接收到的队列信息=====>', message);
              try {
                await ctx.service[queue.service][queue.callback](JSON.parse(message));
              } catch (e) {
                console.log('队列信息处理失败', e);
              }
            },
            exchange: queue.exchange,
            routingKey: queue.routingKey,
          });
        } catch (e) {
          ctx.auditLog('RabbitMQ 初始化失败', `队列 ${queue.name} 初始化失败: ${e.message}`, 'error');
        }
      }
    }
    // 处理服务重启后的中断任务
    // 判断是否是fz分支
    if (_theApp.config.branch === 'fz') {
      console.log('fzDataFix is enabled, handling interrupted tasks...');
      await this.handleInterruptedTasks();
    }
  }

  /**
   * 处理服务重启后的中断任务
   */
  async handleInterruptedTasks() {
    try {
      console.log('检查并处理中断的任务...');

      // 获取所有任务键
      const taskKeys = await this.app.redis.keys('task:*');
      console.log(`找到 ${taskKeys.length} 个任务`);

      if (taskKeys.length === 0) {
        return;
      }

      // 批量获取任务数据
      const pipeline = this.app.redis.pipeline();
      taskKeys.forEach(key => {
        pipeline.hgetall(key);
      });

      const results = await pipeline.exec();
      let interruptedCount = 0;

      for (let i = 0; i < results.length; i++) {
        const [ err, taskData ] = results[i];
        if (err || !taskData || Object.keys(taskData).length === 0) {
          continue;
        }

        // 检查任务状态
        if (taskData.status === 'processing') {
          const taskKey = taskKeys[i];
          const taskId = taskKey.replace('task:', '');

          console.log(`发现中断任务: ${taskId}`);

          // 更新任务状态为中断
          await this.app.redis.hset(taskKey, {
            status: 'interrupted',
            message: '服务重启导致任务中断',
            updatedAt: new Date().toISOString(),
            interruptedAt: new Date().toISOString(),
          });

          interruptedCount++;
        }
      }

      if (interruptedCount > 0) {
        console.log(`已将 ${interruptedCount} 个处理中的任务标记为中断状态`);
      } else {
        console.log('没有发现需要处理的中断任务');
      }

    } catch (error) {
      console.error('处理中断任务时出错:', error);
    }
  }
}

module.exports = AppBootHook;
